Component: ARM Compiler 5.05 update 2 (build 169) Tool: armlink [4d0f33]

==============================================================================

Section Cross References

    startup_stm32f334x8.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f334x8.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f334x8.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f334x8.o(RESET) refers to startup_stm32f334x8.o(STACK) for __initial_sp
    startup_stm32f334x8.o(RESET) refers to startup_stm32f334x8.o(.text) for Reset_Handler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.EXTI9_5_IRQHandler) for EXTI9_5_IRQHandler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.TIM1_UP_TIM16_IRQHandler) for TIM1_UP_TIM16_IRQHandler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f334x8.o(RESET) refers to stm32f3xx_it.o(i.HRTIM1_TIMA_IRQHandler) for HRTIM1_TIMA_IRQHandler
    startup_stm32f334x8.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f334x8.o(.text) refers to system_stm32f30x.o(i.SystemInit) for SystemInit
    startup_stm32f334x8.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f334x8.o(.text) refers to startup_stm32f334x8.o(HEAP) for Heap_Mem
    startup_stm32f334x8.o(.text) refers to startup_stm32f334x8.o(STACK) for Stack_Mem
    stm32f3xx_it.o(i.EXTI9_5_IRQHandler) refers to stm32f30x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    stm32f3xx_it.o(i.EXTI9_5_IRQHandler) refers to qk_ir.o(i.IrDecode) for IrDecode
    stm32f3xx_it.o(i.EXTI9_5_IRQHandler) refers to stm32f30x_exti.o(i.EXTI_ClearFlag) for EXTI_ClearFlag
    stm32f3xx_it.o(i.HRTIM1_TIMA_IRQHandler) refers to stm32f30x_hrtim.o(i.HRTIM_GetITStatus) for HRTIM_GetITStatus
    stm32f3xx_it.o(i.HRTIM1_TIMA_IRQHandler) refers to task.o(i.TASK_Periodicity) for TASK_Periodicity
    stm32f3xx_it.o(i.HRTIM1_TIMA_IRQHandler) refers to stm32f30x_hrtim.o(i.HRTIM_ClearITPendingBit) for HRTIM_ClearITPendingBit
    stm32f3xx_it.o(i.TIM1_UP_TIM16_IRQHandler) refers to task.o(i.TASK_Periodicity) for TASK_Periodicity
    stm32f3xx_it.o(i.USART2_IRQHandler) refers to stm32f30x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    stm32f3xx_it.o(i.USART2_IRQHandler) refers to uart.o(i.UART_TxdIsr) for UART_TxdIsr
    stm32f3xx_it.o(i.USART2_IRQHandler) refers to stm32f30x_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    stm32f3xx_it.o(i.USART2_IRQHandler) refers to uart.o(i.UART_RxdIsr) for UART_RxdIsr
    system_stm32f30x.o(i.Delay_ms) refers to system_stm32f30x.o(.data) for .data
    system_stm32f30x.o(i.Delay_us) refers to system_stm32f30x.o(.data) for .data
    system_stm32f30x.o(i.SystemCoreClockUpdate) refers to qk_common.o(.data) for SystemCoreClock
    system_stm32f30x.o(i.SystemCoreClockUpdate) refers to system_stm32f30x.o(.data) for .data
    system_stm32f30x.o(i.TimingDelay_Decrement) refers to system_stm32f30x.o(.data) for .data
    spi.o(i.SPI_Configuration) refers to stm32f30x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    spi.o(i.SPI_Configuration) refers to stm32f30x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    spi.o(i.SPI_Configuration) refers to stm32f30x_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    spi.o(i.SPI_Configuration) refers to stm32f30x_gpio.o(i.GPIO_Init) for GPIO_Init
    spi.o(i.SPI_Configuration) refers to stm32f30x_spi.o(i.SPI_Init) for SPI_Init
    spi.o(i.SPI_Configuration) refers to stm32f30x_spi.o(i.SPI_Cmd) for SPI_Cmd
    font.o(.constdata) refers to font.o(.constdata) for ASCII_16X32
    font.o(.constdata) refers to font.o(.constdata) for ASCII_12X24
    task.o(i.TASK_Adc) refers to task.o(i.mean1) for mean1
    task.o(i.TASK_Adc) refers to task.o(i.mean2) for mean2
    task.o(i.TASK_Adc) refers to task.o(i.mean3) for mean3
    task.o(i.TASK_Adc) refers to task.o(i.TASK_PwmShutDown) for TASK_PwmShutDown
    task.o(i.TASK_Adc) refers to task.o(i.TASK_Send2Nums) for TASK_Send2Nums
    task.o(i.TASK_Adc) refers to task.o(.bss) for .bss
    task.o(i.TASK_Adc) refers to task.o(.data) for .data
    task.o(i.TASK_ClearWave) refers to qk_lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    task.o(i.TASK_ClearWave) refers to task.o(.data) for .data
    task.o(i.TASK_ClearWave) refers to task.o(.bss) for .bss
    task.o(i.TASK_Continuous) refers to qk_menu.o(i.InitParameter) for InitParameter
    task.o(i.TASK_Continuous) refers to qk_lcd.o(i.LCD_Clear) for LCD_Clear
    task.o(i.TASK_Continuous) refers to qk_lcd.o(i.LCD_PutStr) for LCD_PutStr
    task.o(i.TASK_Continuous) refers to qk_menu.o(i.SetParameter) for SetParameter
    task.o(i.TASK_DisplayWave) refers to qk_lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    task.o(i.TASK_DisplayWave) refers to task.o(.data) for .data
    task.o(i.TASK_DisplayWave) refers to task.o(.bss) for .bss
    task.o(i.TASK_GetTimeCnt) refers to task.o(.data) for .data
    task.o(i.TASK_Init) refers to task.o(i.TASK_UpdateVar) for TASK_UpdateVar
    task.o(i.TASK_Init) refers to task.o(i.TASK_InitPeripheral) for TASK_InitPeripheral
    task.o(i.TASK_InitPeripheral) refers to qk_common.o(i.CloclkInit) for CloclkInit
    task.o(i.TASK_InitPeripheral) refers to uart.o(i.UART_Init) for UART_Init
    task.o(i.TASK_InitPeripheral) refers to uart.o(i.UART_ClearRxdBuffer) for UART_ClearRxdBuffer
    task.o(i.TASK_InitPeripheral) refers to hal.o(i.HAL_WatchDogInit) for HAL_WatchDogInit
    task.o(i.TASK_InitPeripheral) refers to hal.o(i.HAL_LedInit) for HAL_LedInit
    task.o(i.TASK_InitPeripheral) refers to hal.o(i.HAL_PwmInit) for HAL_PwmInit
    task.o(i.TASK_Periodicity) refers to task.o(i.TASK_Pwm) for TASK_Pwm
    task.o(i.TASK_Periodicity) refers to task.o(i.TASK_Adc) for TASK_Adc
    task.o(i.TASK_Periodicity) refers to task.o(i.TASK_ScanKey) for TASK_ScanKey
    task.o(i.TASK_Periodicity) refers to task.o(.data) for .data
    task.o(i.TASK_Periodicity) refers to qk_ir.o(.data) for IR_LedTimeout
    task.o(i.TASK_Pwm) refers to llsdiv.o(.text) for __aeabi_ldivmod
    task.o(i.TASK_Pwm) refers to task.o(i.intSqrt) for intSqrt
    task.o(i.TASK_Pwm) refers to task.o(.data) for .data
    task.o(i.TASK_Pwm) refers to task.o(.bss) for .bss
    task.o(i.TASK_PwmShutDown) refers to stm32f30x_gpio.o(i.GPIO_Init) for GPIO_Init
    task.o(i.TASK_Run) refers to task.o(i.TASK_Init) for TASK_Init
    task.o(i.TASK_ScanKey) refers to qk_common.o(i.DelayHalfUs) for DelayHalfUs
    task.o(i.TASK_ScanKey) refers to task.o(.data) for .data
    task.o(i.TASK_Send2Nums) refers to uart.o(i.UART_SendData) for UART_SendData
    task.o(i.TASK_SendNum) refers to uart.o(i.UART_SendData) for UART_SendData
    task.o(i.TASK_Start) refers to stm32f30x_tim.o(i.TIM_Cmd) for TIM_Cmd
    task.o(i.TASK_Start) refers to task.o(i.TASK_Continuous) for TASK_Continuous
    task.o(i.TASK_UpdateVar) refers to task.o(.data) for .data
    task.o(i.TASK_UpdateVar) refers to task.o(.bss) for .bss
    task.o(i.mean1) refers to task.o(.bss) for .bss
    task.o(i.mean1) refers to task.o(.data) for .data
    task.o(i.mean2) refers to task.o(.bss) for .bss
    task.o(i.mean2) refers to task.o(.data) for .data
    task.o(i.mean3) refers to task.o(.bss) for .bss
    task.o(i.mean3) refers to task.o(.data) for .data
    stm32f30x_adc.o(i.ADC_DeInit) refers to stm32f30x_rcc.o(i.RCC_AHBPeriphResetCmd) for RCC_AHBPeriphResetCmd
    stm32f30x_dac.o(i.DAC_DeInit) refers to stm32f30x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f30x_gpio.o(i.GPIO_DeInit) refers to stm32f30x_rcc.o(i.RCC_AHBPeriphResetCmd) for RCC_AHBPeriphResetCmd
    stm32f30x_hrtim.o(i.HRTIM_DeInit) refers to stm32f30x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f30x_hrtim.o(i.HRTIM_EventConfig) refers to stm32f30x_hrtim.o(i.HRTIM_ExternalEventConfig) for HRTIM_ExternalEventConfig
    stm32f30x_hrtim.o(i.HRTIM_SimpleBaseStart) refers to stm32f30x_hrtim.o(.data) for .data
    stm32f30x_hrtim.o(i.HRTIM_SimpleBaseStop) refers to stm32f30x_hrtim.o(.data) for .data
    stm32f30x_hrtim.o(i.HRTIM_SimpleBase_Init) refers to stm32f30x_hrtim.o(i.HRTIM_TimingUnitBase_Config) for HRTIM_TimingUnitBase_Config
    stm32f30x_hrtim.o(i.HRTIM_SimpleBase_Init) refers to stm32f30x_hrtim.o(i.HRTIM_MasterBase_Config) for HRTIM_MasterBase_Config
    stm32f30x_hrtim.o(i.HRTIM_SimpleCaptureChannelConfig) refers to stm32f30x_hrtim.o(i.HRTIM_ExternalEventConfig) for HRTIM_ExternalEventConfig
    stm32f30x_hrtim.o(i.HRTIM_SimpleCaptureStart) refers to stm32f30x_hrtim.o(.data) for .data
    stm32f30x_hrtim.o(i.HRTIM_SimpleCaptureStop) refers to stm32f30x_hrtim.o(.data) for .data
    stm32f30x_hrtim.o(i.HRTIM_SimpleCapture_Init) refers to stm32f30x_hrtim.o(i.HRTIM_TimingUnitBase_Config) for HRTIM_TimingUnitBase_Config
    stm32f30x_hrtim.o(i.HRTIM_SimpleOCChannelConfig) refers to stm32f30x_hrtim.o(i.HRTIM_CompareUnitConfig) for HRTIM_CompareUnitConfig
    stm32f30x_hrtim.o(i.HRTIM_SimpleOCChannelConfig) refers to stm32f30x_hrtim.o(i.HRTIM_OutputConfig) for HRTIM_OutputConfig
    stm32f30x_hrtim.o(i.HRTIM_SimpleOCStart) refers to stm32f30x_hrtim.o(.data) for .data
    stm32f30x_hrtim.o(i.HRTIM_SimpleOCStop) refers to stm32f30x_hrtim.o(.data) for .data
    stm32f30x_hrtim.o(i.HRTIM_SimpleOC_Init) refers to stm32f30x_hrtim.o(i.HRTIM_TimingUnitBase_Config) for HRTIM_TimingUnitBase_Config
    stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulseChannelConfig) refers to stm32f30x_hrtim.o(i.HRTIM_CompareUnitConfig) for HRTIM_CompareUnitConfig
    stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulseChannelConfig) refers to stm32f30x_hrtim.o(i.HRTIM_OutputConfig) for HRTIM_OutputConfig
    stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulseChannelConfig) refers to stm32f30x_hrtim.o(i.HRTIM_ExternalEventConfig) for HRTIM_ExternalEventConfig
    stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulseChannelConfig) refers to stm32f30x_hrtim.o(i.HRTIM_TIM_ResetConfig) for HRTIM_TIM_ResetConfig
    stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulseStart) refers to stm32f30x_hrtim.o(.data) for .data
    stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulseStop) refers to stm32f30x_hrtim.o(.data) for .data
    stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulse_Init) refers to stm32f30x_hrtim.o(i.HRTIM_TimingUnitBase_Config) for HRTIM_TimingUnitBase_Config
    stm32f30x_hrtim.o(i.HRTIM_SimplePWMChannelConfig) refers to stm32f30x_hrtim.o(i.HRTIM_CompareUnitConfig) for HRTIM_CompareUnitConfig
    stm32f30x_hrtim.o(i.HRTIM_SimplePWMChannelConfig) refers to stm32f30x_hrtim.o(i.HRTIM_OutputConfig) for HRTIM_OutputConfig
    stm32f30x_hrtim.o(i.HRTIM_SimplePWMStart) refers to stm32f30x_hrtim.o(.data) for .data
    stm32f30x_hrtim.o(i.HRTIM_SimplePWMStop) refers to stm32f30x_hrtim.o(.data) for .data
    stm32f30x_hrtim.o(i.HRTIM_SimplePWM_Init) refers to stm32f30x_hrtim.o(i.HRTIM_TimingUnitBase_Config) for HRTIM_TimingUnitBase_Config
    stm32f30x_hrtim.o(i.HRTIM_WaveformOutputConfig) refers to stm32f30x_hrtim.o(i.HRTIM_OutputConfig) for HRTIM_OutputConfig
    stm32f30x_hrtim.o(i.HRTIM_Waveform_Init) refers to stm32f30x_hrtim.o(i.HRTIM_TimingUnitBase_Config) for HRTIM_TimingUnitBase_Config
    stm32f30x_hrtim.o(i.HRTIM_Waveform_Init) refers to stm32f30x_hrtim.o(i.HRTIM_TimingUnitWaveform_Config) for HRTIM_TimingUnitWaveform_Config
    stm32f30x_hrtim.o(i.HRTIM_Waveform_Init) refers to stm32f30x_hrtim.o(i.HRTIM_MasterBase_Config) for HRTIM_MasterBase_Config
    stm32f30x_pwr.o(i.PWR_DeInit) refers to stm32f30x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f30x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f30x_rcc.o(.data) for .data
    stm32f30x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f30x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f30x_spi.o(i.I2S_Init) refers to stm32f30x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f30x_spi.o(i.SPI_I2S_DeInit) refers to stm32f30x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f30x_spi.o(i.SPI_I2S_DeInit) refers to stm32f30x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f30x_tim.o(i.TIM_DeInit) refers to stm32f30x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f30x_tim.o(i.TIM_DeInit) refers to stm32f30x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f30x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f30x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f30x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f30x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f30x_tim.o(i.TIM_ICInit) refers to stm32f30x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f30x_tim.o(i.TIM_ICInit) refers to stm32f30x_tim.o(i.TI1_Config) for TI1_Config
    stm32f30x_tim.o(i.TIM_ICInit) refers to stm32f30x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f30x_tim.o(i.TIM_ICInit) refers to stm32f30x_tim.o(i.TI2_Config) for TI2_Config
    stm32f30x_tim.o(i.TIM_ICInit) refers to stm32f30x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f30x_tim.o(i.TIM_ICInit) refers to stm32f30x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f30x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f30x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f30x_tim.o(i.TIM_PWMIConfig) refers to stm32f30x_tim.o(i.TI2_Config) for TI2_Config
    stm32f30x_tim.o(i.TIM_PWMIConfig) refers to stm32f30x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f30x_tim.o(i.TIM_PWMIConfig) refers to stm32f30x_tim.o(i.TI1_Config) for TI1_Config
    stm32f30x_tim.o(i.TIM_PWMIConfig) refers to stm32f30x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f30x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f30x_tim.o(i.TI1_Config) for TI1_Config
    stm32f30x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f30x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f30x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f30x_tim.o(i.TI2_Config) for TI2_Config
    stm32f30x_usart.o(i.USART_DeInit) refers to stm32f30x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f30x_usart.o(i.USART_DeInit) refers to stm32f30x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f30x_usart.o(i.USART_Init) refers to stm32f30x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f30x_wwdg.o(i.WWDG_DeInit) refers to stm32f30x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    uart.o(i.UART_ClearRxdBuffer) refers to uart.o(.data) for .data
    uart.o(i.UART_ClearRxdBuffer) refers to uart.o(.bss) for .bss
    uart.o(i.UART_GetTxdFifoDataEmptyLen) refers to uart.o(.data) for .data
    uart.o(i.UART_GetTxdFifoDataLen) refers to uart.o(.data) for .data
    uart.o(i.UART_Init) refers to stm32f30x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    uart.o(i.UART_Init) refers to stm32f30x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    uart.o(i.UART_Init) refers to stm32f30x_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    uart.o(i.UART_Init) refers to stm32f30x_gpio.o(i.GPIO_Init) for GPIO_Init
    uart.o(i.UART_Init) refers to stm32f30x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    uart.o(i.UART_Init) refers to stm32f30x_usart.o(i.USART_Init) for USART_Init
    uart.o(i.UART_Init) refers to stm32f30x_usart.o(i.USART_Cmd) for USART_Cmd
    uart.o(i.UART_Init) refers to stm32f30x_usart.o(i.USART_ITConfig) for USART_ITConfig
    uart.o(i.UART_Init) refers to stm32f30x_misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    uart.o(i.UART_Init) refers to stm32f30x_misc.o(i.NVIC_Init) for NVIC_Init
    uart.o(i.UART_Init) refers to uart.o(.data) for .data
    uart.o(i.UART_RxdIsr) refers to task.o(i.TASK_GetTimeCnt) for TASK_GetTimeCnt
    uart.o(i.UART_RxdIsr) refers to uart.o(.data) for .data
    uart.o(i.UART_RxdIsr) refers to uart.o(.bss) for .bss
    uart.o(i.UART_SendCmd) refers to uart.o(i.UART_CalcCheck) for UART_CalcCheck
    uart.o(i.UART_SendCmd) refers to uart.o(i.UART_TxdIsr) for UART_TxdIsr
    uart.o(i.UART_SendCmd) refers to uart.o(.data) for .data
    uart.o(i.UART_SendCmd) refers to uart.o(.bss) for .bss
    uart.o(i.UART_SendData) refers to uart.o(i.UART_TxdIsr) for UART_TxdIsr
    uart.o(i.UART_SendData) refers to uart.o(.data) for .data
    uart.o(i.UART_SendData) refers to uart.o(.bss) for .bss
    uart.o(i.UART_SendStr) refers to uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.UART_TxdIsr) refers to uart.o(.data) for .data
    uart.o(i.UART_TxdIsr) refers to uart.o(.bss) for .bss
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_DeInit) for RCC_DeInit
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_HSEConfig) for RCC_HSEConfig
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_PLLCmd) for RCC_PLLCmd
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_HCLKConfig) for RCC_HCLKConfig
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_PCLK2Config) for RCC_PCLK2Config
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_PCLK1Config) for RCC_PCLK1Config
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_PREDIV1Config) for RCC_PREDIV1Config
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_PLLConfig) for RCC_PLLConfig
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_SYSCLKConfig) for RCC_SYSCLKConfig
    qk_common.o(i.CloclkInit) refers to stm32f30x_rcc.o(i.RCC_GetSYSCLKSource) for RCC_GetSYSCLKSource
    qk_common.o(i.CloclkInit) refers to qk_common.o(i.DelayInit) for DelayInit
    qk_common.o(i.CloclkInit) refers to qk_common.o(.data) for .data
    qk_common.o(i.DelayInit) refers to qk_common.o(.data) for .data
    qk_common.o(i.DelayMs) refers to stm32f30x_iwdg.o(i.IWDG_ReloadCounter) for IWDG_ReloadCounter
    qk_common.o(i.DelayMs) refers to qk_common.o(.data) for .data
    qk_common.o(i.DelayUs) refers to qk_common.o(.data) for .data
    qk_lcd.o(i.LCD_BackLightInit) refers to stm32f30x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    qk_lcd.o(i.LCD_BackLightInit) refers to stm32f30x_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    qk_lcd.o(i.LCD_BackLightInit) refers to stm32f30x_gpio.o(i.GPIO_Init) for GPIO_Init
    qk_lcd.o(i.LCD_BackLightInit) refers to stm32f30x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    qk_lcd.o(i.LCD_BackLightInit) refers to stm32f30x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    qk_lcd.o(i.LCD_BackLightInit) refers to qk_lcd.o(i.LCD_SetBright) for LCD_SetBright
    qk_lcd.o(i.LCD_Clear) refers to qk_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    qk_lcd.o(i.LCD_Clear) refers to qk_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    qk_lcd.o(i.LCD_Clear) refers to qk_lcd.o(i.LCD_WriteConst) for LCD_WriteConst
    qk_lcd.o(i.LCD_DrawBmp) refers to qk_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    qk_lcd.o(i.LCD_DrawBmp) refers to qk_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    qk_lcd.o(i.LCD_DrawBmp) refers to qk_lcd.o(i.LCD_WriteArray) for LCD_WriteArray
    qk_lcd.o(i.LCD_DrawLine) refers to qk_lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    qk_lcd.o(i.LCD_DrawNum) refers to qk_lcd.o(i.LCD_PutChar) for LCD_PutChar
    qk_lcd.o(i.LCD_DrawNum) refers to qk_lcd.o(.data) for .data
    qk_lcd.o(i.LCD_DrawPic) refers to qk_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    qk_lcd.o(i.LCD_DrawPic) refers to qk_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    qk_lcd.o(i.LCD_DrawPoint) refers to qk_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    qk_lcd.o(i.LCD_DrawPoint) refers to qk_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    qk_lcd.o(i.LCD_Init) refers to qk_lcd.o(i.LCD_InitGPIO) for LCD_InitGPIO
    qk_lcd.o(i.LCD_Init) refers to qk_common.o(i.DelayMs) for DelayMs
    qk_lcd.o(i.LCD_Init) refers to qk_lcd.o(i.LCD_SelectFont) for LCD_SelectFont
    qk_lcd.o(i.LCD_Init) refers to font.o(.constdata) for FONT_32
    qk_lcd.o(i.LCD_InitGPIO) refers to stm32f30x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    qk_lcd.o(i.LCD_InitGPIO) refers to stm32f30x_gpio.o(i.GPIO_Init) for GPIO_Init
    qk_lcd.o(i.LCD_PutChar) refers to qk_lcd.o(i.Search_Font) for Search_Font
    qk_lcd.o(i.LCD_PutChar) refers to qk_lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    qk_lcd.o(i.LCD_PutChar) refers to qk_lcd.o(.data) for .data
    qk_lcd.o(i.LCD_PutHanzi) refers to qk_lcd.o(i.Search_Font) for Search_Font
    qk_lcd.o(i.LCD_PutHanzi) refers to qk_lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    qk_lcd.o(i.LCD_PutHanzi) refers to qk_lcd.o(.data) for .data
    qk_lcd.o(i.LCD_PutStr) refers to qk_lcd.o(i.LCD_PutHanzi) for LCD_PutHanzi
    qk_lcd.o(i.LCD_PutStr) refers to qk_lcd.o(i.LCD_PutChar) for LCD_PutChar
    qk_lcd.o(i.LCD_PutStr) refers to qk_lcd.o(.data) for .data
    qk_lcd.o(i.LCD_SelectFont) refers to qk_lcd.o(.data) for .data
    qk_lcd.o(i.LCD_SetBar) refers to qk_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    qk_lcd.o(i.LCD_SetBar) refers to qk_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    qk_lcd.o(i.LCD_SetBar) refers to qk_lcd.o(i.LCD_WriteConst) for LCD_WriteConst
    qk_lcd.o(i.LCD_SetCursor) refers to qk_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    qk_lcd.o(i.Search_Font) refers to qk_lcd.o(.data) for .data
    qk_ir.o(i.IrDecode) refers to qk_ir.o(.data) for .data
    qk_ir.o(i.IrInit) refers to stm32f30x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    qk_ir.o(i.IrInit) refers to stm32f30x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    qk_ir.o(i.IrInit) refers to stm32f30x_gpio.o(i.GPIO_Init) for GPIO_Init
    qk_ir.o(i.IrInit) refers to stm32f30x_syscfg.o(i.SYSCFG_EXTILineConfig) for SYSCFG_EXTILineConfig
    qk_ir.o(i.IrInit) refers to stm32f30x_exti.o(i.EXTI_Init) for EXTI_Init
    qk_ir.o(i.IrInit) refers to stm32f30x_misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    qk_ir.o(i.IrInit) refers to stm32f30x_misc.o(i.NVIC_Init) for NVIC_Init
    qk_ir.o(i.IrInit) refers to stm32f30x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    qk_ir.o(i.IrInit) refers to stm32f30x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    qk_ir.o(i.IrInit) refers to stm32f30x_tim.o(i.TIM_Cmd) for TIM_Cmd
    qk_ir.o(i.IrInit) refers to qk_common.o(.data) for SystemCoreClock
    qk_ir.o(i.IrInit) refers to qk_ir.o(.data) for .data
    qk_menu.o(i.FlashWriteData) refers to stm32f30x_flash.o(i.FLASH_ProgramWord) for FLASH_ProgramWord
    qk_menu.o(i.FlashWriteEnd) refers to stm32f30x_flash.o(i.FLASH_ClearFlag) for FLASH_ClearFlag
    qk_menu.o(i.FlashWriteEnd) refers to stm32f30x_flash.o(i.FLASH_Lock) for FLASH_Lock
    qk_menu.o(i.FlashWriteStart) refers to stm32f30x_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    qk_menu.o(i.FlashWriteStart) refers to stm32f30x_flash.o(i.FLASH_ClearFlag) for FLASH_ClearFlag
    qk_menu.o(i.FlashWriteStart) refers to stm32f30x_flash.o(i.FLASH_ErasePage) for FLASH_ErasePage
    qk_menu.o(i.GetKeyValue) refers to qk_ir.o(.data) for IR_Key
    qk_menu.o(i.InitFun) refers to qk_menu.o(i.changeFun_PwmFreq) for changeFun_PwmFreq
    qk_menu.o(i.InitFun) refers to qk_menu.o(i.changeFun_LcdBkLight) for changeFun_LcdBkLight
    qk_menu.o(i.InitFun) refers to qk_menu.o(i.changeFun_RunState) for changeFun_RunState
    qk_menu.o(i.InitFun) refers to qk_menu.o(i.changeFun_PwmDutyLen) for changeFun_PwmDutyLen
    qk_menu.o(i.InitFun) refers to qk_menu.o(i.changeFun_PwmPhase) for changeFun_PwmPhase
    qk_menu.o(i.InitFun) refers to qk_menu.o(i.changeFun_DacSetValue0) for changeFun_DacSetValue0
    qk_menu.o(i.InitFun) refers to qk_menu.o(i.changeFun_DacSetValue1) for changeFun_DacSetValue1
    qk_menu.o(i.InitFun) refers to task.o(.data) for RunState
    qk_menu.o(i.InitParameter) refers to qk_menu.o(i.LoadData) for LoadData
    qk_menu.o(i.InitParameter) refers to qk_menu.o(i.InitFun) for InitFun
    qk_menu.o(i.LoadData) refers to task.o(.data) for OutputAcFlag
    qk_menu.o(i.LoadData) refers to task.o(.bss) for Duty
    qk_menu.o(i.LoadData) refers to task.o(.data) for PwmFreq
    qk_menu.o(i.LoadData) refers to task.o(.data) for LcdBkLight
    qk_menu.o(i.MenuDisplayNumber) refers to qk_lcd.o(i.LCD_PutChar) for LCD_PutChar
    qk_menu.o(i.MenuDisplayNumber) refers to qk_lcd.o(.data) for Font_ASCII_WIDTH
    qk_menu.o(i.SetParameter) refers to qk_lcd.o(i.LCD_SetBar) for LCD_SetBar
    qk_menu.o(i.SetParameter) refers to qk_lcd.o(i.LCD_PutStr) for LCD_PutStr
    qk_menu.o(i.SetParameter) refers to qk_menu.o(i.MenuStrLen) for MenuStrLen
    qk_menu.o(i.SetParameter) refers to qk_menu.o(.bss) for .bss
    qk_menu.o(i.SetParameter) refers to qk_menu.o(.constdata) for .constdata
    qk_menu.o(i.SetParameter) refers to qk_lcd.o(.data) for Font_ASCII_WIDTH
    qk_menu.o(i.SetParameter) refers to qk_menu.o(.data) for .data
    qk_menu.o(i.SetParameter) refers to qk_menu.o(i.MenuDisplayNumber) for MenuDisplayNumber
    qk_menu.o(i.SetParameter) refers to qk_menu.o(i.GetKeyValue) for GetKeyValue
    qk_menu.o(i.SetParameter) refers to task.o(i.TASK_GetTimeCnt) for TASK_GetTimeCnt
    qk_menu.o(i.SetParameter) refers to task.o(.data) for TaskTimeNextCnt
    qk_menu.o(i.SetParameter) refers to stm32f30x_iwdg.o(i.IWDG_ReloadCounter) for IWDG_ReloadCounter
    qk_menu.o(i.SetParameter) refers to qk_menu.o(i.UpdatePeriod) for UpdatePeriod
    qk_menu.o(i.UpdatePeriod) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    qk_menu.o(i.UpdatePeriod) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    qk_menu.o(i.UpdatePeriod) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    qk_menu.o(i.UpdatePeriod) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    qk_menu.o(i.UpdatePeriod) refers to task.o(.data) for OutputAcFlag
    qk_menu.o(i.UpdatePeriod) refers to task.o(.data) for Current1CaliK
    qk_menu.o(i.UpdatePeriod) refers to task.o(.data) for Current1
    qk_menu.o(i.UpdatePeriod) refers to task.o(.data) for Volt1CaliK
    qk_menu.o(i.UpdatePeriod) refers to task.o(.data) for Volt1
    qk_menu.o(i.UpdatePeriod) refers to task.o(.data) for Volt2
    qk_menu.o(i.UpdatePeriod) refers to task.o(.data) for Power1
    qk_menu.o(i.UpdatePeriod) refers to task.o(.bss) for AdcValueRmse
    qk_menu.o(i.UpdatePeriod) refers to task.o(.data) for Current1CaliB
    qk_menu.o(i.changeFun_DacSetValue0) refers to task.o(.data) for DacSetValue
    qk_menu.o(i.changeFun_DacSetValue1) refers to task.o(.data) for DacSetValue
    qk_menu.o(i.changeFun_LcdBkLight) refers to qk_lcd.o(i.LCD_SetBright) for LCD_SetBright
    qk_menu.o(i.changeFun_LcdBkLight) refers to task.o(.data) for LcdBkLight
    qk_menu.o(i.changeFun_PwmDead) refers to task.o(.data) for PwmDead
    qk_menu.o(i.changeFun_PwmDutyLen) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    qk_menu.o(i.changeFun_PwmDutyLen) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    qk_menu.o(i.changeFun_PwmDutyLen) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    qk_menu.o(i.changeFun_PwmDutyLen) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    qk_menu.o(i.changeFun_PwmDutyLen) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    qk_menu.o(i.changeFun_PwmDutyLen) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    qk_menu.o(i.changeFun_PwmDutyLen) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    qk_menu.o(i.changeFun_PwmDutyLen) refers to qk_menu.o(i.changeFun_PwmPhase) for changeFun_PwmPhase
    qk_menu.o(i.changeFun_PwmDutyLen) refers to task.o(.data) for PwmDutyLen
    qk_menu.o(i.changeFun_PwmDutyLen) refers to task.o(.bss) for PwmTable
    qk_menu.o(i.changeFun_PwmFreq) refers to qk_menu.o(i.changeFun_LcdBkLight) for changeFun_LcdBkLight
    qk_menu.o(i.changeFun_PwmFreq) refers to qk_common.o(.data) for SystemCoreClock
    qk_menu.o(i.changeFun_PwmFreq) refers to task.o(.data) for PwmFreq
    qk_menu.o(i.changeFun_PwmFreq) refers to task.o(.data) for TaskMiliSecCnt
    qk_menu.o(i.changeFun_PwmPhase) refers to task.o(.data) for PwmPhase
    qk_menu.o(i.changeFun_PwmPhase) refers to task.o(.data) for PwmDutyLen
    qk_menu.o(i.changeFun_RunState) refers to stm32f30x_gpio.o(i.GPIO_Init) for GPIO_Init
    qk_menu.o(i.changeFun_RunState) refers to stm32f30x_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    qk_menu.o(i.changeFun_RunState) refers to task.o(.data) for RunState
    qk_menu.o(.constdata) refers to task.o(.data) for RunState
    qk_menu.o(.constdata) refers to qk_menu.o(i.changeFun_RunState) for changeFun_RunState
    qk_menu.o(.constdata) refers to task.o(.bss) for Duty
    qk_menu.o(.constdata) refers to qk_menu.o(i.changeFun_Duty) for changeFun_Duty
    qk_menu.o(.constdata) refers to task.o(.data) for PwmPhase
    qk_menu.o(.constdata) refers to qk_menu.o(i.changeFun_PwmPhase) for changeFun_PwmPhase
    qk_menu.o(.constdata) refers to qk_menu.o(i.changeFun_PwmDutyLen) for changeFun_PwmDutyLen
    qk_menu.o(.constdata) refers to task.o(.data) for PwmFreq
    qk_menu.o(.constdata) refers to qk_menu.o(i.changeFun_PwmFreq) for changeFun_PwmFreq
    qk_menu.o(.constdata) refers to qk_menu.o(i.changeFun_PwmDead) for changeFun_PwmDead
    qk_menu.o(.constdata) refers to task.o(.data) for LcdBkLight
    qk_menu.o(.constdata) refers to qk_menu.o(i.changeFun_LcdBkLight) for changeFun_LcdBkLight
    stm32f30x_flash.o(i.FLASH_EraseAllPages) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_ErasePage) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_OB_BOOTConfig) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_OB_EnableWRP) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_OB_Erase) refers to stm32f30x_flash.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f30x_flash.o(i.FLASH_OB_Erase) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_OB_SRAMParityConfig) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_OB_UserConfig) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_OB_VDDAConfig) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_OB_WriteUser) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_ProgramWord) refers to stm32f30x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f30x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f30x_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    hal.o(i.HAL_I2cReadReg) refers to hal.o(i.I2C_Start) for I2C_Start
    hal.o(i.HAL_I2cReadReg) refers to hal.o(i.I2C_SendByte) for I2C_SendByte
    hal.o(i.HAL_I2cReadReg) refers to hal.o(i.I2C_ReceiveByte) for I2C_ReceiveByte
    hal.o(i.HAL_I2cReadReg) refers to hal.o(i.I2C_SendAck) for I2C_SendAck
    hal.o(i.HAL_I2cReadReg) refers to hal.o(i.I2C_Stop) for I2C_Stop
    hal.o(i.HAL_I2cWriteReg) refers to hal.o(i.I2C_Start) for I2C_Start
    hal.o(i.HAL_I2cWriteReg) refers to hal.o(i.I2C_SendByte) for I2C_SendByte
    hal.o(i.HAL_I2cWriteReg) refers to hal.o(i.I2C_Stop) for I2C_Stop
    hal.o(i.HAL_LedInit) refers to stm32f30x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    hal.o(i.HAL_LedInit) refers to stm32f30x_gpio.o(i.GPIO_Init) for GPIO_Init
    hal.o(i.HAL_PwmInit) refers to stm32f30x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    hal.o(i.HAL_PwmInit) refers to stm32f30x_gpio.o(i.GPIO_Init) for GPIO_Init
    hal.o(i.HAL_PwmInit) refers to stm32f30x_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    hal.o(i.HAL_PwmInit) refers to stm32f30x_rcc.o(i.RCC_HRTIM1CLKConfig) for RCC_HRTIM1CLKConfig
    hal.o(i.HAL_PwmInit) refers to stm32f30x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    hal.o(i.HAL_PwmInit) refers to stm32f30x_hrtim.o(i.HRTIM_DLLCalibrationStart) for HRTIM_DLLCalibrationStart
    hal.o(i.HAL_PwmInit) refers to stm32f30x_hrtim.o(i.HRTIM_GetCommonFlagStatus) for HRTIM_GetCommonFlagStatus
    hal.o(i.HAL_PwmInit) refers to stm32f30x_hrtim.o(i.HRTIM_Waveform_Init) for HRTIM_Waveform_Init
    hal.o(i.HAL_PwmInit) refers to stm32f30x_hrtim.o(i.HRTIM_WaveformTimerConfig) for HRTIM_WaveformTimerConfig
    hal.o(i.HAL_PwmInit) refers to stm32f30x_hrtim.o(i.HRTIM_WaveformOutputConfig) for HRTIM_WaveformOutputConfig
    hal.o(i.HAL_PwmInit) refers to stm32f30x_hrtim.o(i.HRTIM_WaveformCompareConfig) for HRTIM_WaveformCompareConfig
    hal.o(i.HAL_PwmInit) refers to stm32f30x_hrtim.o(i.HRTIM_DeadTimeConfig) for HRTIM_DeadTimeConfig
    hal.o(i.HAL_PwmInit) refers to stm32f30x_misc.o(i.NVIC_Init) for NVIC_Init
    hal.o(i.HAL_PwmInit) refers to stm32f30x_hrtim.o(i.HRTIM_ITConfig) for HRTIM_ITConfig
    hal.o(i.HAL_PwmInit) refers to stm32f30x_hrtim.o(i.HRTIM_WaveformCounterStart) for HRTIM_WaveformCounterStart
    hal.o(i.HAL_PwmInit) refers to stm32f30x_hrtim.o(i.HRTIM_WaveformOutputStart) for HRTIM_WaveformOutputStart
    hal.o(i.HAL_PwmInit) refers to qk_common.o(.data) for SystemCoreClock
    hal.o(i.HAL_TouchCal) refers to qk_lcd.o(i.LCD_Clear) for LCD_Clear
    hal.o(i.HAL_TouchCal) refers to qk_lcd.o(i.LCD_PutStr) for LCD_PutStr
    hal.o(i.HAL_TouchCal) refers to qk_lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    hal.o(i.HAL_TouchCal) refers to qk_common.o(i.DelayMs) for DelayMs
    hal.o(i.HAL_TouchCal) refers to hal.o(i.HAL_TouchScan) for HAL_TouchScan
    hal.o(i.HAL_TouchCal) refers to qk_lcd.o(i.LCD_SetBar) for LCD_SetBar
    hal.o(i.HAL_TouchCal) refers to qk_lcd.o(i.LCD_DrawNum) for LCD_DrawNum
    hal.o(i.HAL_TouchCal) refers to hal.o(.data) for .data
    hal.o(i.HAL_TouchCal) refers to hal.o(.bss) for .bss
    hal.o(i.HAL_TouchDispPos) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hal.o(i.HAL_TouchDispPos) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    hal.o(i.HAL_TouchDispPos) refers to _printf_dec.o(.text) for _printf_int_dec
    hal.o(i.HAL_TouchDispPos) refers to qk_lcd.o(i.LCD_Clear) for LCD_Clear
    hal.o(i.HAL_TouchDispPos) refers to qk_lcd.o(i.LCD_PutStr) for LCD_PutStr
    hal.o(i.HAL_TouchDispPos) refers to noretval__2sprintf.o(.text) for __2sprintf
    hal.o(i.HAL_TouchDispPos) refers to hal.o(i.HAL_TouchScan) for HAL_TouchScan
    hal.o(i.HAL_TouchDispPos) refers to qk_lcd.o(i.num2string) for num2string
    hal.o(i.HAL_TouchDispPos) refers to qk_common.o(i.DelayMs) for DelayMs
    hal.o(i.HAL_TouchDispPos) refers to hal.o(.bss) for .bss
    hal.o(i.HAL_TouchDrawLine) refers to qk_lcd.o(i.LCD_Clear) for LCD_Clear
    hal.o(i.HAL_TouchDrawLine) refers to hal.o(i.HAL_TouchScan) for HAL_TouchScan
    hal.o(i.HAL_TouchDrawLine) refers to qk_lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    hal.o(i.HAL_TouchDrawLine) refers to qk_common.o(i.DelayMs) for DelayMs
    hal.o(i.HAL_TouchDrawLine) refers to hal.o(.bss) for .bss
    hal.o(i.HAL_TouchInit) refers to stm32f30x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    hal.o(i.HAL_TouchInit) refers to stm32f30x_gpio.o(i.GPIO_Init) for GPIO_Init
    hal.o(i.HAL_TouchInit) refers to spi.o(i.SPI_Configuration) for SPI_Configuration
    hal.o(i.HAL_TouchInit) refers to hal.o(.data) for .data
    hal.o(i.HAL_TouchInit) refers to hal.o(.bss) for .bss
    hal.o(i.HAL_TouchResGetAd) refers to spi.o(i.SPI_ReadWriteByte) for SPI_ReadWriteByte
    hal.o(i.HAL_TouchScan) refers to hal.o(i.HAL_TouchResGetAd) for HAL_TouchResGetAd
    hal.o(i.HAL_TouchScan) refers to hal.o(.bss) for .bss
    hal.o(i.HAL_TouchScan) refers to hal.o(.data) for .data
    hal.o(i.HAL_WatchDogInit) refers to stm32f30x_iwdg.o(i.IWDG_WriteAccessCmd) for IWDG_WriteAccessCmd
    hal.o(i.HAL_WatchDogInit) refers to stm32f30x_iwdg.o(i.IWDG_SetPrescaler) for IWDG_SetPrescaler
    hal.o(i.HAL_WatchDogInit) refers to stm32f30x_iwdg.o(i.IWDG_SetReload) for IWDG_SetReload
    hal.o(i.HAL_WatchDogInit) refers to stm32f30x_iwdg.o(i.IWDG_ReloadCounter) for IWDG_ReloadCounter
    hal.o(i.HAL_WatchDogInit) refers to stm32f30x_iwdg.o(i.IWDG_Enable) for IWDG_Enable
    hal.o(i.I2C_ByteWrite) refers to hal.o(i.I2C_Start) for I2C_Start
    hal.o(i.I2C_ByteWrite) refers to hal.o(i.I2C_SendByte) for I2C_SendByte
    hal.o(i.I2C_ByteWrite) refers to hal.o(i.I2C_Stop) for I2C_Stop
    hal.o(i.I2C_ReceiveByte) refers to qk_common.o(i.DelayUs) for DelayUs
    hal.o(i.I2C_SendAck) refers to qk_common.o(i.DelayUs) for DelayUs
    hal.o(i.I2C_SendByte) refers to qk_common.o(i.DelayUs) for DelayUs
    hal.o(i.I2C_Start) refers to qk_common.o(i.DelayUs) for DelayUs
    hal.o(i.I2C_Stop) refers to qk_common.o(i.DelayUs) for DelayUs
    hal.o(i.I2C_Wait) refers to qk_common.o(i.DelayUs) for DelayUs
    llsdiv.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    sin.o(i.__hardfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f334x8.o(.text) for __user_initial_stackheap
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing stm32f3xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f3xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f30x.o(.rev16_text), (4 bytes).
    Removing system_stm32f30x.o(.revsh_text), (4 bytes).
    Removing system_stm32f30x.o(i.Delay_ms), (20 bytes).
    Removing system_stm32f30x.o(i.Delay_us), (20 bytes).
    Removing system_stm32f30x.o(i.SystemCoreClockUpdate), (112 bytes).
    Removing system_stm32f30x.o(i.TimingDelay_Decrement), (20 bytes).
    Removing system_stm32f30x.o(.data), (20 bytes).
    Removing system_stm32f30x.o(.data), (1 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(i.SPI_Configuration), (240 bytes).
    Removing spi.o(i.SPI_ReadWriteByte), (28 bytes).
    Removing font.o(.rev16_text), (4 bytes).
    Removing font.o(.revsh_text), (4 bytes).
    Removing font.o(.constdata), (6144 bytes).
    Removing font.o(.constdata), (8192 bytes).
    Removing font.o(.constdata), (12 bytes).
    Removing font.o(.constdata), (12 bytes).
    Removing task.o(.rev16_text), (4 bytes).
    Removing task.o(.revsh_text), (4 bytes).
    Removing task.o(i.ByteArrayToU16), (4 bytes).
    Removing task.o(i.ByteArrayToU32), (10 bytes).
    Removing task.o(i.TASK_ClearWave), (288 bytes).
    Removing task.o(i.TASK_Continuous), (216 bytes).
    Removing task.o(i.TASK_DisplayWave), (372 bytes).
    Removing task.o(i.TASK_Init), (8 bytes).
    Removing task.o(i.TASK_InitPeripheral), (56 bytes).
    Removing task.o(i.TASK_Other), (2 bytes).
    Removing task.o(i.TASK_Run), (4 bytes).
    Removing task.o(i.TASK_SendNum), (34 bytes).
    Removing task.o(i.TASK_Start), (16 bytes).
    Removing task.o(i.TASK_UpdateVar), (52 bytes).
    Removing task.o(i.U16ToByteArray), (8 bytes).
    Removing task.o(i.U32ToByteArray), (16 bytes).
    Removing task.o(i.mySqrt), (20 bytes).
    Removing task.o(.data), (1 bytes).
    Removing task.o(.data), (8 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (4 bytes).
    Removing task.o(.data), (8 bytes).
    Removing stm32f30x_adc.o(.rev16_text), (4 bytes).
    Removing stm32f30x_adc.o(.revsh_text), (4 bytes).
    Removing stm32f30x_adc.o(i.ADC_AnalogWatchdog1SingleChannelConfig), (14 bytes).
    Removing stm32f30x_adc.o(i.ADC_AnalogWatchdog1ThresholdsConfig), (40 bytes).
    Removing stm32f30x_adc.o(i.ADC_AnalogWatchdog2SingleChannelConfig), (28 bytes).
    Removing stm32f30x_adc.o(i.ADC_AnalogWatchdog2ThresholdsConfig), (32 bytes).
    Removing stm32f30x_adc.o(i.ADC_AnalogWatchdog3SingleChannelConfig), (28 bytes).
    Removing stm32f30x_adc.o(i.ADC_AnalogWatchdog3ThresholdsConfig), (32 bytes).
    Removing stm32f30x_adc.o(i.ADC_AnalogWatchdogCmd), (12 bytes).
    Removing stm32f30x_adc.o(i.ADC_AutoDelayCmd), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_AutoInjectedConvCmd), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_ChannelOffset1Cmd), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_ChannelOffset2Cmd), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_ChannelOffset3Cmd), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_ChannelOffset4Cmd), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_ClearCommonFlag), (44 bytes).
    Removing stm32f30x_adc.o(i.ADC_ClearFlag), (4 bytes).
    Removing stm32f30x_adc.o(i.ADC_ClearITPendingBit), (4 bytes).
    Removing stm32f30x_adc.o(i.ADC_Cmd), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_CommonInit), (84 bytes).
    Removing stm32f30x_adc.o(i.ADC_CommonStructInit), (14 bytes).
    Removing stm32f30x_adc.o(i.ADC_DMACmd), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_DMAConfig), (16 bytes).
    Removing stm32f30x_adc.o(i.ADC_DeInit), (68 bytes).
    Removing stm32f30x_adc.o(i.ADC_DisableCmd), (10 bytes).
    Removing stm32f30x_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f30x_adc.o(i.ADC_DiscModeCmd), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_ExternalTriggerConfig), (18 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetCalibrationStatus), (14 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetCalibrationValue), (6 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetCommonFlagStatus), (48 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetConversionValue), (6 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetDisableCmdStatus), (14 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetDualModeConversionValue), (36 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetFlagStatus), (14 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetITStatus), (28 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetInjectedConversionValue), (16 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetStartConversionStatus), (14 bytes).
    Removing stm32f30x_adc.o(i.ADC_GetStartInjectedConversionStatus), (14 bytes).
    Removing stm32f30x_adc.o(i.ADC_ITConfig), (16 bytes).
    Removing stm32f30x_adc.o(i.ADC_Init), (60 bytes).
    Removing stm32f30x_adc.o(i.ADC_InjectedChannelSampleTimeConfig), (62 bytes).
    Removing stm32f30x_adc.o(i.ADC_InjectedDiscModeCmd), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_InjectedInit), (44 bytes).
    Removing stm32f30x_adc.o(i.ADC_InjectedStructInit), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_RegularChannelConfig), (172 bytes).
    Removing stm32f30x_adc.o(i.ADC_RegularChannelSequencerLengthConfig), (18 bytes).
    Removing stm32f30x_adc.o(i.ADC_SelectCalibrationMode), (16 bytes).
    Removing stm32f30x_adc.o(i.ADC_SelectDifferentialMode), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_SelectQueueOfContextMode), (20 bytes).
    Removing stm32f30x_adc.o(i.ADC_SetCalibrationValue), (6 bytes).
    Removing stm32f30x_adc.o(i.ADC_SetChannelOffset1), (32 bytes).
    Removing stm32f30x_adc.o(i.ADC_SetChannelOffset2), (32 bytes).
    Removing stm32f30x_adc.o(i.ADC_SetChannelOffset3), (32 bytes).
    Removing stm32f30x_adc.o(i.ADC_SetChannelOffset4), (32 bytes).
    Removing stm32f30x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f30x_adc.o(i.ADC_StartConversion), (10 bytes).
    Removing stm32f30x_adc.o(i.ADC_StartInjectedConversion), (10 bytes).
    Removing stm32f30x_adc.o(i.ADC_StopConversion), (10 bytes).
    Removing stm32f30x_adc.o(i.ADC_StopInjectedConversion), (10 bytes).
    Removing stm32f30x_adc.o(i.ADC_StructInit), (22 bytes).
    Removing stm32f30x_adc.o(i.ADC_TempSensorCmd), (28 bytes).
    Removing stm32f30x_adc.o(i.ADC_VbatCmd), (28 bytes).
    Removing stm32f30x_adc.o(i.ADC_VoltageRegulatorCmd), (28 bytes).
    Removing stm32f30x_adc.o(i.ADC_VrefintCmd), (68 bytes).
    Removing stm32f30x_dac.o(.rev16_text), (4 bytes).
    Removing stm32f30x_dac.o(.revsh_text), (4 bytes).
    Removing stm32f30x_dac.o(i.DAC_ClearFlag), (6 bytes).
    Removing stm32f30x_dac.o(i.DAC_ClearITPendingBit), (6 bytes).
    Removing stm32f30x_dac.o(i.DAC_Cmd), (18 bytes).
    Removing stm32f30x_dac.o(i.DAC_DMACmd), (20 bytes).
    Removing stm32f30x_dac.o(i.DAC_DeInit), (44 bytes).
    Removing stm32f30x_dac.o(i.DAC_DualSoftwareTriggerCmd), (20 bytes).
    Removing stm32f30x_dac.o(i.DAC_GetDataOutputValue), (16 bytes).
    Removing stm32f30x_dac.o(i.DAC_GetFlagStatus), (16 bytes).
    Removing stm32f30x_dac.o(i.DAC_GetITStatus), (24 bytes).
    Removing stm32f30x_dac.o(i.DAC_ITConfig), (16 bytes).
    Removing stm32f30x_dac.o(i.DAC_Init), (34 bytes).
    Removing stm32f30x_dac.o(i.DAC_SetChannel1Data), (12 bytes).
    Removing stm32f30x_dac.o(i.DAC_SetChannel2Data), (12 bytes).
    Removing stm32f30x_dac.o(i.DAC_SetDualChannelData), (20 bytes).
    Removing stm32f30x_dac.o(i.DAC_SoftwareTriggerCmd), (22 bytes).
    Removing stm32f30x_dac.o(i.DAC_StructInit), (14 bytes).
    Removing stm32f30x_dac.o(i.DAC_WaveGenerationCmd), (16 bytes).
    Removing stm32f30x_exti.o(.rev16_text), (4 bytes).
    Removing stm32f30x_exti.o(.revsh_text), (4 bytes).
    Removing stm32f30x_exti.o(i.EXTI_ClearITPendingBit), (28 bytes).
    Removing stm32f30x_exti.o(i.EXTI_DeInit), (72 bytes).
    Removing stm32f30x_exti.o(i.EXTI_GenerateSWInterrupt), (32 bytes).
    Removing stm32f30x_exti.o(i.EXTI_GetFlagStatus), (36 bytes).
    Removing stm32f30x_exti.o(i.EXTI_Init), (248 bytes).
    Removing stm32f30x_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f30x_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f30x_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_DeInit), (112 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_PinAFConfig), (32 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_ReadInputData), (4 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_ReadInputDataBit), (14 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_ReadOutputData), (4 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_StructInit), (20 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f30x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f30x_hrtim.o(.rev16_text), (4 bytes).
    Removing stm32f30x_hrtim.o(.revsh_text), (4 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_ADCTriggerConfig), (94 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_BurstDMAConfig), (50 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_BurstModeConfig), (60 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_BurstModeCtl), (16 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_ChopperModeConfig), (32 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_ClearCommonFlag), (12 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_ClearCommonITPendingBit), (12 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_ClearFlag), (36 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_CompareUnitConfig), (96 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_DLLCalibrationStart), (36 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_DMACmd), (50 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_DeInit), (24 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_DeadTimeConfig), (60 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_EventConfig), (4 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_EventPrescalerConfig), (16 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_ExternalEventConfig), (418 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_FaultConfig), (178 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_FaultModeCtl), (90 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_FaultPrescalerConfig), (16 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_GetBurstStatus), (10 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_GetCapturedValue), (28 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_GetCommonFlagStatus), (16 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_GetCommonITStatus), (32 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_GetCurrentPushPullStatus), (14 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_GetDelayedProtectionStatus), (82 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_GetFlagStatus), (42 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_GetIdlePushPullStatus), (14 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_ITCommonConfig), (20 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_ITConfig), (50 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_MasterBase_Config), (42 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_MasterSetCompare), (32 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_OutputConfig), (142 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleBaseStart), (20 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleBaseStop), (152 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleBase_Init), (14 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleCaptureChannelConfig), (160 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleCaptureStart), (20 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleCaptureStop), (196 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleCapture_Init), (4 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleOCChannelConfig), (182 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleOCStart), (28 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleOCStop), (164 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleOC_Init), (4 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulseChannelConfig), (172 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulseStart), (28 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulseStop), (164 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimpleOnePulse_Init), (4 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimplePWMChannelConfig), (136 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimplePWMStart), (28 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimplePWMStop), (164 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SimplePWM_Init), (4 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SlaveSetCompare), (44 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SoftwareCapture), (30 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SoftwareReset), (12 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SoftwareUpdate), (12 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_SynchronizationConfig), (30 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_TIM_ResetConfig), (114 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_TimerEventFilteringConfig), (196 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_TimingUnitBase_Config), (48 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_TimingUnitWaveform_Config), (160 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformCaptureConfig), (28 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformCompareConfig), (122 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformCounterStart), (8 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformCounterStop), (8 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformGetOutputLevel), (82 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformGetOutputState), (120 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformOutputConfig), (4 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformOutputStart), (6 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformOutputStop), (6 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformSetOutputLevel), (94 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_WaveformTimerConfig), (88 bytes).
    Removing stm32f30x_hrtim.o(i.HRTIM_Waveform_Init), (116 bytes).
    Removing stm32f30x_hrtim.o(.data), (24 bytes).
    Removing stm32f30x_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f30x_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f30x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f30x_iwdg.o(i.IWDG_GetFlagStatus), (20 bytes).
    Removing stm32f30x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f30x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f30x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f30x_iwdg.o(i.IWDG_SetWindowValue), (12 bytes).
    Removing stm32f30x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f30x_misc.o(.rev16_text), (4 bytes).
    Removing stm32f30x_misc.o(.revsh_text), (4 bytes).
    Removing stm32f30x_misc.o(i.NVIC_Init), (100 bytes).
    Removing stm32f30x_misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing stm32f30x_misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing stm32f30x_misc.o(i.NVIC_SystemLPConfig), (24 bytes).
    Removing stm32f30x_misc.o(i.SysTick_CLKSourceConfig), (24 bytes).
    Removing stm32f30x_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f30x_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f30x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f30x_pwr.o(i.PWR_ClearFlag), (16 bytes).
    Removing stm32f30x_pwr.o(i.PWR_DeInit), (24 bytes).
    Removing stm32f30x_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f30x_pwr.o(i.PWR_EnterSTOPMode), (52 bytes).
    Removing stm32f30x_pwr.o(i.PWR_EnterSleepMode), (28 bytes).
    Removing stm32f30x_pwr.o(i.PWR_GetFlagStatus), (20 bytes).
    Removing stm32f30x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f30x_pwr.o(i.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f30x_pwr.o(i.PWR_WakeUpPinCmd), (24 bytes).
    Removing stm32f30x_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f30x_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f30x_rcc.o(i.RCC_ADCCLKConfig), (32 bytes).
    Removing stm32f30x_rcc.o(i.RCC_AHBPeriphClockCmd), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_AHBPeriphResetCmd), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_APB1PeriphClockCmd), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_APB2PeriphClockCmd), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_APB2PeriphResetCmd), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f30x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f30x_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f30x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f30x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f30x_rcc.o(i.RCC_DeInit), (80 bytes).
    Removing stm32f30x_rcc.o(i.RCC_GetClocksFreq), (736 bytes).
    Removing stm32f30x_rcc.o(i.RCC_GetFlagStatus), (52 bytes).
    Removing stm32f30x_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f30x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f30x_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f30x_rcc.o(i.RCC_HRTIM1CLKConfig), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f30x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f30x_rcc.o(i.RCC_I2CCLKConfig), (52 bytes).
    Removing stm32f30x_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f30x_rcc.o(i.RCC_ITConfig), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_LSEConfig), (32 bytes).
    Removing stm32f30x_rcc.o(i.RCC_LSEDriveConfig), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f30x_rcc.o(i.RCC_MCOConfig), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f30x_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f30x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f30x_rcc.o(i.RCC_PLLConfig), (24 bytes).
    Removing stm32f30x_rcc.o(i.RCC_PREDIV1Config), (20 bytes).
    Removing stm32f30x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f30x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f30x_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f30x_rcc.o(i.RCC_TIMCLKConfig), (72 bytes).
    Removing stm32f30x_rcc.o(i.RCC_USARTCLKConfig), (72 bytes).
    Removing stm32f30x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f30x_rcc.o(i.RCC_WaitForHSEStartUp), (44 bytes).
    Removing stm32f30x_rcc.o(.data), (48 bytes).
    Removing stm32f30x_spi.o(.rev16_text), (4 bytes).
    Removing stm32f30x_spi.o(.revsh_text), (4 bytes).
    Removing stm32f30x_spi.o(i.I2S_Cmd), (20 bytes).
    Removing stm32f30x_spi.o(i.I2S_FullDuplexConfig), (58 bytes).
    Removing stm32f30x_spi.o(i.I2S_Init), (152 bytes).
    Removing stm32f30x_spi.o(i.I2S_StructInit), (18 bytes).
    Removing stm32f30x_spi.o(i.SPI_BiDirectionalLineConfig), (22 bytes).
    Removing stm32f30x_spi.o(i.SPI_CRCLengthConfig), (16 bytes).
    Removing stm32f30x_spi.o(i.SPI_CalculateCRC), (20 bytes).
    Removing stm32f30x_spi.o(i.SPI_Cmd), (20 bytes).
    Removing stm32f30x_spi.o(i.SPI_DataSizeConfig), (12 bytes).
    Removing stm32f30x_spi.o(i.SPI_GetCRC), (12 bytes).
    Removing stm32f30x_spi.o(i.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f30x_spi.o(i.SPI_GetReceptionFIFOStatus), (8 bytes).
    Removing stm32f30x_spi.o(i.SPI_GetTransmissionFIFOStatus), (8 bytes).
    Removing stm32f30x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f30x_spi.o(i.SPI_I2S_DMACmd), (16 bytes).
    Removing stm32f30x_spi.o(i.SPI_I2S_DeInit), (84 bytes).
    Removing stm32f30x_spi.o(i.SPI_I2S_GetFlagStatus), (14 bytes).
    Removing stm32f30x_spi.o(i.SPI_I2S_GetITStatus), (42 bytes).
    Removing stm32f30x_spi.o(i.SPI_I2S_ITConfig), (24 bytes).
    Removing stm32f30x_spi.o(i.SPI_I2S_ReceiveData16), (4 bytes).
    Removing stm32f30x_spi.o(i.SPI_I2S_SendData16), (4 bytes).
    Removing stm32f30x_spi.o(i.SPI_Init), (122 bytes).
    Removing stm32f30x_spi.o(i.SPI_LastDMATransferCmd), (16 bytes).
    Removing stm32f30x_spi.o(i.SPI_NSSInternalSoftwareConfig), (24 bytes).
    Removing stm32f30x_spi.o(i.SPI_NSSPulseModeCmd), (20 bytes).
    Removing stm32f30x_spi.o(i.SPI_ReceiveData8), (4 bytes).
    Removing stm32f30x_spi.o(i.SPI_RxFIFOThresholdConfig), (16 bytes).
    Removing stm32f30x_spi.o(i.SPI_SSOutputCmd), (20 bytes).
    Removing stm32f30x_spi.o(i.SPI_SendData8), (4 bytes).
    Removing stm32f30x_spi.o(i.SPI_StructInit), (28 bytes).
    Removing stm32f30x_spi.o(i.SPI_TIModeCmd), (20 bytes).
    Removing stm32f30x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f30x_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f30x_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_BreakConfig), (16 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_BypassParityCheckDisable), (16 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_ClearFlag), (16 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_DMAChannelRemapConfig), (44 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_DeInit), (40 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_EXTILineConfig), (40 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_EncoderRemapConfig), (24 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_GetFlagStatus), (20 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_I2CFastModePlusConfig), (24 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_ITConfig), (24 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_MemoryRemapConfig), (20 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_SRAMWRPEnable), (16 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_TriggerRemapConfig), (44 bytes).
    Removing stm32f30x_syscfg.o(i.SYSCFG_USBInterruptLineRemapCmd), (12 bytes).
    Removing stm32f30x_tim.o(.rev16_text), (4 bytes).
    Removing stm32f30x_tim.o(.revsh_text), (4 bytes).
    Removing stm32f30x_tim.o(i.TI1_Config), (40 bytes).
    Removing stm32f30x_tim.o(i.TI2_Config), (54 bytes).
    Removing stm32f30x_tim.o(i.TIM_ARRPreloadConfig), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f30x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f30x_tim.o(i.TIM_Break1Cmd), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_Break1Config), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_Break2Cmd), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_Break2Config), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_CCPreloadControl), (22 bytes).
    Removing stm32f30x_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f30x_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f30x_tim.o(i.TIM_ClearFlag), (8 bytes).
    Removing stm32f30x_tim.o(i.TIM_ClearITPendingBit), (8 bytes).
    Removing stm32f30x_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_ClearOC2Ref), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_ClearOC4Ref), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_ClearOC5Ref), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_ClearOC6Ref), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_Cmd), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_CtrlPWMOutputs), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_DMACmd), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f30x_tim.o(i.TIM_DeInit), (224 bytes).
    Removing stm32f30x_tim.o(i.TIM_ETRClockMode1Config), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_ETRClockMode2Config), (18 bytes).
    Removing stm32f30x_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f30x_tim.o(i.TIM_EncoderInterfaceConfig), (56 bytes).
    Removing stm32f30x_tim.o(i.TIM_ForcedOC1Config), (16 bytes).
    Removing stm32f30x_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_ForcedOC4Config), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_ForcedOC5Config), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_ForcedOC6Config), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_GetCapture4), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_GetCounter), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_GetITStatus), (24 bytes).
    Removing stm32f30x_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_ICInit), (184 bytes).
    Removing stm32f30x_tim.o(i.TIM_ICStructInit), (16 bytes).
    Removing stm32f30x_tim.o(i.TIM_ITConfig), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_ITRxExternalClockConfig), (18 bytes).
    Removing stm32f30x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC1Init), (136 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC2FastConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC2Init), (120 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC2NPolarityConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC2PolarityConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC2PreloadConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC3Init), (108 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC3NPolarityConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC3PolarityConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC3PreloadConfig), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC4FastConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC4Init), (84 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC4PolarityConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC4PreloadConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC5Init), (84 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC5PolarityConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC5PreloadConfig), (12 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC6Init), (80 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC6PolarityConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OC6PreloadConfig), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_PWMIConfig), (108 bytes).
    Removing stm32f30x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f30x_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectCCDMA), (22 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectCOM), (22 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectGC5C1), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectGC5C2), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectGC5C3), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectHallSensor), (22 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectInputTrigger), (14 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectOCREFClear), (18 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectOCxM), (76 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectOutputTrigger2), (16 bytes).
    Removing stm32f30x_tim.o(i.TIM_SelectSlaveMode), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetCompare5), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetCompare6), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetIC2Prescaler), (18 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f30x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f30x_tim.o(i.TIM_TIxExternalClockConfig), (46 bytes).
    Removing stm32f30x_tim.o(i.TIM_TimeBaseInit), (144 bytes).
    Removing stm32f30x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f30x_tim.o(i.TIM_UIFRemap), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f30x_tim.o(i.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f30x_usart.o(.rev16_text), (4 bytes).
    Removing stm32f30x_usart.o(.revsh_text), (4 bytes).
    Removing stm32f30x_usart.o(i.USART_AddressDetectionConfig), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_AutoBaudRateCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_AutoBaudRateConfig), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_ClearITPendingBit), (10 bytes).
    Removing stm32f30x_usart.o(i.USART_ClockInit), (28 bytes).
    Removing stm32f30x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f30x_usart.o(i.USART_Cmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_DECmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_DEPolarityConfig), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_DMACmd), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_DMAReceptionErrorConfig), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_DataInvCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_DeInit), (116 bytes).
    Removing stm32f30x_usart.o(i.USART_DirectionModeCmd), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_GetFlagStatus), (14 bytes).
    Removing stm32f30x_usart.o(i.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_ITConfig), (40 bytes).
    Removing stm32f30x_usart.o(i.USART_Init), (200 bytes).
    Removing stm32f30x_usart.o(i.USART_InvPinCmd), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_IrDACmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_LINCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_MSBFirstCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_MuteModeCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_MuteModeWakeUpConfig), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_OverrunDetectionConfig), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_ReceiveData), (8 bytes).
    Removing stm32f30x_usart.o(i.USART_ReceiverTimeOutCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_RequestCmd), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_STOPModeCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_SWAPPinCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f30x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f30x_usart.o(i.USART_SetAutoRetryCount), (18 bytes).
    Removing stm32f30x_usart.o(i.USART_SetBlockLength), (18 bytes).
    Removing stm32f30x_usart.o(i.USART_SetDEAssertionTime), (18 bytes).
    Removing stm32f30x_usart.o(i.USART_SetDEDeassertionTime), (18 bytes).
    Removing stm32f30x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_SetReceiverTimeOut), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_SmartCardCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f30x_usart.o(i.USART_StopModeWakeUpSourceConfig), (16 bytes).
    Removing stm32f30x_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f30x_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f30x_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f30x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f30x_wwdg.o(i.WWDG_DeInit), (24 bytes).
    Removing stm32f30x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f30x_wwdg.o(i.WWDG_EnableIT), (16 bytes).
    Removing stm32f30x_wwdg.o(i.WWDG_GetFlagStatus), (20 bytes).
    Removing stm32f30x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f30x_wwdg.o(i.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f30x_wwdg.o(i.WWDG_SetWindowValue), (28 bytes).
    Removing uart.o(.rev16_text), (4 bytes).
    Removing uart.o(.revsh_text), (4 bytes).
    Removing uart.o(i.UART_CalcCheck), (24 bytes).
    Removing uart.o(i.UART_ClearRxdBuffer), (24 bytes).
    Removing uart.o(i.UART_GetStr), (4 bytes).
    Removing uart.o(i.UART_GetTxdFifoDataEmptyLen), (32 bytes).
    Removing uart.o(i.UART_GetTxdFifoDataLen), (28 bytes).
    Removing uart.o(i.UART_Init), (324 bytes).
    Removing uart.o(i.UART_SendCmd), (112 bytes).
    Removing uart.o(i.UART_SendStr), (22 bytes).
    Removing uart.o(.data), (2 bytes).
    Removing qk_common.o(.rev16_text), (4 bytes).
    Removing qk_common.o(.revsh_text), (4 bytes).
    Removing qk_common.o(i.CloclkInit), (120 bytes).
    Removing qk_common.o(i.DelayInit), (48 bytes).
    Removing qk_common.o(i.DelayMs), (60 bytes).
    Removing qk_common.o(i.DelayUs), (44 bytes).
    Removing qk_common.o(.data), (12 bytes).
    Removing qk_common.o(.data), (1 bytes).
    Removing qk_lcd.o(.rev16_text), (4 bytes).
    Removing qk_lcd.o(.revsh_text), (4 bytes).
    Removing qk_lcd.o(i.LCD_BackLightInit), (128 bytes).
    Removing qk_lcd.o(i.LCD_Clear), (38 bytes).
    Removing qk_lcd.o(i.LCD_DrawBmp), (68 bytes).
    Removing qk_lcd.o(i.LCD_DrawLine), (464 bytes).
    Removing qk_lcd.o(i.LCD_DrawNum), (144 bytes).
    Removing qk_lcd.o(i.LCD_DrawPic), (684 bytes).
    Removing qk_lcd.o(i.LCD_DrawPoint), (64 bytes).
    Removing qk_lcd.o(i.LCD_FSMCConfig), (2 bytes).
    Removing qk_lcd.o(i.LCD_GetBar), (2 bytes).
    Removing qk_lcd.o(i.LCD_Init), (652 bytes).
    Removing qk_lcd.o(i.LCD_InitGPIO), (248 bytes).
    Removing qk_lcd.o(i.LCD_PutChar), (136 bytes).
    Removing qk_lcd.o(i.LCD_PutHanzi), (156 bytes).
    Removing qk_lcd.o(i.LCD_PutStr), (236 bytes).
    Removing qk_lcd.o(i.LCD_ReadPoint), (2 bytes).
    Removing qk_lcd.o(i.LCD_ReadRAM_Prepare), (2 bytes).
    Removing qk_lcd.o(i.LCD_SelectFont), (72 bytes).
    Removing qk_lcd.o(i.LCD_SetBar), (72 bytes).
    Removing qk_lcd.o(i.LCD_SetBright), (28 bytes).
    Removing qk_lcd.o(i.LCD_SetCursor), (8 bytes).
    Removing qk_lcd.o(i.LCD_SetDisplayWindow), (184 bytes).
    Removing qk_lcd.o(i.LCD_SetScanMode), (2 bytes).
    Removing qk_lcd.o(i.LCD_WriteArray), (64 bytes).
    Removing qk_lcd.o(i.LCD_WriteConst), (56 bytes).
    Removing qk_lcd.o(i.LCD_WriteRAM_Prepare), (48 bytes).
    Removing qk_lcd.o(i.Search_Font), (64 bytes).
    Removing qk_lcd.o(i.num2string), (270 bytes).
    Removing qk_lcd.o(.data), (12 bytes).
    Removing qk_ir.o(.rev16_text), (4 bytes).
    Removing qk_ir.o(.revsh_text), (4 bytes).
    Removing qk_ir.o(i.IrInit), (200 bytes).
    Removing qk_menu.o(.rev16_text), (4 bytes).
    Removing qk_menu.o(.revsh_text), (4 bytes).
    Removing qk_menu.o(i.FlashReadData), (4 bytes).
    Removing qk_menu.o(i.FlashWriteData), (10 bytes).
    Removing qk_menu.o(i.FlashWriteEnd), (16 bytes).
    Removing qk_menu.o(i.FlashWriteStart), (28 bytes).
    Removing qk_menu.o(i.GetKeyValue), (124 bytes).
    Removing qk_menu.o(i.InitFun), (44 bytes).
    Removing qk_menu.o(i.InitParameter), (14 bytes).
    Removing qk_menu.o(i.LoadData), (72 bytes).
    Removing qk_menu.o(i.MenuDisplayNumber), (272 bytes).
    Removing qk_menu.o(i.MenuStrLen), (16 bytes).
    Removing qk_menu.o(i.SaveData), (4 bytes).
    Removing qk_menu.o(i.SetParameter), (3354 bytes).
    Removing qk_menu.o(i.UpdateOneTime), (2 bytes).
    Removing qk_menu.o(i.UpdatePeriod), (224 bytes).
    Removing qk_menu.o(i.changeFun_DacSetValue0), (28 bytes).
    Removing qk_menu.o(i.changeFun_DacSetValue1), (32 bytes).
    Removing qk_menu.o(i.changeFun_Duty), (2 bytes).
    Removing qk_menu.o(i.changeFun_LcdBkLight), (12 bytes).
    Removing qk_menu.o(i.changeFun_PwmDead), (28 bytes).
    Removing qk_menu.o(i.changeFun_PwmDutyLen), (148 bytes).
    Removing qk_menu.o(i.changeFun_PwmFreq), (72 bytes).
    Removing qk_menu.o(i.changeFun_PwmPhase), (36 bytes).
    Removing qk_menu.o(i.changeFun_RunState), (132 bytes).
    Removing qk_menu.o(.bss), (44 bytes).
    Removing qk_menu.o(.constdata), (968 bytes).
    Removing qk_menu.o(.data), (24 bytes).
    Removing stm32f30x_flash.o(.rev16_text), (4 bytes).
    Removing stm32f30x_flash.o(.revsh_text), (4 bytes).
    Removing stm32f30x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f30x_flash.o(i.FLASH_EraseAllPages), (56 bytes).
    Removing stm32f30x_flash.o(i.FLASH_ErasePage), (60 bytes).
    Removing stm32f30x_flash.o(i.FLASH_GetFlagStatus), (20 bytes).
    Removing stm32f30x_flash.o(i.FLASH_GetStatus), (40 bytes).
    Removing stm32f30x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f30x_flash.o(i.FLASH_ITConfig), (24 bytes).
    Removing stm32f30x_flash.o(i.FLASH_Lock), (16 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_BOOTConfig), (80 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_EnableWRP), (92 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_Erase), (104 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_GetRDP), (20 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_Launch), (16 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_Lock), (16 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_RDPConfig), (108 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_SRAMParityConfig), (64 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_Unlock), (32 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_UserConfig), (92 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_VDDAConfig), (80 bytes).
    Removing stm32f30x_flash.o(i.FLASH_OB_WriteUser), (80 bytes).
    Removing stm32f30x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f30x_flash.o(i.FLASH_ProgramHalfWord), (52 bytes).
    Removing stm32f30x_flash.o(i.FLASH_ProgramOptionByteData), (56 bytes).
    Removing stm32f30x_flash.o(i.FLASH_ProgramWord), (76 bytes).
    Removing stm32f30x_flash.o(i.FLASH_SetLatency), (20 bytes).
    Removing stm32f30x_flash.o(i.FLASH_Unlock), (32 bytes).
    Removing stm32f30x_flash.o(i.FLASH_WaitForLastOperation), (36 bytes).
    Removing hal.o(.rev16_text), (4 bytes).
    Removing hal.o(.revsh_text), (4 bytes).
    Removing hal.o(i.HAL_AdcInit), (2 bytes).
    Removing hal.o(i.HAL_DacInit), (2 bytes).
    Removing hal.o(i.HAL_I2cReadReg), (88 bytes).
    Removing hal.o(i.HAL_I2cWriteReg), (66 bytes).
    Removing hal.o(i.HAL_LedInit), (92 bytes).
    Removing hal.o(i.HAL_PwmInit), (384 bytes).
    Removing hal.o(i.HAL_TouchCal), (1496 bytes).
    Removing hal.o(i.HAL_TouchDispPos), (416 bytes).
    Removing hal.o(i.HAL_TouchDrawLine), (112 bytes).
    Removing hal.o(i.HAL_TouchInit), (140 bytes).
    Removing hal.o(i.HAL_TouchResGetAd), (74 bytes).
    Removing hal.o(i.HAL_TouchScan), (232 bytes).
    Removing hal.o(i.HAL_WatchDogInit), (60 bytes).
    Removing hal.o(i.I2C_ByteWrite), (80 bytes).
    Removing hal.o(i.I2C_ReceiveByte), (64 bytes).
    Removing hal.o(i.I2C_SendAck), (52 bytes).
    Removing hal.o(i.I2C_SendByte), (108 bytes).
    Removing hal.o(i.I2C_Start), (32 bytes).
    Removing hal.o(i.I2C_Stop), (38 bytes).
    Removing hal.o(i.I2C_Wait), (6 bytes).
    Removing hal.o(.bss), (22 bytes).
    Removing hal.o(.constdata), (10 bytes).
    Removing hal.o(.data), (16 bytes).

665 unused section(s) (total 47087 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llsdiv.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_adc.c 0x00000000   Number         0  stm32f30x_adc.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_dac.c 0x00000000   Number         0  stm32f30x_dac.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_exti.c 0x00000000   Number         0  stm32f30x_exti.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_flash.c 0x00000000   Number         0  stm32f30x_flash.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_gpio.c 0x00000000   Number         0  stm32f30x_gpio.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_hrtim.c 0x00000000   Number         0  stm32f30x_hrtim.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_iwdg.c 0x00000000   Number         0  stm32f30x_iwdg.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_misc.c 0x00000000   Number         0  stm32f30x_misc.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_pwr.c 0x00000000   Number         0  stm32f30x_pwr.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_rcc.c 0x00000000   Number         0  stm32f30x_rcc.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_spi.c 0x00000000   Number         0  stm32f30x_spi.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_syscfg.c 0x00000000   Number         0  stm32f30x_syscfg.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_tim.c 0x00000000   Number         0  stm32f30x_tim.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_usart.c 0x00000000   Number         0  stm32f30x_usart.o ABSOLUTE
    ..\Source\FWLib_STM32F334\src\stm32f30x_wwdg.c 0x00000000   Number         0  stm32f30x_wwdg.o ABSOLUTE
    ..\Source\cm4_core\startup_stm32f334x8.s 0x00000000   Number         0  startup_stm32f334x8.o ABSOLUTE
    ..\Source\qiankunLib\src\hal.c           0x00000000   Number         0  hal.o ABSOLUTE
    ..\Source\qiankunLib\src\qk_common.c     0x00000000   Number         0  qk_common.o ABSOLUTE
    ..\Source\qiankunLib\src\qk_ir.c         0x00000000   Number         0  qk_ir.o ABSOLUTE
    ..\Source\qiankunLib\src\qk_lcd.c        0x00000000   Number         0  qk_lcd.o ABSOLUTE
    ..\Source\qiankunLib\src\qk_menu.c       0x00000000   Number         0  qk_menu.o ABSOLUTE
    ..\Source\qiankunLib\uart\uart.c         0x00000000   Number         0  uart.o ABSOLUTE
    ..\User\SPI.c                            0x00000000   Number         0  spi.o ABSOLUTE
    ..\User\font.c                           0x00000000   Number         0  font.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\stm32f3xx_it.c                   0x00000000   Number         0  stm32f3xx_it.o ABSOLUTE
    ..\User\system_stm32f30x.c               0x00000000   Number         0  system_stm32f30x.o ABSOLUTE
    ..\User\task.c                           0x00000000   Number         0  task.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_adc.c 0x00000000   Number         0  stm32f30x_adc.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_dac.c 0x00000000   Number         0  stm32f30x_dac.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_exti.c 0x00000000   Number         0  stm32f30x_exti.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_flash.c 0x00000000   Number         0  stm32f30x_flash.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_gpio.c 0x00000000   Number         0  stm32f30x_gpio.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_hrtim.c 0x00000000   Number         0  stm32f30x_hrtim.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_iwdg.c 0x00000000   Number         0  stm32f30x_iwdg.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_misc.c 0x00000000   Number         0  stm32f30x_misc.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_pwr.c 0x00000000   Number         0  stm32f30x_pwr.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_rcc.c 0x00000000   Number         0  stm32f30x_rcc.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_spi.c 0x00000000   Number         0  stm32f30x_spi.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_syscfg.c 0x00000000   Number         0  stm32f30x_syscfg.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_tim.c 0x00000000   Number         0  stm32f30x_tim.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_usart.c 0x00000000   Number         0  stm32f30x_usart.o ABSOLUTE
    ..\\Source\\FWLib_STM32F334\\src\\stm32f30x_wwdg.c 0x00000000   Number         0  stm32f30x_wwdg.o ABSOLUTE
    ..\\Source\\qiankunLib\\src\\hal.c       0x00000000   Number         0  hal.o ABSOLUTE
    ..\\Source\\qiankunLib\\src\\qk_common.c 0x00000000   Number         0  qk_common.o ABSOLUTE
    ..\\Source\\qiankunLib\\src\\qk_ir.c     0x00000000   Number         0  qk_ir.o ABSOLUTE
    ..\\Source\\qiankunLib\\src\\qk_lcd.c    0x00000000   Number         0  qk_lcd.o ABSOLUTE
    ..\\Source\\qiankunLib\\src\\qk_menu.c   0x00000000   Number         0  qk_menu.o ABSOLUTE
    ..\\Source\\qiankunLib\\uart\\uart.c     0x00000000   Number         0  uart.o ABSOLUTE
    ..\\User\\SPI.c                          0x00000000   Number         0  spi.o ABSOLUTE
    ..\\User\\font.c                         0x00000000   Number         0  font.o ABSOLUTE
    ..\\User\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\User\\stm32f3xx_it.c                 0x00000000   Number         0  stm32f3xx_it.o ABSOLUTE
    ..\\User\\system_stm32f30x.c             0x00000000   Number         0  system_stm32f30x.o ABSOLUTE
    ..\\User\\task.c                         0x00000000   Number         0  task.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f334x8.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001fc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080001fe   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000202   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000204   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x08000206   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x08000208   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000208   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000208   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800020e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800020e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000212   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000212   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800021a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800021c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800021c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000220   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000228   Section       64  startup_stm32f334x8.o(.text)
    $v0                                      0x08000228   Number         0  startup_stm32f334x8.o(.text)
    .text                                    0x08000268   Section       72  llsdiv.o(.text)
    .text                                    0x080002b0   Section        0  heapauxi.o(.text)
    .text                                    0x080002b6   Section      238  lludivv7m.o(.text)
    .text                                    0x080003a4   Section        8  libspace.o(.text)
    .text                                    0x080003ac   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080003f6   Section        0  exit.o(.text)
    .text                                    0x08000404   Section        0  sys_exit.o(.text)
    .text                                    0x08000410   Section        2  use_no_semi.o(.text)
    .text                                    0x08000412   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x08000412   Section        0  stm32f3xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000414   Section        0  stm32f3xx_it.o(i.DebugMon_Handler)
    i.DelayHalfUs                            0x08000416   Section        0  qk_common.o(i.DelayHalfUs)
    i.EXTI9_5_IRQHandler                     0x08000460   Section        0  stm32f3xx_it.o(i.EXTI9_5_IRQHandler)
    i.EXTI_ClearFlag                         0x0800047c   Section        0  stm32f30x_exti.o(i.EXTI_ClearFlag)
    i.EXTI_GetITStatus                       0x08000498   Section        0  stm32f30x_exti.o(i.EXTI_GetITStatus)
    i.GPIO_Init                              0x080004bc   Section        0  stm32f30x_gpio.o(i.GPIO_Init)
    i.HRTIM1_TIMA_IRQHandler                 0x08000530   Section        0  stm32f3xx_it.o(i.HRTIM1_TIMA_IRQHandler)
    i.HRTIM_ClearITPendingBit                0x0800055c   Section        0  stm32f30x_hrtim.o(i.HRTIM_ClearITPendingBit)
    i.HRTIM_GetITStatus                      0x08000580   Section        0  stm32f30x_hrtim.o(i.HRTIM_GetITStatus)
    i.HardFault_Handler                      0x080005c2   Section        0  stm32f3xx_it.o(i.HardFault_Handler)
    i.IrDecode                               0x080005c4   Section        0  qk_ir.o(i.IrDecode)
    i.MemManage_Handler                      0x080007f4   Section        0  stm32f3xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080007f6   Section        0  stm32f3xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x080007f8   Section        0  stm32f3xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x080007fa   Section        0  stm32f3xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x080007fc   Section        0  stm32f3xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08000800   Section        0  system_stm32f30x.o(i.SystemInit)
    i.TASK_Adc                               0x08000868   Section        0  task.o(i.TASK_Adc)
    i.TASK_GetTimeCnt                        0x08000960   Section        0  task.o(i.TASK_GetTimeCnt)
    i.TASK_Periodicity                       0x0800096c   Section        0  task.o(i.TASK_Periodicity)
    i.TASK_Pwm                               0x080009f0   Section        0  task.o(i.TASK_Pwm)
    i.TASK_PwmShutDown                       0x08000b50   Section        0  task.o(i.TASK_PwmShutDown)
    i.TASK_ScanKey                           0x08000b94   Section        0  task.o(i.TASK_ScanKey)
    i.TASK_Send2Nums                         0x08000c08   Section        0  task.o(i.TASK_Send2Nums)
    i.TIM1_UP_TIM16_IRQHandler               0x08000c3c   Section        0  stm32f3xx_it.o(i.TIM1_UP_TIM16_IRQHandler)
    i.UART_RxdIsr                            0x08000c54   Section        0  uart.o(i.UART_RxdIsr)
    i.UART_SendData                          0x08000d14   Section        0  uart.o(i.UART_SendData)
    i.UART_TxdIsr                            0x08000d84   Section        0  uart.o(i.UART_TxdIsr)
    i.USART2_IRQHandler                      0x08000dc8   Section        0  stm32f3xx_it.o(i.USART2_IRQHandler)
    i.USART_ClearFlag                        0x08000e04   Section        0  stm32f30x_usart.o(i.USART_ClearFlag)
    i.USART_GetITStatus                      0x08000e08   Section        0  stm32f30x_usart.o(i.USART_GetITStatus)
    i.UsageFault_Handler                     0x08000e42   Section        0  stm32f3xx_it.o(i.UsageFault_Handler)
    i.intSqrt                                0x08000e44   Section        0  task.o(i.intSqrt)
    i.main                                   0x08000e94   Section        0  main.o(i.main)
    i.mean1                                  0x08000e98   Section        0  task.o(i.mean1)
    i.mean2                                  0x08000ee4   Section        0  task.o(i.mean2)
    i.mean3                                  0x08000f30   Section        0  task.o(i.mean3)
    x$fpl$fpinit                             0x08000f7c   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08000f7c   Number         0  fpinit.o(x$fpl$fpinit)
    .data                                    0x20000000   Section      104  task.o(.data)
    cnt                                      0x2000000c   Data           4  task.o(.data)
    task_cnt                                 0x20000010   Data           4  task.o(.data)
    task_cnt                                 0x20000014   Data           4  task.o(.data)
    p                                        0x20000018   Data           4  task.o(.data)
    sum                                      0x2000001c   Data           4  task.o(.data)
    p                                        0x20000020   Data           4  task.o(.data)
    sum                                      0x20000024   Data           4  task.o(.data)
    p                                        0x20000028   Data           4  task.o(.data)
    sum                                      0x2000002c   Data           4  task.o(.data)
    cnt                                      0x20000030   Data           4  task.o(.data)
    wave_cnt                                 0x20000034   Data           4  task.o(.data)
    .data                                    0x20000068   Section       40  uart.o(.data)
    d_last                                   0x20000069   Data           1  uart.o(.data)
    addr_flag                                0x2000006a   Data           1  uart.o(.data)
    check                                    0x2000006b   Data           1  uart.o(.data)
    UART_InitFlag                            0x2000006c   Data           1  uart.o(.data)
    UART_TxdEndFlag                          0x2000006d   Data           1  uart.o(.data)
    data_len                                 0x2000006e   Data           2  uart.o(.data)
    TxdFifoDataFront                         0x20000070   Data           2  uart.o(.data)
    TxdFifoDataRear                          0x20000072   Data           2  uart.o(.data)
    RxdFifoDataFront                         0x20000074   Data           2  uart.o(.data)
    RxdFifoDataRearTemp                      0x20000078   Data           2  uart.o(.data)
    pack_len                                 0x2000007c   Data           4  uart.o(.data)
    .data                                    0x20000090   Section       16  qk_ir.o(.data)
    data1                                    0x20000091   Data           1  qk_ir.o(.data)
    data2                                    0x20000092   Data           1  qk_ir.o(.data)
    data3                                    0x20000093   Data           1  qk_ir.o(.data)
    data4                                    0x20000094   Data           1  qk_ir.o(.data)
    IR_start                                 0x20000095   Data           1  qk_ir.o(.data)
    IR_n                                     0x20000096   Data           1  qk_ir.o(.data)
    IR_Key_last                              0x20000097   Data           1  qk_ir.o(.data)
    rep_cnt                                  0x20000098   Data           1  qk_ir.o(.data)
    .bss                                     0x200000a0   Section     2780  task.o(.bss)
    buffer                                   0x200000a0   Data         100  task.o(.bss)
    buffer                                   0x20000104   Data         100  task.o(.bss)
    buffer                                   0x20000168   Data         100  task.o(.bss)
    d                                        0x200001cc   Data          10  task.o(.bss)
    .bss                                     0x20000b7c   Section     1520  uart.o(.bss)
    TxdFifoData                              0x20000b7c   Data         510  uart.o(.bss)
    .bss                                     0x2000116c   Section       96  libspace.o(.bss)
    HEAP                                     0x200011d0   Section     1024  startup_stm32f334x8.o(HEAP)
    Heap_Mem                                 0x200011d0   Data        1024  startup_stm32f334x8.o(HEAP)
    STACK                                    0x200015d0   Section     2048  startup_stm32f334x8.o(STACK)
    Stack_Mem                                0x200015d0   Data        2048  startup_stm32f334x8.o(STACK)
    __initial_sp                             0x20001dd0   Data           0  startup_stm32f334x8.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f334x8.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f334x8.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f334x8.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001fd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000205   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x08000209   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000209   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000209   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800021b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000221   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000229   Thumb Code     8  startup_stm32f334x8.o(.text)
    ADC1_2_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    CAN1_RX0_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    CAN1_RX1_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    CAN1_SCE_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    CAN1_TX_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    COMP2_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    COMP4_6_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    EXTI0_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    EXTI15_10_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    EXTI1_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    EXTI2_TS_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    EXTI3_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    EXTI4_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    FLASH_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    FPU_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    HRTIM1_FLT_IRQHandler                    0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    HRTIM1_Master_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    HRTIM1_TIMB_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    HRTIM1_TIMC_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    HRTIM1_TIMD_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    HRTIM1_TIME_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    I2C1_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    I2C1_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    PVD_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    RCC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    RTC_Alarm_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    RTC_WKUP_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    SPI1_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    TAMPER_STAMP_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    TIM1_BRK_TIM15_IRQHandler                0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    TIM1_CC_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    TIM1_TRG_COM_TIM17_IRQHandler            0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    TIM2_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    TIM3_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    TIM6_DAC1_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    TIM7_DAC2_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    USART1_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    USART3_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    WWDG_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f334x8.o(.text)
    __user_initial_stackheap                 0x08000245   Thumb Code     0  startup_stm32f334x8.o(.text)
    __aeabi_ldivmod                          0x08000269   Thumb Code     0  llsdiv.o(.text)
    _ll_sdiv                                 0x08000269   Thumb Code    72  llsdiv.o(.text)
    __use_two_region_memory                  0x080002b1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002b3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002b5   Thumb Code     2  heapauxi.o(.text)
    __aeabi_uldivmod                         0x080002b7   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080002b7   Thumb Code   238  lludivv7m.o(.text)
    __user_libspace                          0x080003a5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080003a5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080003a5   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080003ad   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080003f7   Thumb Code    12  exit.o(.text)
    _sys_exit                                0x08000405   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000411   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000411   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x08000413   Thumb Code     2  stm32f3xx_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x08000413   Thumb Code     0  indicate_semi.o(.text)
    DebugMon_Handler                         0x08000415   Thumb Code     2  stm32f3xx_it.o(i.DebugMon_Handler)
    DelayHalfUs                              0x08000417   Thumb Code    74  qk_common.o(i.DelayHalfUs)
    EXTI9_5_IRQHandler                       0x08000461   Thumb Code    28  stm32f3xx_it.o(i.EXTI9_5_IRQHandler)
    EXTI_ClearFlag                           0x0800047d   Thumb Code    22  stm32f30x_exti.o(i.EXTI_ClearFlag)
    EXTI_GetITStatus                         0x08000499   Thumb Code    32  stm32f30x_exti.o(i.EXTI_GetITStatus)
    GPIO_Init                                0x080004bd   Thumb Code   116  stm32f30x_gpio.o(i.GPIO_Init)
    HRTIM1_TIMA_IRQHandler                   0x08000531   Thumb Code    38  stm32f3xx_it.o(i.HRTIM1_TIMA_IRQHandler)
    HRTIM_ClearITPendingBit                  0x0800055d   Thumb Code    36  stm32f30x_hrtim.o(i.HRTIM_ClearITPendingBit)
    HRTIM_GetITStatus                        0x08000581   Thumb Code    66  stm32f30x_hrtim.o(i.HRTIM_GetITStatus)
    HardFault_Handler                        0x080005c3   Thumb Code     2  stm32f3xx_it.o(i.HardFault_Handler)
    IrDecode                                 0x080005c5   Thumb Code   550  qk_ir.o(i.IrDecode)
    MemManage_Handler                        0x080007f5   Thumb Code     2  stm32f3xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080007f7   Thumb Code     2  stm32f3xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x080007f9   Thumb Code     2  stm32f3xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x080007fb   Thumb Code     2  stm32f3xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x080007fd   Thumb Code     2  stm32f3xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08000801   Thumb Code    82  system_stm32f30x.o(i.SystemInit)
    TASK_Adc                                 0x08000869   Thumb Code   228  task.o(i.TASK_Adc)
    TASK_GetTimeCnt                          0x08000961   Thumb Code     8  task.o(i.TASK_GetTimeCnt)
    TASK_Periodicity                         0x0800096d   Thumb Code   118  task.o(i.TASK_Periodicity)
    TASK_Pwm                                 0x080009f1   Thumb Code   332  task.o(i.TASK_Pwm)
    TASK_PwmShutDown                         0x08000b51   Thumb Code    64  task.o(i.TASK_PwmShutDown)
    TASK_ScanKey                             0x08000b95   Thumb Code   108  task.o(i.TASK_ScanKey)
    TASK_Send2Nums                           0x08000c09   Thumb Code    50  task.o(i.TASK_Send2Nums)
    TIM1_UP_TIM16_IRQHandler                 0x08000c3d   Thumb Code    20  stm32f3xx_it.o(i.TIM1_UP_TIM16_IRQHandler)
    UART_RxdIsr                              0x08000c55   Thumb Code   178  uart.o(i.UART_RxdIsr)
    UART_SendData                            0x08000d15   Thumb Code   104  uart.o(i.UART_SendData)
    UART_TxdIsr                              0x08000d85   Thumb Code    56  uart.o(i.UART_TxdIsr)
    USART2_IRQHandler                        0x08000dc9   Thumb Code    48  stm32f3xx_it.o(i.USART2_IRQHandler)
    USART_ClearFlag                          0x08000e05   Thumb Code     4  stm32f30x_usart.o(i.USART_ClearFlag)
    USART_GetITStatus                        0x08000e09   Thumb Code    58  stm32f30x_usart.o(i.USART_GetITStatus)
    UsageFault_Handler                       0x08000e43   Thumb Code     2  stm32f3xx_it.o(i.UsageFault_Handler)
    intSqrt                                  0x08000e45   Thumb Code    76  task.o(i.intSqrt)
    main                                     0x08000e95   Thumb Code     2  main.o(i.main)
    mean1                                    0x08000e99   Thumb Code    68  task.o(i.mean1)
    mean2                                    0x08000ee5   Thumb Code    68  task.o(i.mean2)
    mean3                                    0x08000f31   Thumb Code    68  task.o(i.mean3)
    _fp_init                                 0x08000f7d   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08000f85   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08000f85   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    Region$$Table$$Base                      0x08000f88   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08000fa8   Number         0  anon$$obj.o(Region$$Table)
    PwmDutyIndex                             0x20000000   Data           2  task.o(.data)
    AnalogDataIndex                          0x20000002   Data           2  task.o(.data)
    WaveBufferIndex                          0x20000004   Data           2  task.o(.data)
    KeyState                                 0x20000006   Data           2  task.o(.data)
    AdcRef                                   0x20000008   Data           2  task.o(.data)
    UartSendEnable                           0x2000000a   Data           2  task.o(.data)
    TaskTimeSec                              0x20000038   Data           4  task.o(.data)
    TaskTimeMiliSec                          0x2000003c   Data           4  task.o(.data)
    TaskMiliSecCnt                           0x20000040   Data           4  task.o(.data)
    PwmDead                                  0x20000044   Data           4  task.o(.data)
    RunState                                 0x20000048   Data           4  task.o(.data)
    Volt2CaliK                               0x2000004c   Data           4  task.o(.data)
    PwmDutyLen                               0x20000050   Data           4  task.o(.data)
    OutputAcFlag                             0x20000054   Data           4  task.o(.data)
    PwmDutyIndexOffset                       0x20000058   Data           4  task.o(.data)
    TaskTimeCnt                              0x20000060   Data           8  task.o(.data)
    LocalAddr                                0x20000068   Data           1  uart.o(.data)
    RxdFifoDataRear                          0x20000076   Data           2  uart.o(.data)
    RxdPackLen                               0x20000080   Data           4  uart.o(.data)
    RxdTaskTime                              0x20000088   Data           8  uart.o(.data)
    IR_Key                                   0x20000090   Data           1  qk_ir.o(.data)
    IR_LedTimeout                            0x2000009c   Data           4  qk_ir.o(.data)
    PwmTable                                 0x200001d6   Data        2000  task.o(.bss)
    AnalogData                               0x200009a6   Data         200  task.o(.bss)
    WaveBuffer                               0x20000a6e   Data         100  task.o(.bss)
    AdcValue                                 0x20000ad2   Data          12  task.o(.bss)
    AdcValueAve                              0x20000ae0   Data          24  task.o(.bss)
    AdcValueRmse                             0x20000af8   Data          24  task.o(.bss)
    AdcValueSumTemp                          0x20000b10   Data          24  task.o(.bss)
    AdcValueSqSumTemp                        0x20000b28   Data          48  task.o(.bss)
    Duty                                     0x20000b58   Data          12  task.o(.bss)
    AdcRawData                               0x20000b64   Data          24  task.o(.bss)
    RxdFifoData                              0x20000d7a   Data        1010  uart.o(.bss)
    __libspace_start                         0x2000116c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200011cc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00001048, Max: 0x00008000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00000fa8, Max: 0x00008000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f334x8.o
    0x08000188   0x00000008   Code   RO         4352  * !!!main             c_w.l(__main.o)
    0x08000190   0x00000034   Code   RO         4623    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x0000001a   Code   RO         4625    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x00000002   PAD
    0x080001e0   0x0000001c   Code   RO         4627    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x00000002   Code   RO         4493    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001fe   0x00000004   Code   RO         4500    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4503    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4506    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4508    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4510    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4513    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4515    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4517    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4519    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4521    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4523    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4525    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4527    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4529    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4531    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4533    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4537    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4539    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4541    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000202   0x00000000   Code   RO         4543    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000202   0x00000002   Code   RO         4544    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000204   0x00000002   Code   RO         4574    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000206   0x00000000   Code   RO         4592    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO         4595    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO         4598    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO         4600    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x08000206   0x00000000   Code   RO         4603    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000206   0x00000002   Code   RO         4604    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000208   0x00000000   Code   RO         4386    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000208   0x00000000   Code   RO         4438    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000208   0x00000006   Code   RO         4450    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800020e   0x00000000   Code   RO         4440    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800020e   0x00000004   Code   RO         4441    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000212   0x00000000   Code   RO         4443    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000212   0x00000008   Code   RO         4444    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800021a   0x00000002   Code   RO         4494    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800021c   0x00000000   Code   RO         4546    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800021c   0x00000004   Code   RO         4547    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000220   0x00000006   Code   RO         4548    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000226   0x00000002   PAD
    0x08000228   0x00000040   Code   RO            4    .text               startup_stm32f334x8.o
    0x08000268   0x00000048   Code   RO         4318    .text               c_w.l(llsdiv.o)
    0x080002b0   0x00000006   Code   RO         4350    .text               c_w.l(heapauxi.o)
    0x080002b6   0x000000ee   Code   RO         4387    .text               c_w.l(lludivv7m.o)
    0x080003a4   0x00000008   Code   RO         4479    .text               c_w.l(libspace.o)
    0x080003ac   0x0000004a   Code   RO         4482    .text               c_w.l(sys_stackheap_outer.o)
    0x080003f6   0x0000000c   Code   RO         4486    .text               c_w.l(exit.o)
    0x08000402   0x00000002   PAD
    0x08000404   0x0000000c   Code   RO         4562    .text               c_w.l(sys_exit.o)
    0x08000410   0x00000002   Code   RO         4579    .text               c_w.l(use_no_semi.o)
    0x08000412   0x00000000   Code   RO         4581    .text               c_w.l(indicate_semi.o)
    0x08000412   0x00000002   Code   RO          191    i.BusFault_Handler  stm32f3xx_it.o
    0x08000414   0x00000002   Code   RO          192    i.DebugMon_Handler  stm32f3xx_it.o
    0x08000416   0x0000004a   Code   RO         3575    i.DelayHalfUs       qk_common.o
    0x08000460   0x0000001c   Code   RO          193    i.EXTI9_5_IRQHandler  stm32f3xx_it.o
    0x0800047c   0x0000001c   Code   RO         1095    i.EXTI_ClearFlag    stm32f30x_exti.o
    0x08000498   0x00000024   Code   RO         1100    i.EXTI_GetITStatus  stm32f30x_exti.o
    0x080004bc   0x00000074   Code   RO         1158    i.GPIO_Init         stm32f30x_gpio.o
    0x08000530   0x0000002c   Code   RO          194    i.HRTIM1_TIMA_IRQHandler  stm32f3xx_it.o
    0x0800055c   0x00000024   Code   RO         1257    i.HRTIM_ClearITPendingBit  stm32f30x_hrtim.o
    0x08000580   0x00000042   Code   RO         1276    i.HRTIM_GetITStatus  stm32f30x_hrtim.o
    0x080005c2   0x00000002   Code   RO          195    i.HardFault_Handler  stm32f3xx_it.o
    0x080005c4   0x00000230   Code   RO         3808    i.IrDecode          qk_ir.o
    0x080007f4   0x00000002   Code   RO          196    i.MemManage_Handler  stm32f3xx_it.o
    0x080007f6   0x00000002   Code   RO          197    i.NMI_Handler       stm32f3xx_it.o
    0x080007f8   0x00000002   Code   RO          198    i.PendSV_Handler    stm32f3xx_it.o
    0x080007fa   0x00000002   Code   RO          199    i.SVC_Handler       stm32f3xx_it.o
    0x080007fc   0x00000002   Code   RO          200    i.SysTick_Handler   stm32f3xx_it.o
    0x080007fe   0x00000002   PAD
    0x08000800   0x00000068   Code   RO          294    i.SystemInit        system_stm32f30x.o
    0x08000868   0x000000f8   Code   RO          397    i.TASK_Adc          task.o
    0x08000960   0x0000000c   Code   RO          401    i.TASK_GetTimeCnt   task.o
    0x0800096c   0x00000084   Code   RO          405    i.TASK_Periodicity  task.o
    0x080009f0   0x00000160   Code   RO          406    i.TASK_Pwm          task.o
    0x08000b50   0x00000044   Code   RO          407    i.TASK_PwmShutDown  task.o
    0x08000b94   0x00000074   Code   RO          409    i.TASK_ScanKey      task.o
    0x08000c08   0x00000032   Code   RO          410    i.TASK_Send2Nums    task.o
    0x08000c3a   0x00000002   PAD
    0x08000c3c   0x00000018   Code   RO          201    i.TIM1_UP_TIM16_IRQHandler  stm32f3xx_it.o
    0x08000c54   0x000000c0   Code   RO         3491    i.UART_RxdIsr       uart.o
    0x08000d14   0x00000070   Code   RO         3493    i.UART_SendData     uart.o
    0x08000d84   0x00000044   Code   RO         3495    i.UART_TxdIsr       uart.o
    0x08000dc8   0x0000003c   Code   RO          202    i.USART2_IRQHandler  stm32f3xx_it.o
    0x08000e04   0x00000004   Code   RO         3118    i.USART_ClearFlag   stm32f30x_usart.o
    0x08000e08   0x0000003a   Code   RO         3131    i.USART_GetITStatus  stm32f30x_usart.o
    0x08000e42   0x00000002   Code   RO          203    i.UsageFault_Handler  stm32f3xx_it.o
    0x08000e44   0x00000050   Code   RO          416    i.intSqrt           task.o
    0x08000e94   0x00000002   Code   RO           12    i.main              main.o
    0x08000e96   0x00000002   PAD
    0x08000e98   0x0000004c   Code   RO          417    i.mean1             task.o
    0x08000ee4   0x0000004c   Code   RO          418    i.mean2             task.o
    0x08000f30   0x0000004c   Code   RO          419    i.mean3             task.o
    0x08000f7c   0x0000000a   Code   RO         4560    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08000f86   0x00000002   PAD
    0x08000f88   0x00000020   Data   RO         4621    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00001dd0, Max: 0x00004000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000068   Data   RW          422    .data               task.o
    0x20000068   0x00000028   Data   RW         3497    .data               uart.o
    0x20000090   0x00000010   Data   RW         3810    .data               qk_ir.o
    0x200000a0   0x00000adc   Zero   RW          421    .bss                task.o
    0x20000b7c   0x000005f0   Zero   RW         3496    .bss                uart.o
    0x2000116c   0x00000060   Zero   RW         4480    .bss                c_w.l(libspace.o)
    0x200011cc   0x00000004   PAD
    0x200011d0   0x00000400   Zero   RW            2    HEAP                startup_stm32f334x8.o
    0x200015d0   0x00000800   Zero   RW            1    STACK               startup_stm32f334x8.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         2          0          0          0          0     328759   main.o
        74          0          0          0          0        514   qk_common.o
       560         24          0         16          0       1534   qk_ir.o
        64         26        392          0       3072        880   startup_stm32f334x8.o
        64         10          0          0          0       1192   stm32f30x_exti.o
       116          0          0          0          0       1164   stm32f30x_gpio.o
       102         12          0          0          0       2850   stm32f30x_hrtim.o
        62          0          0          0          0       2070   stm32f30x_usart.o
       174         22          0          0          0       6263   stm32f3xx_it.o
       104         22          0          0          0        501   system_stm32f30x.o
      1286         98          0        104       2780      10122   task.o
       372         34          0         40       1520       4256   uart.o

    ----------------------------------------------------------------------
      2986        <USER>        <GROUP>        160       7372     360105   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        12          0          0          0          0         72   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        72          0          0          0          0         76   llsdiv.o
       238          0          0          0          0        100   lludivv7m.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
       598         <USER>          <GROUP>          0        100        868   Library Totals
         8          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       580         16          0          0         96        752   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
       598         <USER>          <GROUP>          0        100        868   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      3584        264        424        160       7472     357613   Grand Totals
      3584        264        424        160       7472     357613   ELF Image Totals
      3584        264        424        160          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 4008 (   3.91kB)
    Total RW  Size (RW Data + ZI Data)              7632 (   7.45kB)
    Total ROM Size (Code + RO Data + RW Data)       4168 (   4.07kB)

==============================================================================

