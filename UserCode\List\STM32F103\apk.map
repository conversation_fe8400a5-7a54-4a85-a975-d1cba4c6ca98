Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    stm32f10x_vector.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(RESET) refers to stm32f10x_vector.o(STACK) for __initial_sp
    stm32f10x_vector.o(RESET) refers to stm32f10x_vector.o(.text) for Reset_Handler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.NMIException) for NMIException
    stm32f10x_vector.o(RESET) refers to driver.o(i.HardFaultException) for HardFaultException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.MemManageException) for MemManageException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.BusFaultException) for BusFaultException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.UsageFaultException) for UsageFaultException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SVCHandler) for SVCHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DebugMonitor) for DebugMonitor
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.PendSVC) for PendSVC
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SysTickHandler) for SysTickHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.WWDG_IRQHandler) for WWDG_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.PVD_IRQHandler) for PVD_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TAMPER_IRQHandler) for TAMPER_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.RTC_IRQHandler) for RTC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.FLASH_IRQHandler) for FLASH_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.RCC_IRQHandler) for RCC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel3_IRQHandler) for DMA1_Channel3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel4_IRQHandler) for DMA1_Channel4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel6_IRQHandler) for DMA1_Channel6_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel7_IRQHandler) for DMA1_Channel7_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.ADC1_2_IRQHandler) for ADC1_2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler) for USB_HP_CAN_TX_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler) for USB_LP_CAN_RX0_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.CAN_RX1_IRQHandler) for CAN_RX1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.CAN_SCE_IRQHandler) for CAN_SCE_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI9_5_IRQHandler) for EXTI9_5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM1_BRK_IRQHandler) for TIM1_BRK_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.TIM1_UP_IRQHandler) for TIM1_UP_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler) for TIM1_TRG_COM_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM1_CC_IRQHandler) for TIM1_CC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C1_EV_IRQHandler) for I2C1_EV_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C1_ER_IRQHandler) for I2C1_ER_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C2_EV_IRQHandler) for I2C2_EV_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C2_ER_IRQHandler) for I2C2_ER_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SPI1_IRQHandler) for SPI1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SPI2_IRQHandler) for SPI2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.USART2_IRQHandler) for USART2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.EXTI15_10_IRQHandler) for EXTI15_10_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.RTCAlarm_IRQHandler) for RTCAlarm_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USBWakeUp_IRQHandler) for USBWakeUp_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_BRK_IRQHandler) for TIM8_BRK_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_UP_IRQHandler) for TIM8_UP_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler) for TIM8_TRG_COM_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_CC_IRQHandler) for TIM8_CC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.ADC3_IRQHandler) for ADC3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.FSMC_IRQHandler) for FSMC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.TIM5_IRQHandler) for TIM5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SPI3_IRQHandler) for SPI3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM6_IRQHandler) for TIM6_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel1_IRQHandler) for DMA2_Channel1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel2_IRQHandler) for DMA2_Channel2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel3_IRQHandler) for DMA2_Channel3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler) for DMA2_Channel4_5_IRQHandler
    stm32f10x_vector.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(.text) refers to main.o(i.SystemInit) for SystemInit
    stm32f10x_vector.o(.text) refers to __main.o(!!!main) for __main
    stm32f10x_vector.o(.text) refers to stm32f10x_vector.o(HEAP) for Heap_Mem
    stm32f10x_vector.o(.text) refers to stm32f10x_vector.o(STACK) for Stack_Mem
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_nvic.o(i.NVIC_BASEPRICONFIG) refers to cortexm3_macro.o(.text) for __BASEPRICONFIG
    stm32f10x_nvic.o(i.NVIC_GetBASEPRI) refers to cortexm3_macro.o(.text) for __GetBASEPRI
    stm32f10x_nvic.o(i.NVIC_RESETFAULTMASK) refers to cortexm3_macro.o(.text) for __RESETFAULTMASK
    stm32f10x_nvic.o(i.NVIC_RESETPRIMASK) refers to cortexm3_macro.o(.text) for __RESETPRIMASK
    stm32f10x_nvic.o(i.NVIC_SETFAULTMASK) refers to cortexm3_macro.o(.text) for __SETFAULTMASK
    stm32f10x_nvic.o(i.NVIC_SETPRIMASK) refers to cortexm3_macro.o(.text) for __SETPRIMASK
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode) refers to cortexm3_macro.o(.text) for __WFI
    stm32f10x_pwr.o(i.PWR_EnterSTOPMode) refers to cortexm3_macro.o(.text) for __WFI
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.constdata) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(.data) for StartUpCounter
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.delay) for delay
    main.o(i.SystemInit) refers to driver.o(i.DelayInit) for DelayInit
    main.o(i.SystemInit) refers to driver.o(i.DelayMs) for DelayMs
    main.o(i.SystemInit) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    main.o(i.SystemInit) refers to driver.o(.data) for SystemCoreClock
    main.o(i.main) refers to driver.o(i.CloclkInit) for CloclkInit
    main.o(i.main) refers to driver.o(i.CommonConfig) for CommonConfig
    main.o(i.main) refers to task.o(i.TASK_Init) for TASK_Init
    main.o(i.main) refers to task.o(i.TASK_Run) for TASK_Run
    stm32f10x_it.o(i.ADC1_2_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.ADC3_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.BusFaultException) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.CAN_RX1_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.CAN_SCE_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel1_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel2_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel3_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel4_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel5_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel6_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel7_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel1_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel2_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel3_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DebugMonitor) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI15_10_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI1_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI2_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI3_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI4_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI9_5_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.FLASH_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.FSMC_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.HardFaultException) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C1_ER_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C1_EV_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C2_ER_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C2_EV_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.MemManageException) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.NMIException) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.PVD_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.PendSVC) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.RCC_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.RTCAlarm_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.RTC_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SDIO_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SPI1_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SPI2_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SPI3_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SVCHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SysTickHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TAMPER_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_BRK_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_CC_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM4_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM5_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM6_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM7_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_BRK_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_CC_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_UP_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.UART4_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.UART5_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USART1_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USART2_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USART3_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USBWakeUp_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.UsageFaultException) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.WWDG_IRQHandler) refers to driver.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    apk.o(i.APK_Common) refers to cmd.o(i.CMD_MainTask) for CMD_MainTask
    apk.o(i.APK_Continuous) refers to apk.o(.data) for ptrApkTask
    apk.o(i.APK_Ctrl) refers to apk.o(i.APK_VoltCurrCalc) for APK_VoltCurrCalc
    apk.o(i.APK_Init) refers to driver.o(i.SPI_FLASH_Init) for SPI_FLASH_Init
    apk.o(i.APK_Init) refers to xfont.o(i.XFONT_Init) for XFONT_Init
    apk.o(i.APK_Init) refers to ir.o(i.IR_Init) for IR_Init
    apk.o(i.APK_Init) refers to uart.o(i.UART_Init) for UART_Init
    apk.o(i.APK_Init) refers to cmd.o(i.CMD_Init) for CMD_Init
    apk.o(i.APK_Init) refers to driver.o(i.PwmInit) for PwmInit
    apk.o(i.APK_Init) refers to driver.o(i.LedInit) for LedInit
    apk.o(i.APK_Init) refers to driver.o(i.AdcInit) for AdcInit
    apk.o(i.APK_Init) refers to driver.o(i.DacInit) for DacInit
    apk.o(i.APK_Init) refers to uart.o(i.UART_SendStr) for UART_SendStr
    apk.o(i.APK_Init) refers to lcd.o(i.LCD_Init) for LCD_Init
    apk.o(i.APK_Init) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    apk.o(i.APK_Init) refers to driver.o(i.LCD_SetBright) for LCD_SetBright
    apk.o(i.APK_Init) refers to driver.o(i.TP_Init) for TP_Init
    apk.o(i.APK_Init) refers to apk.o(i.Apk_Main) for Apk_Main
    apk.o(i.APK_Init) refers to apk.o(.data) for ptrApkTask
    apk.o(i.APK_Init) refers to task.o(.data) for TaskTimeCntNext
    apk.o(i.APK_Init) refers to apk.o(i.APK_Ctrl) for APK_Ctrl
    apk.o(i.APK_Init) refers to driver.o(.data) for PwmIsr
    apk.o(i.APK_Jump) refers to apk.o(.data) for ptrApkTask
    apk.o(i.APK_Jump2Pre) refers to apk.o(.data) for ptrApkTaskPre
    apk.o(i.APK_Mean1) refers to apk.o(.data) for sum
    apk.o(i.APK_Mean1) refers to apk.o(.bss) for buffer
    apk.o(i.APK_Mean2) refers to apk.o(.data) for sum
    apk.o(i.APK_Mean2) refers to apk.o(.bss) for buffer
    apk.o(i.APK_Periodicity) refers to apk.o(i.APK_Ctrl) for APK_Ctrl
    apk.o(i.APK_Periodicity) refers to apk.o(.data) for cnt
    apk.o(i.APK_VoltCurrCalc) refers to apk.o(.bss) for B_Curr
    apk.o(i.Apk_Main) refers to apk.o(i.APK_Common) for APK_Common
    apk.o(i.Apk_Main) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    apk.o(i.Apk_Main) refers to debug.o(i.DEBUG_InitPara) for DEBUG_InitPara
    apk.o(i.Apk_Main) refers to xfont.o(i.XFONT_SeleFont) for XFONT_SeleFont
    apk.o(i.Apk_Main) refers to debug.o(i.DEBUG_SetPara) for DEBUG_SetPara
    apk.o(i.Apk_Main) refers to lcd.o(i.LCD_PutStrCenter) for LCD_PutStrCenter
    apk.o(i.Apk_Main) refers to task.o(.data) for TaskTimeCnt
    driver.o(i.AdcInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.AdcInit) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    driver.o(i.AdcInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig) for ADC_InjectedSequencerLengthConfig
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_InjectedChannelConfig) for ADC_InjectedChannelConfig
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd) for ADC_AutoInjectedConvCmd
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    driver.o(i.AdcInit) refers to driver.o(.data) for SystemCoreClock
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_DeInit) for RCC_DeInit
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_HSEConfig) for RCC_HSEConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PLLCmd) for RCC_PLLCmd
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    driver.o(i.CloclkInit) refers to stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd) for FLASH_PrefetchBufferCmd
    driver.o(i.CloclkInit) refers to stm32f10x_flash.o(i.FLASH_SetLatency) for FLASH_SetLatency
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_HCLKConfig) for RCC_HCLKConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PCLK2Config) for RCC_PCLK2Config
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PCLK1Config) for RCC_PCLK1Config
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PLLConfig) for RCC_PLLConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_SYSCLKConfig) for RCC_SYSCLKConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_GetSYSCLKSource) for RCC_GetSYSCLKSource
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.CloclkInit) refers to driver.o(i.DelayInit) for DelayInit
    driver.o(i.CloclkInit) refers to driver.o(.data) for SystemCoreClock
    driver.o(i.CommonConfig) refers to stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    driver.o(i.CommonConfig) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    driver.o(i.CommonConfig) refers to driver.o(i.GetCpuId) for GetCpuId
    driver.o(i.CommonConfig) refers to driver.o(.data) for SPI_InitFlag
    driver.o(i.DacInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.DacInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.DacInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.DacInit) refers to stm32f10x_dac.o(i.DAC_Init) for DAC_Init
    driver.o(i.DacInit) refers to stm32f10x_dac.o(i.DAC_Cmd) for DAC_Cmd
    driver.o(i.DelayInit) refers to driver.o(.data) for SystemCoreClock
    driver.o(i.DelayMs) refers to driver.o(.data) for fac_ms
    driver.o(i.DelayUs) refers to driver.o(.data) for fac_us
    driver.o(i.EXTI15_10_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    driver.o(i.EXTI15_10_IRQHandler) refers to driver.o(.data) for IrExtiIsr
    driver.o(i.GetCpuId) refers to driver.o(.bss) for CpuId
    driver.o(i.GetDelayTimeFlag) refers to driver.o(.data) for DelayTimeFlag
    driver.o(i.HardFaultException) refers to lcd.o(i.LCD_SelectFont) for LCD_SelectFont
    driver.o(i.HardFaultException) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    driver.o(i.HardFaultException) refers to lcd.o(i.LCD_PutStrCenter) for LCD_PutStrCenter
    driver.o(i.HardFaultException) refers to driver.o(i.LCD_SetBright) for LCD_SetBright
    driver.o(i.HardFaultException) refers to font.o(.constdata) for FONT_32
    driver.o(i.I2C_ByteWrite) refers to driver.o(i.I2C_Start) for I2C_Start
    driver.o(i.I2C_ByteWrite) refers to driver.o(i.I2C_SendByte) for I2C_SendByte
    driver.o(i.I2C_ByteWrite) refers to driver.o(i.I2C_Stop) for I2C_Stop
    driver.o(i.I2C_GpioInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.I2C_GpioInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.I2C_ReceiveByte) refers to driver.o(i.I2C_Wait) for I2C_Wait
    driver.o(i.I2C_SendAck) refers to driver.o(i.I2C_Wait) for I2C_Wait
    driver.o(i.I2C_SendByte) refers to driver.o(i.I2C_Wait) for I2C_Wait
    driver.o(i.I2C_Start) refers to driver.o(i.I2C_Wait) for I2C_Wait
    driver.o(i.I2C_Stop) refers to driver.o(i.I2C_Wait) for I2C_Wait
    driver.o(i.I2C_Wait) refers to driver.o(i.DelayUs) for DelayUs
    driver.o(i.ILI9481Init) refers to driver.o(i.DelayMs) for DelayMs
    driver.o(i.IrInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.IrInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.IrInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.IrInit) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    driver.o(i.IrInit) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    driver.o(i.IrInit) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.IrInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.IrInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.IrInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.IrInit) refers to driver.o(.data) for IrExtiIsr
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    driver.o(i.LCD_BackLightInit) refers to driver.o(i.LCD_SetBright) for LCD_SetBright
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    driver.o(i.LCD_BackLightInit) refers to driver.o(.data) for SystemCoreClock
    driver.o(i.LCD_InitGpio) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.LCD_InitGpio) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.LCD_InitGpio) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    driver.o(i.LCD_WriteArrayFromXflash) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    driver.o(i.LCD_WriteArrayFromXflash) refers to xflash.o(.ARM.__AT_0x20001000) for XFLASH_TempBuffer
    driver.o(i.LedInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.OnChipFlashWritePageData) refers to stm32f10x_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    driver.o(i.OnChipFlashWritePageData) refers to stm32f10x_flash.o(i.FLASH_ClearFlag) for FLASH_ClearFlag
    driver.o(i.OnChipFlashWritePageData) refers to stm32f10x_flash.o(i.FLASH_ErasePage) for FLASH_ErasePage
    driver.o(i.OnChipFlashWritePageData) refers to stm32f10x_flash.o(i.FLASH_ProgramWord) for FLASH_ProgramWord
    driver.o(i.OnChipFlashWritePageData) refers to stm32f10x_flash.o(i.FLASH_Lock) for FLASH_Lock
    driver.o(i.PwmInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.PwmInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.PwmInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_BDTRConfig) for TIM_BDTRConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.PwmInit) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.PwmInit) refers to driver.o(.data) for PwmIsr
    driver.o(i.SPI_FLASH_EraseSector) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_EraseSector) refers to driver.o(.data) for SPI_FLASH_InitFlag
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.Spi3Init) for Spi3Init
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.DelayUs) for DelayUs
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.SPI_FLASH_ReadId) for SPI_FLASH_ReadId
    driver.o(i.SPI_FLASH_Init) refers to driver.o(.data) for SPI_FLASH_InitFlag
    driver.o(i.SPI_FLASH_ReadData) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_ReadData) refers to driver.o(.data) for SPI_FLASH_InitFlag
    driver.o(i.SPI_FLASH_ReadId) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_ReadId) refers to driver.o(.data) for XFlashId
    driver.o(i.SPI_FLASH_WriteSector) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_WriteSector) refers to driver.o(.data) for SPI_FLASH_InitFlag
    driver.o(i.SetDelayTimeUs) refers to driver.o(.data) for DelayTimeFlag
    driver.o(i.Spi3Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.Spi3Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.Spi3Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.Spi3Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    driver.o(i.Spi3Init) refers to stm32f10x_spi.o(i.SPI_Init) for SPI_Init
    driver.o(i.Spi3Init) refers to stm32f10x_spi.o(i.SPI_Cmd) for SPI_Cmd
    driver.o(i.Spi3Init) refers to driver.o(.data) for SPI_InitFlag
    driver.o(i.TASK_TimerInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    driver.o(i.TASK_TimerInit) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.TASK_TimerInit) refers to driver.o(.data) for SystemCoreClock
    driver.o(i.TIM1_UP_IRQHandler) refers to driver.o(.data) for PwmIsr
    driver.o(i.TIM5_IRQHandler) refers to driver.o(.data) for TaskTimerIsr
    driver.o(i.TP_Init) refers to driver.o(i.Spi3Init) for Spi3Init
    driver.o(i.TP_Init) refers to driver.o(.data) for Cal_kx
    driver.o(i.TP_Init) refers to driver.o(.bss) for TP_TouchPoint
    driver.o(i.TP_ReadReg) refers to driver.o(i.I2C_Start) for I2C_Start
    driver.o(i.TP_ReadReg) refers to driver.o(i.I2C_SendByte) for I2C_SendByte
    driver.o(i.TP_ReadReg) refers to driver.o(i.I2C_ReceiveByte) for I2C_ReceiveByte
    driver.o(i.TP_ReadReg) refers to driver.o(i.I2C_SendAck) for I2C_SendAck
    driver.o(i.TP_ReadReg) refers to driver.o(i.I2C_Stop) for I2C_Stop
    driver.o(i.TP_TouchScan) refers to driver.o(i.TP_ResGetAd) for TP_ResGetAd
    driver.o(i.TP_TouchScan) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    driver.o(i.TP_TouchScan) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    driver.o(i.TP_TouchScan) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    driver.o(i.TP_TouchScan) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    driver.o(i.TP_TouchScan) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    driver.o(i.TP_TouchScan) refers to driver.o(.data) for cnt
    driver.o(i.TP_TouchScan) refers to driver.o(.bss) for TP_TouchPoint
    driver.o(i.TP_WriteReg) refers to driver.o(i.I2C_Start) for I2C_Start
    driver.o(i.TP_WriteReg) refers to driver.o(i.I2C_SendByte) for I2C_SendByte
    driver.o(i.TP_WriteReg) refers to driver.o(i.I2C_Stop) for I2C_Stop
    driver.o(i.USART2_IRQHandler) refers to driver.o(.data) for Uart2TxdIsr
    driver.o(i.Uart2Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.Uart2Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.Uart2Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    driver.o(i.Uart2Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    driver.o(i.Uart2Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    driver.o(i.Uart2Init) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.Uart2Init) refers to driver.o(.data) for Uart2RxdIsr
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd) for IWDG_WriteAccessCmd
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_SetPrescaler) for IWDG_SetPrescaler
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_SetReload) for IWDG_SetReload
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_ReloadCounter) for IWDG_ReloadCounter
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_Enable) for IWDG_Enable
    cmd.o(i.CMD_Init) refers to uart.o(i.UART_ClearRxdBuffer) for UART_ClearRxdBuffer
    cmd.o(i.CMD_Init) refers to cmd.o(.data) for timeout_cnt
    cmd.o(i.CMD_Init) refers to cmd.o(.bss) for CMD_Pack
    cmd.o(i.CMD_MainTask) refers to uart.o(i.UART_GetRxdData) for UART_GetRxdData
    cmd.o(i.CMD_MainTask) refers to uart.o(i.UART_GetRxdFifoLen) for UART_GetRxdFifoLen
    cmd.o(i.CMD_MainTask) refers to cmd.o(i.CMD_PackCheck) for CMD_PackCheck
    cmd.o(i.CMD_MainTask) refers to cmd.o(i.CMD_PackRun) for CMD_PackRun
    cmd.o(i.CMD_MainTask) refers to cmd.o(.data) for PackTimeout
    cmd.o(i.CMD_MainTask) refers to cmd.o(.bss) for CMD_Pack
    cmd.o(i.CMD_PackRun) refers to uart.o(i.UART_SendStr) for UART_SendStr
    cmd.o(i.CMD_PackRun) refers to xflash.o(i.XFLASH_GetDataFromUart) for XFLASH_GetDataFromUart
    cmd.o(i.CMD_PackRun) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    cmd.o(i.CMD_PackRun) refers to lcd.o(i.LCD_PutStrCenter) for LCD_PutStrCenter
    cmd.o(i.CMD_PackRun) refers to stm32f10x_nvic.o(i.NVIC_GenerateSystemReset) for NVIC_GenerateSystemReset
    cmd.o(i.CMD_PackRun) refers to cmd.o(.bss) for CMD_Pack
    common.o(i.Str2Double) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    common.o(i.Str2Double) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    common.o(i.Str2Double) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    common.o(i.Str2Double) refers to pow.o(i.pow) for pow
    ir.o(i.IR_Decode) refers to ir.o(.data) for IR_start
    ir.o(i.IR_Init) refers to driver.o(i.IrInit) for IrInit
    ir.o(i.IR_Init) refers to ir.o(i.IR_Decode) for IR_Decode
    ir.o(i.IR_Init) refers to driver.o(.data) for IrExtiIsr
    ir.o(i.IR_Init) refers to ir.o(.data) for IR_LedTimeout
    lcd.o(i.LCD_Clear) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_Clear) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_Clear) refers to driver.o(i.LCD_WriteConst) for LCD_WriteConst
    lcd.o(i.LCD_Clear) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_DrawBmpFromRawData) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_DrawBmpFromRawData) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_DrawBmpFromRawData) refers to driver.o(i.LCD_WriteArray) for LCD_WriteArray
    lcd.o(i.LCD_DrawBmpFromRawData) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.LCD_WriteArrayFromXflash) for LCD_WriteArrayFromXflash
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to xflash.o(.ARM.__AT_0x20001000) for XFLASH_TempBuffer
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to touch.o(.data) for TouchRectInfoNum
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to touch.o(.bss) for TouchRectInfo
    lcd.o(i.LCD_DrawChildPic) refers to lcd.o(i.LCD_DrawBmpFromXflashPackData) for LCD_DrawBmpFromXflashPackData
    lcd.o(i.LCD_DrawChildPic) refers to lcd.o(.data) for PageId
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_DrawLine) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_DrawPage) refers to lcd.o(i.LCD_DrawBmpFromXflashPackData) for LCD_DrawBmpFromXflashPackData
    lcd.o(i.LCD_DrawPageCenter) refers to lcd.o(i.LCD_DrawBmpFromXflashPackData) for LCD_DrawBmpFromXflashPackData
    lcd.o(i.LCD_DrawPoint) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_DrawPoint) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_DrawProgress) refers to lcd.o(i.LCD_SetBar) for LCD_SetBar
    lcd.o(i.LCD_DrawProgress) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_GetBar) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_Init) refers to driver.o(i.LCD_InitGpio) for LCD_InitGpio
    lcd.o(i.LCD_Init) refers to driver.o(i.ILI9481Init) for ILI9481Init
    lcd.o(i.LCD_Init) refers to driver.o(i.LCD_BackLightInit) for LCD_BackLightInit
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_SelectFont) for LCD_SelectFont
    lcd.o(i.LCD_Init) refers to lcd.o(.data) for PageOffsetX
    lcd.o(i.LCD_Init) refers to font.o(.constdata) for FONT_32
    lcd.o(i.LCD_PutChar) refers to lcd.o(i.LCD_SearchFont) for LCD_SearchFont
    lcd.o(i.LCD_PutChar) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_PutChar) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_PutHanzi) refers to lcd.o(i.LCD_SearchFont) for LCD_SearchFont
    lcd.o(i.LCD_PutHanzi) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_PutHanzi) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_PutStr) refers to lcd.o(i.LCD_PutHanzi) for LCD_PutHanzi
    lcd.o(i.LCD_PutStr) refers to lcd.o(i.LCD_PutChar) for LCD_PutChar
    lcd.o(i.LCD_PutStr) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_PutStrCenter) refers to common.o(i.StrLen) for StrLen
    lcd.o(i.LCD_PutStrCenter) refers to lcd.o(i.LCD_PutStr) for LCD_PutStr
    lcd.o(i.LCD_PutStrCenter) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_PutStrLeftTop) refers to lcd.o(i.LCD_PutStr) for LCD_PutStr
    lcd.o(i.LCD_PutStrRightCenter) refers to common.o(i.StrLen) for StrLen
    lcd.o(i.LCD_PutStrRightCenter) refers to lcd.o(i.LCD_PutStr) for LCD_PutStr
    lcd.o(i.LCD_PutStrRightCenter) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_SearchFont) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    lcd.o(i.LCD_SearchFont) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_SearchFont) refers to xfont.o(.bss) for XFONT_CurrHeader
    lcd.o(i.LCD_SearchFont) refers to xfont.o(.data) for FontDataLenAscii
    lcd.o(i.LCD_SelectFont) refers to lcd.o(.data) for Font_ASCII_p
    lcd.o(i.LCD_SetBar) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_SetBar) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_SetBar) refers to driver.o(i.LCD_WriteConst) for LCD_WriteConst
    lcd.o(i.LCD_SetBar) refers to lcd.o(.data) for LcdInitFlag
    lcd.o(i.LCD_SetCursor) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    touch.o(i.TOUCH_DispPos) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    touch.o(i.TOUCH_DispPos) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    touch.o(i.TOUCH_DispPos) refers to _printf_dec.o(.text) for _printf_int_dec
    touch.o(i.TOUCH_DispPos) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    touch.o(i.TOUCH_DispPos) refers to lcd.o(i.LCD_PutStrLeftTop) for LCD_PutStrLeftTop
    touch.o(i.TOUCH_DispPos) refers to noretval__2sprintf.o(.text) for __2sprintf
    touch.o(i.TOUCH_DispPos) refers to driver.o(i.TP_TouchScan) for TP_TouchScan
    touch.o(i.TOUCH_DispPos) refers to common.o(i.Num2Str) for Num2Str
    touch.o(i.TOUCH_DispPos) refers to driver.o(i.DelayMs) for DelayMs
    touch.o(i.TOUCH_DispPos) refers to driver.o(.data) for TP_TouchNum
    touch.o(i.TOUCH_DispPos) refers to driver.o(.bss) for TP_TouchPoint
    touch.o(i.TOUCH_DrawLine) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    touch.o(i.TOUCH_DrawLine) refers to driver.o(i.TP_TouchScan) for TP_TouchScan
    touch.o(i.TOUCH_DrawLine) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    touch.o(i.TOUCH_DrawLine) refers to driver.o(i.DelayMs) for DelayMs
    touch.o(i.TOUCH_DrawLine) refers to driver.o(.data) for TP_TouchNum
    touch.o(i.TOUCH_DrawLine) refers to driver.o(.bss) for TP_TouchPoint
    touch.o(i.TOUCH_GetIdByPoint) refers to touch.o(.bss) for TouchRectInfo
    touch.o(i.TOUCH_GetIdByPoint) refers to touch.o(.data) for TouchRectInfoNum
    touch.o(i.TOUCH_GetState) refers to driver.o(i.TP_TouchScan) for TP_TouchScan
    touch.o(i.TOUCH_GetState) refers to touch.o(.data) for num_last
    touch.o(i.TOUCH_GetState) refers to driver.o(.data) for TP_TouchNum
    touch.o(i.TOUCH_GetState) refers to touch.o(.bss) for TouchState
    touch.o(i.TOUCH_GetState) refers to driver.o(.bss) for TP_TouchPoint
    uart.o(i.UART_ClearRxdBuffer) refers to uart.o(.data) for RxdFifoDataFront
    uart.o(i.UART_ClearRxdBuffer) refers to uart.o(.bss) for RxdFifoData
    uart.o(i.UART_GetRxdData) refers to uart.o(.bss) for RxdFifoData
    uart.o(i.UART_GetRxdData) refers to uart.o(.data) for RxdFifoDataFront
    uart.o(i.UART_GetRxdFifoLen) refers to uart.o(.data) for RxdFifoDataRear
    uart.o(i.UART_Init) refers to driver.o(i.Uart2Init) for Uart2Init
    uart.o(i.UART_Init) refers to uart.o(.data) for UART_InitFlag
    uart.o(i.UART_Init) refers to uart.o(i.UART_RxdIsr) for UART_RxdIsr
    uart.o(i.UART_Init) refers to driver.o(.data) for Uart2RxdIsr
    uart.o(i.UART_Init) refers to uart.o(i.UART_TxdIsr) for UART_TxdIsr
    uart.o(i.UART_RxdIsr) refers to uart.o(i.UART_GetRxdFifoLen) for UART_GetRxdFifoLen
    uart.o(i.UART_RxdIsr) refers to uart.o(.bss) for RxdFifoData
    uart.o(i.UART_RxdIsr) refers to uart.o(.data) for RxdFifoDataRear
    uart.o(i.UART_SendData) refers to uart.o(i.UART_TxdIsr) for UART_TxdIsr
    uart.o(i.UART_SendData) refers to uart.o(.data) for UART_InitFlag
    uart.o(i.UART_SendData) refers to uart.o(.bss) for TxdFifoData
    uart.o(i.UART_SendStr) refers to uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.UART_TxdIsr) refers to uart.o(.data) for TxdFifoDataRear
    uart.o(i.UART_TxdIsr) refers to uart.o(.bss) for TxdFifoData
    xflash.o(i.XFLASH_GetDataFromUart) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    xflash.o(i.XFLASH_GetDataFromUart) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    xflash.o(i.XFLASH_GetDataFromUart) refers to _printf_dec.o(.text) for _printf_int_dec
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_SelectFont) for LCD_SelectFont
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_PutStrLeftTop) for LCD_PutStrLeftTop
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.DelayMs) for DelayMs
    xflash.o(i.XFLASH_GetDataFromUart) refers to noretval__2sprintf.o(.text) for __2sprintf
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_DrawProgress) for LCD_DrawProgress
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.DelayUs) for DelayUs
    xflash.o(i.XFLASH_GetDataFromUart) refers to common.o(i.LrcCalc) for LrcCalc
    xflash.o(i.XFLASH_GetDataFromUart) refers to uart.o(i.UART_SendData) for UART_SendData
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.OnChipFlashWritePageData) for OnChipFlashWritePageData
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.OnChipFlashReadData) for OnChipFlashReadData
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(i.XFLASH_WriteData) for XFLASH_WriteData
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    xflash.o(i.XFLASH_GetDataFromUart) refers to font.o(.constdata) for FONT_32
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(.data) for UartRxdFlag
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(.bss) for UartRxdDataBuffer1
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(.data) for Uart2RxdIsr
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(i.XFLASH_UartRxdIsr) for XFLASH_UartRxdIsr
    xflash.o(i.XFLASH_GetDataFromUart) refers to uart.o(.data) for UART_BdRate
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(.ARM.__AT_0x20001000) for XFLASH_TempBuffer
    xflash.o(i.XFLASH_GetDataFromUart) refers to common.o(i.Num2Str) for Num2Str
    xflash.o(i.XFLASH_UartRxdIsr) refers to xflash.o(.data) for UartRxdFlag
    xflash.o(i.XFLASH_UartRxdIsr) refers to xflash.o(.bss) for UartRxdDataBuffer1
    xflash.o(i.XFLASH_WriteData) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    xflash.o(i.XFLASH_WriteData) refers to driver.o(i.SPI_FLASH_EraseSector) for SPI_FLASH_EraseSector
    xflash.o(i.XFLASH_WriteData) refers to driver.o(i.SPI_FLASH_WriteSector) for SPI_FLASH_WriteSector
    xflash.o(i.XFLASH_WriteData) refers to xflash.o(.ARM.__AT_0x20001000) for XFLASH_TempBuffer
    xfont.o(i.XFONT_GetFontInf) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    xfont.o(i.XFONT_GetFontInf) refers to xfont.o(.bss) for XFONT_Inf
    xfont.o(i.XFONT_GetFontInf) refers to xfont.o(.data) for XFONT_FontNum
    xfont.o(i.XFONT_Init) refers to xfont.o(i.XFONT_GetFontInf) for XFONT_GetFontInf
    xfont.o(i.XFONT_Init) refers to xfont.o(i.XFONT_SeleFont) for XFONT_SeleFont
    xfont.o(i.XFONT_SeleFont) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    xfont.o(i.XFONT_SeleFont) refers to lcd.o(.data) for FontExternalFlag
    xfont.o(i.XFONT_SeleFont) refers to xfont.o(.bss) for XFONT_Inf
    xfont.o(i.XFONT_SeleFont) refers to xfont.o(.data) for FontDataLenAscii
    task.o(i.TASK_Init) refers to apk.o(i.APK_Init) for APK_Init
    task.o(i.TASK_Init) refers to driver.o(i.TASK_TimerInit) for TASK_TimerInit
    task.o(i.TASK_Init) refers to task.o(.data) for TaskTimeCnt
    task.o(i.TASK_Init) refers to task.o(i.TASK_Periodicity) for TASK_Periodicity
    task.o(i.TASK_Init) refers to driver.o(.data) for TaskTimerIsr
    task.o(i.TASK_Periodicity) refers to apk.o(i.APK_Periodicity) for APK_Periodicity
    task.o(i.TASK_Periodicity) refers to task.o(.data) for TaskTimeCnt
    task.o(i.TASK_Run) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    task.o(i.TASK_Run) refers to stm32f10x_nvic.o(i.NVIC_RESETPRIMASK) for NVIC_RESETPRIMASK
    task.o(i.TASK_Run) refers to apk.o(i.APK_Continuous) for APK_Continuous
    debug.o(i.DEBUG_ClearWave) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    debug.o(i.DEBUG_ClearWave) refers to debug.o(.data) for WaveBufferIndex
    debug.o(i.DEBUG_ClearWave) refers to debug.o(.bss) for WaveBuffer
    debug.o(i.DEBUG_DisplayWave) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    debug.o(i.DEBUG_DisplayWave) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    debug.o(i.DEBUG_DisplayWave) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    debug.o(i.DEBUG_DisplayWave) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    debug.o(i.DEBUG_DisplayWave) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    debug.o(i.DEBUG_DisplayWave) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    debug.o(i.DEBUG_DisplayWave) refers to debug.o(.data) for WaveBufferIndex
    debug.o(i.DEBUG_DisplayWave) refers to debug.o(.bss) for AnalogData
    debug.o(i.DEBUG_InitPara) refers to debug.o(i.LoadData) for LoadData
    debug.o(i.DEBUG_InitPara) refers to debug.o(i.InitFun) for InitFun
    debug.o(i.DEBUG_SetPara) refers to lcd.o(i.LCD_SetBar) for LCD_SetBar
    debug.o(i.DEBUG_SetPara) refers to lcd.o(i.LCD_PutStrLeftTop) for LCD_PutStrLeftTop
    debug.o(i.DEBUG_SetPara) refers to debug.o(.constdata) for VarMenu
    debug.o(i.DEBUG_SetPara) refers to debug.o(.bss) for VarMenuBuffer
    debug.o(i.DEBUG_SetPara) refers to lcd.o(.data) for Font_ASCII_WIDTH
    debug.o(i.DEBUG_SetPara) refers to debug.o(.data) for menu_p
    debug.o(i.DEBUG_SetPara) refers to debug.o(i.MenuStrLen) for MenuStrLen
    debug.o(i.DEBUG_SetPara) refers to debug.o(i.MenuDisplayNumber) for MenuDisplayNumber
    debug.o(i.DEBUG_SetPara) refers to debug.o(i.GetKeyValue) for GetKeyValue
    debug.o(i.DEBUG_SetPara) refers to debug.o(i.SaveData) for SaveData
    debug.o(i.DEBUG_SetPara) refers to debug.o(i.UpdateOneTime) for UpdateOneTime
    debug.o(i.DEBUG_SetPara) refers to debug.o(i.UpdatePeriod) for UpdatePeriod
    debug.o(i.DEBUG_SetPara) refers to cmd.o(i.CMD_MainTask) for CMD_MainTask
    debug.o(i.DEBUG_SetPara) refers to task.o(.data) for TaskTimeCnt
    debug.o(i.FlashWriteData) refers to stm32f10x_flash.o(i.FLASH_ProgramWord) for FLASH_ProgramWord
    debug.o(i.FlashWriteEnd) refers to stm32f10x_flash.o(i.FLASH_ClearFlag) for FLASH_ClearFlag
    debug.o(i.FlashWriteEnd) refers to stm32f10x_flash.o(i.FLASH_Lock) for FLASH_Lock
    debug.o(i.FlashWriteStart) refers to stm32f10x_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    debug.o(i.FlashWriteStart) refers to stm32f10x_flash.o(i.FLASH_ClearFlag) for FLASH_ClearFlag
    debug.o(i.FlashWriteStart) refers to stm32f10x_flash.o(i.FLASH_ErasePage) for FLASH_ErasePage
    debug.o(i.GetKeyValue) refers to driver.o(i.DelayHalfUs) for DelayHalfUs
    debug.o(i.GetKeyValue) refers to debug.o(.data) for key_cnt
    debug.o(i.GetKeyValue) refers to driver.o(.data) for DbValue
    debug.o(i.GetKeyValue) refers to ir.o(.data) for IR_Key
    debug.o(i.GetKeyValue) refers to apk.o(.data) for RunState
    debug.o(i.InitFun) refers to debug.o(i.changeFun_PwmFreq) for changeFun_PwmFreq
    debug.o(i.InitFun) refers to debug.o(i.changeFun_LcdBkLight) for changeFun_LcdBkLight
    debug.o(i.InitFun) refers to debug.o(i.changeFun_RunState) for changeFun_RunState
    debug.o(i.InitFun) refers to debug.o(i.changeFun_DacSetValue0) for changeFun_DacSetValue0
    debug.o(i.InitFun) refers to debug.o(i.changeFun_DacSetValue1) for changeFun_DacSetValue1
    debug.o(i.InitFun) refers to debug.o(i.changeFun_Duty) for changeFun_Duty
    debug.o(i.InitFun) refers to apk.o(.data) for RunState
    debug.o(i.LoadData) refers to debug.o(i.FlashReadData) for FlashReadData
    debug.o(i.LoadData) refers to debug.o(.constdata) for VarMenu
    debug.o(i.MenuDisplayNumber) refers to lcd.o(i.LCD_PutChar) for LCD_PutChar
    debug.o(i.MenuDisplayNumber) refers to lcd.o(.data) for Font_ASCII_WIDTH
    debug.o(i.SaveData) refers to debug.o(i.FlashWriteStart) for FlashWriteStart
    debug.o(i.SaveData) refers to debug.o(i.FlashWriteData) for FlashWriteData
    debug.o(i.SaveData) refers to debug.o(i.FlashWriteEnd) for FlashWriteEnd
    debug.o(i.SaveData) refers to debug.o(i.LoadData) for LoadData
    debug.o(i.SaveData) refers to debug.o(.constdata) for VarMenu
    debug.o(i.UpdatePeriod) refers to apk.o(.bss) for AdcRawData
    debug.o(i.changeFun_DacSetValue0) refers to apk.o(.data) for DacSetValue
    debug.o(i.changeFun_DacSetValue1) refers to apk.o(.data) for DacSetValue
    debug.o(i.changeFun_Duty) refers to apk.o(.bss) for Duty
    debug.o(i.changeFun_LcdBkLight) refers to driver.o(i.LCD_SetBright) for LCD_SetBright
    debug.o(i.changeFun_LcdBkLight) refers to apk.o(.data) for LcdBkLight
    debug.o(i.changeFun_PwmDead) refers to apk.o(.data) for PwmDead
    debug.o(i.changeFun_PwmFreq) refers to debug.o(i.changeFun_LcdBkLight) for changeFun_LcdBkLight
    debug.o(i.changeFun_PwmFreq) refers to driver.o(.data) for SystemCoreClock
    debug.o(i.changeFun_PwmFreq) refers to apk.o(.data) for PwmFreq
    debug.o(i.changeFun_RunState) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    debug.o(i.changeFun_RunState) refers to apk.o(.data) for RunState
    debug.o(.constdata) refers to apk.o(.data) for RunState
    debug.o(.constdata) refers to debug.o(i.changeFun_RunState) for changeFun_RunState
    debug.o(.constdata) refers to apk.o(.bss) for Duty
    debug.o(.constdata) refers to debug.o(i.changeFun_Duty) for changeFun_Duty
    debug.o(.constdata) refers to debug.o(i.changeFun_PwmFreq) for changeFun_PwmFreq
    debug.o(.constdata) refers to debug.o(i.changeFun_PwmDead) for changeFun_PwmDead
    debug.o(.constdata) refers to debug.o(i.changeFun_LcdBkLight) for changeFun_LcdBkLight
    debug.o(.constdata) refers to debug.o(i.changeFun_DacSetValue0) for changeFun_DacSetValue0
    debug.o(.constdata) refers to debug.o(i.changeFun_DacSetValue1) for changeFun_DacSetValue1
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.__pow$lsc) for __pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.__pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.__pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.__pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.__pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.__pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.__pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.__pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.__pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.__pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(i.__pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dsqrt_noumaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_noumaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to stm32f10x_vector.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    driver.o(i.SPI_FLASH_WaitBusy) refers to driver.o(i.SetDelayTimeUs) for SetDelayTimeUs
    driver.o(i.SPI_FLASH_WaitBusy) refers to driver.o(i.GetDelayTimeFlag) for GetDelayTimeFlag


==============================================================================

Removing Unused input sections from the image.

    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (96 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (4 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (16 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (16 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (196 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_BASEPRICONFIG), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_ClearIRQChannelPendingBit), (24 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_ClearSystemHandlerPendingBit), (32 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_DeInit), (68 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GenerateCoreReset), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetBASEPRI), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetCPUID), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetCurrentActiveHandler), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetCurrentPendingIRQChannel), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetFaultAddress), (32 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetFaultHandlerSources), (68 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetIRQChannelActiveBitStatus), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetIRQChannelPendingBitStatus), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetSystemHandlerActiveBitStatus), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetSystemHandlerPendingBitStatus), (48 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_RESETFAULTMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SCBDeInit), (84 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SETFAULTMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SETPRIMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SetIRQChannelPendingBit), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SetSystemHandlerPendingBit), (28 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_StructInit), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SystemHandlerConfig), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SystemHandlerPriorityConfig), (112 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (76 bytes).
    Removing stm32f10x_rcc.o(.data), (8 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (210 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (34 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (48 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (92 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (56 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (224 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (110 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (108 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (14 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (76 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (160 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (68 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (108 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_it.o(i.EXTI15_10_IRQHandler), (24 bytes).
    Removing stm32f10x_it.o(i.HardFaultException), (24 bytes).
    Removing stm32f10x_it.o(i.TIM5_IRQHandler), (24 bytes).
    Removing stm32f10x_it.o(i.USART2_IRQHandler), (24 bytes).
    Removing apk.o(i.APK_Jump), (24 bytes).
    Removing apk.o(i.APK_Jump2Pre), (20 bytes).
    Removing apk.o(i.APK_Mean1), (128 bytes).
    Removing apk.o(i.APK_Mean2), (128 bytes).
    Removing picture.o(.constdata), (1 bytes).
    Removing driver.o(.emb_text), (6 bytes).
    Removing driver.o(i.I2C_ByteWrite), (88 bytes).
    Removing driver.o(i.I2C_GpioInit), (80 bytes).
    Removing driver.o(i.I2C_ReceiveByte), (72 bytes).
    Removing driver.o(i.I2C_SendAck), (64 bytes).
    Removing driver.o(i.I2C_SendByte), (124 bytes).
    Removing driver.o(i.I2C_Start), (44 bytes).
    Removing driver.o(i.I2C_Stop), (44 bytes).
    Removing driver.o(i.I2C_Wait), (10 bytes).
    Removing driver.o(i.ILI9481ReadRamPrepare), (76 bytes).
    Removing driver.o(i.LCD_WriteArray), (120 bytes).
    Removing driver.o(i.LCD_WriteArrayFromXflash), (164 bytes).
    Removing driver.o(i.TP_ReadReg), (84 bytes).
    Removing driver.o(i.TP_ResGetAd), (172 bytes).
    Removing driver.o(i.TP_TouchScan), (356 bytes).
    Removing driver.o(i.TP_WriteReg), (76 bytes).
    Removing driver.o(i.WatchDogInit), (54 bytes).
    Removing common.o(i.IntPower), (28 bytes).
    Removing common.o(i.Str2Double), (236 bytes).
    Removing common.o(i.Str2Num), (204 bytes).
    Removing lcd.o(i.LCD_DrawBmpFromRawData), (132 bytes).
    Removing lcd.o(i.LCD_DrawBmpFromXflashPackData), (540 bytes).
    Removing lcd.o(i.LCD_DrawChildPic), (40 bytes).
    Removing lcd.o(i.LCD_DrawLine), (520 bytes).
    Removing lcd.o(i.LCD_DrawPage), (26 bytes).
    Removing lcd.o(i.LCD_DrawPageCenter), (24 bytes).
    Removing lcd.o(i.LCD_GetBar), (20 bytes).
    Removing lcd.o(i.LCD_PutStrRightCenter), (108 bytes).
    Removing lcd.o(i.LCD_ReadPoint), (20 bytes).
    Removing lcd.o(i.LCD_SetCursor), (20 bytes).
    Removing touch.o(i.TOUCH_DispPos), (736 bytes).
    Removing touch.o(i.TOUCH_DrawLine), (136 bytes).
    Removing touch.o(i.TOUCH_GetIdByPoint), (188 bytes).
    Removing touch.o(i.TOUCH_GetState), (208 bytes).
    Removing touch.o(.bss), (516 bytes).
    Removing touch.o(.data), (8 bytes).
    Removing debug.o(i.DEBUG_ClearWave), (288 bytes).
    Removing debug.o(i.DEBUG_DisplayWave), (396 bytes).

298 unused section(s) (total 14153 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_noumaal.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\\qiankunTeamLib\\driver\\driver.c     0x00000000   Number         0  driver.o ABSOLUTE
    ..\qiankunTeamLib\api\cmd.c              0x00000000   Number         0  cmd.o ABSOLUTE
    ..\qiankunTeamLib\api\common.c           0x00000000   Number         0  common.o ABSOLUTE
    ..\qiankunTeamLib\api\debug.c            0x00000000   Number         0  debug.o ABSOLUTE
    ..\qiankunTeamLib\api\font.c             0x00000000   Number         0  font.o ABSOLUTE
    ..\qiankunTeamLib\api\ir.c               0x00000000   Number         0  ir.o ABSOLUTE
    ..\qiankunTeamLib\api\lcd.c              0x00000000   Number         0  lcd.o ABSOLUTE
    ..\qiankunTeamLib\api\task.c             0x00000000   Number         0  task.o ABSOLUTE
    ..\qiankunTeamLib\api\touch.c            0x00000000   Number         0  touch.o ABSOLUTE
    ..\qiankunTeamLib\api\uart.c             0x00000000   Number         0  uart.o ABSOLUTE
    ..\qiankunTeamLib\api\xflash.c           0x00000000   Number         0  xflash.o ABSOLUTE
    ..\qiankunTeamLib\api\xfont.c            0x00000000   Number         0  xfont.o ABSOLUTE
    ..\qiankunTeamLib\driver\driver.c        0x00000000   Number         0  driver.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_adc.c  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_dac.c  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_exti.c 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_iwdg.c 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_nvic.c 0x00000000   Number         0  stm32f10x_nvic.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_pwr.c  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_rcc.c  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_spi.c  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_tim.c  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Startup\cortexm3_macro.s                 0x00000000   Number         0  cortexm3_macro.o ABSOLUTE
    Startup\stm32f10x_vector.s               0x00000000   Number         0  stm32f10x_vector.o ABSOLUTE
    User\apk.c                               0x00000000   Number         0  apk.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\picture.c                           0x00000000   Number         0  picture.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08010000   Section      304  stm32f10x_vector.o(RESET)
    !!!main                                  0x08010130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08010138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0801016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08010188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080101a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x080101a4   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x080101aa   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080101ae   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080101b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080101b0   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080101b2   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080101b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080101b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080101b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080101b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080101b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080101b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080101b4   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080101b6   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080101b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080101b6   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080101bc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080101bc   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080101c0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080101c0   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080101c8   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080101ca   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080101ca   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080101ce   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080101d4   Section      122  cortexm3_macro.o(.text)
    .text                                    0x08010250   Section       44  stm32f10x_vector.o(.text)
    .text                                    0x0801027c   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x080102a4   Section        0  __printf.o(.text)
    .text                                    0x0801030c   Section        0  _printf_dec.o(.text)
    .text                                    0x08010384   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080103e8   Section        0  heapauxi.o(.text)
    .text                                    0x080103ee   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080104a0   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080104a1   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x080104d0   Section        0  _sputc.o(.text)
    .text                                    0x080104dc   Section        8  libspace.o(.text)
    .text                                    0x080104e4   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0801052e   Section        0  exit.o(.text)
    .text                                    0x08010540   Section        0  sys_exit.o(.text)
    .text                                    0x0801054c   Section        2  use_no_semi.o(.text)
    .text                                    0x0801054e   Section        0  indicate_semi.o(.text)
    i.ADC1_2_IRQHandler                      0x08010550   Section        0  stm32f10x_it.o(i.ADC1_2_IRQHandler)
    i.ADC3_IRQHandler                        0x08010568   Section        0  stm32f10x_it.o(i.ADC3_IRQHandler)
    i.ADC_AutoInjectedConvCmd                0x08010580   Section        0  stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd)
    i.ADC_Cmd                                0x08010596   Section        0  stm32f10x_adc.o(i.ADC_Cmd)
    i.ADC_GetCalibrationStatus               0x080105ac   Section        0  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    i.ADC_GetResetCalibrationStatus          0x080105c0   Section        0  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    i.ADC_Init                               0x080105d4   Section        0  stm32f10x_adc.o(i.ADC_Init)
    i.ADC_InjectedChannelConfig              0x08010624   Section        0  stm32f10x_adc.o(i.ADC_InjectedChannelConfig)
    i.ADC_InjectedSequencerLengthConfig      0x080106a6   Section        0  stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig)
    i.ADC_ResetCalibration                   0x080106be   Section        0  stm32f10x_adc.o(i.ADC_ResetCalibration)
    i.ADC_SoftwareStartConvCmd               0x080106c8   Section        0  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    i.ADC_StartCalibration                   0x080106de   Section        0  stm32f10x_adc.o(i.ADC_StartCalibration)
    i.APK_Common                             0x080106e8   Section        0  apk.o(i.APK_Common)
    i.APK_Continuous                         0x080106f0   Section        0  apk.o(i.APK_Continuous)
    i.APK_Ctrl                               0x08010700   Section        0  apk.o(i.APK_Ctrl)
    i.APK_Init                               0x08010708   Section        0  apk.o(i.APK_Init)
    i.APK_Periodicity                        0x08010798   Section        0  apk.o(i.APK_Periodicity)
    i.APK_VoltCurrCalc                       0x080107d0   Section        0  apk.o(i.APK_VoltCurrCalc)
    i.AdcInit                                0x08010898   Section        0  driver.o(i.AdcInit)
    i.Apk_Main                               0x08010ab0   Section        0  apk.o(i.Apk_Main)
    i.BusFaultException                      0x08010b2c   Section        0  stm32f10x_it.o(i.BusFaultException)
    i.CAN_RX1_IRQHandler                     0x08010b44   Section        0  stm32f10x_it.o(i.CAN_RX1_IRQHandler)
    i.CAN_SCE_IRQHandler                     0x08010b5c   Section        0  stm32f10x_it.o(i.CAN_SCE_IRQHandler)
    i.CMD_Init                               0x08010b74   Section        0  cmd.o(i.CMD_Init)
    i.CMD_MainTask                           0x08010ba8   Section        0  cmd.o(i.CMD_MainTask)
    i.CMD_PackCheck                          0x08010cf4   Section        0  cmd.o(i.CMD_PackCheck)
    i.CMD_PackRun                            0x08010d28   Section        0  cmd.o(i.CMD_PackRun)
    i.CloclkInit                             0x08010ec4   Section        0  driver.o(i.CloclkInit)
    i.CommonConfig                           0x08010f68   Section        0  driver.o(i.CommonConfig)
    i.DAC_Cmd                                0x08010f98   Section        0  stm32f10x_dac.o(i.DAC_Cmd)
    i.DAC_Init                               0x08010fc0   Section        0  stm32f10x_dac.o(i.DAC_Init)
    i.DEBUG_InitPara                         0x08010ff4   Section        0  debug.o(i.DEBUG_InitPara)
    i.DEBUG_SetPara                          0x08011000   Section        0  debug.o(i.DEBUG_SetPara)
    i.DMA1_Channel1_IRQHandler               0x08012c88   Section        0  stm32f10x_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA1_Channel2_IRQHandler               0x08012ca0   Section        0  stm32f10x_it.o(i.DMA1_Channel2_IRQHandler)
    i.DMA1_Channel3_IRQHandler               0x08012cb8   Section        0  stm32f10x_it.o(i.DMA1_Channel3_IRQHandler)
    i.DMA1_Channel4_IRQHandler               0x08012cd0   Section        0  stm32f10x_it.o(i.DMA1_Channel4_IRQHandler)
    i.DMA1_Channel5_IRQHandler               0x08012ce8   Section        0  stm32f10x_it.o(i.DMA1_Channel5_IRQHandler)
    i.DMA1_Channel6_IRQHandler               0x08012d00   Section        0  stm32f10x_it.o(i.DMA1_Channel6_IRQHandler)
    i.DMA1_Channel7_IRQHandler               0x08012d18   Section        0  stm32f10x_it.o(i.DMA1_Channel7_IRQHandler)
    i.DMA2_Channel1_IRQHandler               0x08012d30   Section        0  stm32f10x_it.o(i.DMA2_Channel1_IRQHandler)
    i.DMA2_Channel2_IRQHandler               0x08012d48   Section        0  stm32f10x_it.o(i.DMA2_Channel2_IRQHandler)
    i.DMA2_Channel3_IRQHandler               0x08012d60   Section        0  stm32f10x_it.o(i.DMA2_Channel3_IRQHandler)
    i.DMA2_Channel4_5_IRQHandler             0x08012d78   Section        0  stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler)
    i.DacInit                                0x08012d90   Section        0  driver.o(i.DacInit)
    i.DebugMonitor                           0x08012df0   Section        0  stm32f10x_it.o(i.DebugMonitor)
    i.DelayHalfUs                            0x08012e08   Section        0  driver.o(i.DelayHalfUs)
    i.DelayInit                              0x08012e54   Section        0  driver.o(i.DelayInit)
    i.DelayMs                                0x08012ea4   Section        0  driver.o(i.DelayMs)
    i.DelayUs                                0x08012ee8   Section        0  driver.o(i.DelayUs)
    i.EXTI0_IRQHandler                       0x08012f30   Section        0  stm32f10x_it.o(i.EXTI0_IRQHandler)
    i.EXTI15_10_IRQHandler                   0x08012f48   Section        0  driver.o(i.EXTI15_10_IRQHandler)
    i.EXTI1_IRQHandler                       0x08012f74   Section        0  stm32f10x_it.o(i.EXTI1_IRQHandler)
    i.EXTI2_IRQHandler                       0x08012f8c   Section        0  stm32f10x_it.o(i.EXTI2_IRQHandler)
    i.EXTI3_IRQHandler                       0x08012fa4   Section        0  stm32f10x_it.o(i.EXTI3_IRQHandler)
    i.EXTI4_IRQHandler                       0x08012fbc   Section        0  stm32f10x_it.o(i.EXTI4_IRQHandler)
    i.EXTI9_5_IRQHandler                     0x08012fd4   Section        0  stm32f10x_it.o(i.EXTI9_5_IRQHandler)
    i.EXTI_GetITStatus                       0x08012fec   Section        0  stm32f10x_exti.o(i.EXTI_GetITStatus)
    i.EXTI_Init                              0x08013014   Section        0  stm32f10x_exti.o(i.EXTI_Init)
    i.FLASH_ClearFlag                        0x080130cc   Section        0  stm32f10x_flash.o(i.FLASH_ClearFlag)
    i.FLASH_ErasePage                        0x080130d8   Section        0  stm32f10x_flash.o(i.FLASH_ErasePage)
    i.FLASH_GetStatus                        0x08013128   Section        0  stm32f10x_flash.o(i.FLASH_GetStatus)
    i.FLASH_IRQHandler                       0x0801315c   Section        0  stm32f10x_it.o(i.FLASH_IRQHandler)
    i.FLASH_Lock                             0x08013174   Section        0  stm32f10x_flash.o(i.FLASH_Lock)
    i.FLASH_PrefetchBufferCmd                0x08013188   Section        0  stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd)
    i.FLASH_ProgramWord                      0x080131a4   Section        0  stm32f10x_flash.o(i.FLASH_ProgramWord)
    i.FLASH_SetLatency                       0x0801320c   Section        0  stm32f10x_flash.o(i.FLASH_SetLatency)
    i.FLASH_Unlock                           0x08013228   Section        0  stm32f10x_flash.o(i.FLASH_Unlock)
    i.FLASH_WaitForLastOperation             0x08013240   Section        0  stm32f10x_flash.o(i.FLASH_WaitForLastOperation)
    i.FSMC_IRQHandler                        0x0801326c   Section        0  stm32f10x_it.o(i.FSMC_IRQHandler)
    i.FlashReadData                          0x08013284   Section        0  debug.o(i.FlashReadData)
    i.FlashWriteData                         0x0801328a   Section        0  debug.o(i.FlashWriteData)
    i.FlashWriteEnd                          0x0801329c   Section        0  debug.o(i.FlashWriteEnd)
    i.FlashWriteStart                        0x080132ac   Section        0  debug.o(i.FlashWriteStart)
    i.GPIO_EXTILineConfig                    0x080132c8   Section        0  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    i.GPIO_Init                              0x08013308   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x0801341c   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.GetCpuId                               0x0801348c   Section        0  driver.o(i.GetCpuId)
    i.GetDelayTimeFlag                       0x080134b0   Section        0  driver.o(i.GetDelayTimeFlag)
    i.GetKeyValue                            0x080134e0   Section        0  debug.o(i.GetKeyValue)
    i.HardFaultException                     0x0801365c   Section        0  driver.o(i.HardFaultException)
    i.I2C1_ER_IRQHandler                     0x080136d0   Section        0  stm32f10x_it.o(i.I2C1_ER_IRQHandler)
    i.I2C1_EV_IRQHandler                     0x080136e8   Section        0  stm32f10x_it.o(i.I2C1_EV_IRQHandler)
    i.I2C2_ER_IRQHandler                     0x08013700   Section        0  stm32f10x_it.o(i.I2C2_ER_IRQHandler)
    i.I2C2_EV_IRQHandler                     0x08013718   Section        0  stm32f10x_it.o(i.I2C2_EV_IRQHandler)
    i.ILI9481Init                            0x08013730   Section        0  driver.o(i.ILI9481Init)
    i.ILI9481SetDisplayWindow                0x08013c5c   Section        0  driver.o(i.ILI9481SetDisplayWindow)
    i.ILI9481WriteRamPrepare                 0x08013d80   Section        0  driver.o(i.ILI9481WriteRamPrepare)
    i.IR_Decode                              0x08013dcc   Section        0  ir.o(i.IR_Decode)
    i.IR_Init                                0x0801414c   Section        0  ir.o(i.IR_Init)
    i.InitFun                                0x08014174   Section        0  debug.o(i.InitFun)
    i.IrInit                                 0x0801419c   Section        0  driver.o(i.IrInit)
    i.LCD_BackLightInit                      0x08014268   Section        0  driver.o(i.LCD_BackLightInit)
    i.LCD_Clear                              0x0801433c   Section        0  lcd.o(i.LCD_Clear)
    i.LCD_DrawPoint                          0x08014370   Section        0  lcd.o(i.LCD_DrawPoint)
    i.LCD_DrawProgress                       0x080143e4   Section        0  lcd.o(i.LCD_DrawProgress)
    i.LCD_Init                               0x08014540   Section        0  lcd.o(i.LCD_Init)
    i.LCD_InitGpio                           0x08014584   Section        0  driver.o(i.LCD_InitGpio)
    i.LCD_PutChar                            0x0801467c   Section        0  lcd.o(i.LCD_PutChar)
    i.LCD_PutHanzi                           0x08014754   Section        0  lcd.o(i.LCD_PutHanzi)
    i.LCD_PutStr                             0x0801482c   Section        0  lcd.o(i.LCD_PutStr)
    i.LCD_PutStrCenter                       0x08014968   Section        0  lcd.o(i.LCD_PutStrCenter)
    i.LCD_PutStrLeftTop                      0x080149d4   Section        0  lcd.o(i.LCD_PutStrLeftTop)
    i.LCD_SearchFont                         0x08014a0c   Section        0  lcd.o(i.LCD_SearchFont)
    i.LCD_SelectFont                         0x08014bf0   Section        0  lcd.o(i.LCD_SelectFont)
    i.LCD_SetBar                             0x08014c84   Section        0  lcd.o(i.LCD_SetBar)
    i.LCD_SetBright                          0x08014ce4   Section        0  driver.o(i.LCD_SetBright)
    i.LCD_WriteConst                         0x08014d00   Section        0  driver.o(i.LCD_WriteConst)
    i.LedInit                                0x08014d58   Section        0  driver.o(i.LedInit)
    i.LoadData                               0x08014d90   Section        0  debug.o(i.LoadData)
    i.LrcCalc                                0x08014edc   Section        0  common.o(i.LrcCalc)
    i.MemManageException                     0x08014ef8   Section        0  stm32f10x_it.o(i.MemManageException)
    i.MenuDisplayNumber                      0x08014f10   Section        0  debug.o(i.MenuDisplayNumber)
    i.MenuStrLen                             0x0801509c   Section        0  debug.o(i.MenuStrLen)
    i.NMIException                           0x080150b4   Section        0  stm32f10x_it.o(i.NMIException)
    i.NVIC_GenerateSystemReset               0x080150cc   Section        0  stm32f10x_nvic.o(i.NVIC_GenerateSystemReset)
    i.NVIC_Init                              0x080150dc   Section        0  stm32f10x_nvic.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08015180   Section        0  stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig)
    i.NVIC_RESETPRIMASK                      0x08015194   Section        0  stm32f10x_nvic.o(i.NVIC_RESETPRIMASK)
    i.Num2Str                                0x0801519c   Section        0  common.o(i.Num2Str)
    i.OnChipFlashReadData                    0x080152fa   Section        0  driver.o(i.OnChipFlashReadData)
    i.OnChipFlashWritePageData               0x08015312   Section        0  driver.o(i.OnChipFlashWritePageData)
    i.PVD_IRQHandler                         0x0801538c   Section        0  stm32f10x_it.o(i.PVD_IRQHandler)
    i.PendSVC                                0x080153a4   Section        0  stm32f10x_it.o(i.PendSVC)
    i.PwmInit                                0x080153bc   Section        0  driver.o(i.PwmInit)
    i.RCC_ADCCLKConfig                       0x080155f8   Section        0  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    i.RCC_APB1PeriphClockCmd                 0x08015610   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08015630   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_DeInit                             0x08015650   Section        0  stm32f10x_rcc.o(i.RCC_DeInit)
    i.RCC_GetClocksFreq                      0x0801569c   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.RCC_GetFlagStatus                      0x08015770   Section        0  stm32f10x_rcc.o(i.RCC_GetFlagStatus)
    i.RCC_GetSYSCLKSource                    0x080157ac   Section        0  stm32f10x_rcc.o(i.RCC_GetSYSCLKSource)
    i.RCC_HCLKConfig                         0x080157bc   Section        0  stm32f10x_rcc.o(i.RCC_HCLKConfig)
    i.RCC_HSEConfig                          0x080157d4   Section        0  stm32f10x_rcc.o(i.RCC_HSEConfig)
    i.RCC_IRQHandler                         0x08015820   Section        0  stm32f10x_it.o(i.RCC_IRQHandler)
    i.RCC_PCLK1Config                        0x08015838   Section        0  stm32f10x_rcc.o(i.RCC_PCLK1Config)
    i.RCC_PCLK2Config                        0x08015850   Section        0  stm32f10x_rcc.o(i.RCC_PCLK2Config)
    i.RCC_PLLCmd                             0x08015868   Section        0  stm32f10x_rcc.o(i.RCC_PLLCmd)
    i.RCC_PLLConfig                          0x08015874   Section        0  stm32f10x_rcc.o(i.RCC_PLLConfig)
    i.RCC_SYSCLKConfig                       0x08015890   Section        0  stm32f10x_rcc.o(i.RCC_SYSCLKConfig)
    i.RTCAlarm_IRQHandler                    0x080158a8   Section        0  stm32f10x_it.o(i.RTCAlarm_IRQHandler)
    i.RTC_IRQHandler                         0x080158c0   Section        0  stm32f10x_it.o(i.RTC_IRQHandler)
    i.SDIO_IRQHandler                        0x080158d8   Section        0  stm32f10x_it.o(i.SDIO_IRQHandler)
    i.SPI1_IRQHandler                        0x080158f0   Section        0  stm32f10x_it.o(i.SPI1_IRQHandler)
    i.SPI2_IRQHandler                        0x08015908   Section        0  stm32f10x_it.o(i.SPI2_IRQHandler)
    i.SPI3_IRQHandler                        0x08015920   Section        0  stm32f10x_it.o(i.SPI3_IRQHandler)
    i.SPI_Cmd                                0x08015938   Section        0  stm32f10x_spi.o(i.SPI_Cmd)
    i.SPI_FLASH_EraseSector                  0x08015950   Section        0  driver.o(i.SPI_FLASH_EraseSector)
    i.SPI_FLASH_Init                         0x08015a24   Section        0  driver.o(i.SPI_FLASH_Init)
    i.SPI_FLASH_ReadData                     0x08015aec   Section        0  driver.o(i.SPI_FLASH_ReadData)
    i.SPI_FLASH_ReadId                       0x08015be0   Section        0  driver.o(i.SPI_FLASH_ReadId)
    i.SPI_FLASH_WaitBusy                     0x08015d40   Section        0  driver.o(i.SPI_FLASH_WaitBusy)
    i.SPI_FLASH_WriteSector                  0x08015db4   Section        0  driver.o(i.SPI_FLASH_WriteSector)
    i.SPI_Init                               0x08015f54   Section        0  stm32f10x_spi.o(i.SPI_Init)
    i.SVCHandler                             0x08015f90   Section        0  stm32f10x_it.o(i.SVCHandler)
    i.SaveData                               0x08015fa8   Section        0  debug.o(i.SaveData)
    i.SetDelayTimeUs                         0x08016014   Section        0  driver.o(i.SetDelayTimeUs)
    i.Spi3Init                               0x08016038   Section        0  driver.o(i.Spi3Init)
    i.StrLen                                 0x08016130   Section        0  common.o(i.StrLen)
    i.SysTickHandler                         0x0801614c   Section        0  stm32f10x_it.o(i.SysTickHandler)
    i.SystemInit                             0x08016164   Section        0  main.o(i.SystemInit)
    i.TAMPER_IRQHandler                      0x0801618c   Section        0  stm32f10x_it.o(i.TAMPER_IRQHandler)
    i.TASK_Init                              0x080161a4   Section        0  task.o(i.TASK_Init)
    i.TASK_Periodicity                       0x080161d4   Section        0  task.o(i.TASK_Periodicity)
    i.TASK_Run                               0x080161f4   Section        0  task.o(i.TASK_Run)
    i.TASK_TimerInit                         0x0801620c   Section        0  driver.o(i.TASK_TimerInit)
    i.TIM1_BRK_IRQHandler                    0x0801629c   Section        0  stm32f10x_it.o(i.TIM1_BRK_IRQHandler)
    i.TIM1_CC_IRQHandler                     0x080162b4   Section        0  stm32f10x_it.o(i.TIM1_CC_IRQHandler)
    i.TIM1_TRG_COM_IRQHandler                0x080162cc   Section        0  stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler)
    i.TIM1_UP_IRQHandler                     0x080162e4   Section        0  driver.o(i.TIM1_UP_IRQHandler)
    i.TIM2_IRQHandler                        0x08016304   Section        0  stm32f10x_it.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x0801631c   Section        0  stm32f10x_it.o(i.TIM3_IRQHandler)
    i.TIM4_IRQHandler                        0x08016334   Section        0  stm32f10x_it.o(i.TIM4_IRQHandler)
    i.TIM5_IRQHandler                        0x0801634c   Section        0  driver.o(i.TIM5_IRQHandler)
    i.TIM6_IRQHandler                        0x0801636c   Section        0  stm32f10x_it.o(i.TIM6_IRQHandler)
    i.TIM7_IRQHandler                        0x08016384   Section        0  stm32f10x_it.o(i.TIM7_IRQHandler)
    i.TIM8_BRK_IRQHandler                    0x0801639c   Section        0  stm32f10x_it.o(i.TIM8_BRK_IRQHandler)
    i.TIM8_CC_IRQHandler                     0x080163b4   Section        0  stm32f10x_it.o(i.TIM8_CC_IRQHandler)
    i.TIM8_TRG_COM_IRQHandler                0x080163cc   Section        0  stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler)
    i.TIM8_UP_IRQHandler                     0x080163e4   Section        0  stm32f10x_it.o(i.TIM8_UP_IRQHandler)
    i.TIM_ARRPreloadConfig                   0x080163fc   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_BDTRConfig                         0x08016414   Section        0  stm32f10x_tim.o(i.TIM_BDTRConfig)
    i.TIM_ClearFlag                          0x08016434   Section        0  stm32f10x_tim.o(i.TIM_ClearFlag)
    i.TIM_Cmd                                0x0801643a   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x08016452   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_ITConfig                           0x08016470   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_OC1Init                            0x08016484   Section        0  stm32f10x_tim.o(i.TIM_OC1Init)
    i.TIM_OC1PreloadConfig                   0x080164fc   Section        0  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    i.TIM_OC2Init                            0x08016510   Section        0  stm32f10x_tim.o(i.TIM_OC2Init)
    i.TIM_OC2PreloadConfig                   0x080165ac   Section        0  stm32f10x_tim.o(i.TIM_OC2PreloadConfig)
    i.TIM_OC3Init                            0x080165c8   Section        0  stm32f10x_tim.o(i.TIM_OC3Init)
    i.TIM_OC3PreloadConfig                   0x08016660   Section        0  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    i.TIM_OC4Init                            0x08016674   Section        0  stm32f10x_tim.o(i.TIM_OC4Init)
    i.TIM_OC4PreloadConfig                   0x080166e8   Section        0  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    i.TIM_TimeBaseInit                       0x08016704   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.TP_Init                                0x08016740   Section        0  driver.o(i.TP_Init)
    i.UART4_IRQHandler                       0x080167a8   Section        0  stm32f10x_it.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x080167c0   Section        0  stm32f10x_it.o(i.UART5_IRQHandler)
    i.UART_ClearRxdBuffer                    0x080167d8   Section        0  uart.o(i.UART_ClearRxdBuffer)
    i.UART_GetRxdData                        0x080167f4   Section        0  uart.o(i.UART_GetRxdData)
    i.UART_GetRxdFifoLen                     0x08016848   Section        0  uart.o(i.UART_GetRxdFifoLen)
    i.UART_Init                              0x0801687c   Section        0  uart.o(i.UART_Init)
    i.UART_RxdIsr                            0x080168ec   Section        0  uart.o(i.UART_RxdIsr)
    i.UART_SendData                          0x0801692c   Section        0  uart.o(i.UART_SendData)
    i.UART_SendStr                           0x080169b4   Section        0  uart.o(i.UART_SendStr)
    i.UART_TxdIsr                            0x080169d8   Section        0  uart.o(i.UART_TxdIsr)
    i.USART1_IRQHandler                      0x08016a28   Section        0  stm32f10x_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08016a40   Section        0  driver.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08016a98   Section        0  stm32f10x_it.o(i.USART3_IRQHandler)
    i.USART_Cmd                              0x08016ab0   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_ITConfig                         0x08016ac8   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08016b08   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USBWakeUp_IRQHandler                   0x08016ba4   Section        0  stm32f10x_it.o(i.USBWakeUp_IRQHandler)
    i.USB_HP_CAN_TX_IRQHandler               0x08016bbc   Section        0  stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler)
    i.USB_LP_CAN_RX0_IRQHandler              0x08016bd4   Section        0  stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler)
    i.Uart2Init                              0x08016bec   Section        0  driver.o(i.Uart2Init)
    i.UpdateOneTime                          0x08016cac   Section        0  debug.o(i.UpdateOneTime)
    i.UpdatePeriod                           0x08016cb0   Section        0  debug.o(i.UpdatePeriod)
    i.UsageFaultException                    0x08016cf8   Section        0  stm32f10x_it.o(i.UsageFaultException)
    i.WWDG_IRQHandler                        0x08016d10   Section        0  stm32f10x_it.o(i.WWDG_IRQHandler)
    i.XFLASH_GetDataFromUart                 0x08016d28   Section        0  xflash.o(i.XFLASH_GetDataFromUart)
    i.XFLASH_UartRxdIsr                      0x080173c8   Section        0  xflash.o(i.XFLASH_UartRxdIsr)
    i.XFLASH_WriteData                       0x080174d0   Section        0  xflash.o(i.XFLASH_WriteData)
    i.XFONT_GetFontInf                       0x08017630   Section        0  xfont.o(i.XFONT_GetFontInf)
    i.XFONT_Init                             0x08017678   Section        0  xfont.o(i.XFONT_Init)
    i.XFONT_SeleFont                         0x08017688   Section        0  xfont.o(i.XFONT_SeleFont)
    i.changeFun_DacSetValue0                 0x08017760   Section        0  debug.o(i.changeFun_DacSetValue0)
    i.changeFun_DacSetValue1                 0x08017780   Section        0  debug.o(i.changeFun_DacSetValue1)
    i.changeFun_Duty                         0x080177a4   Section        0  debug.o(i.changeFun_Duty)
    i.changeFun_LcdBkLight                   0x080177fc   Section        0  debug.o(i.changeFun_LcdBkLight)
    i.changeFun_PwmDead                      0x0801780c   Section        0  debug.o(i.changeFun_PwmDead)
    i.changeFun_PwmFreq                      0x08017828   Section        0  debug.o(i.changeFun_PwmFreq)
    i.changeFun_RunState                     0x08017868   Section        0  debug.o(i.changeFun_RunState)
    i.delay                                  0x08017908   Section        0  stm32f10x_flash.o(i.delay)
    delay                                    0x08017909   Thumb Code    26  stm32f10x_flash.o(i.delay)
    i.main                                   0x08017922   Section        0  main.o(i.main)
    .constdata                               0x08017938   Section       20  stm32f10x_rcc.o(.constdata)
    APBAHBPrescTable                         0x08017938   Data          16  stm32f10x_rcc.o(.constdata)
    ADCPrescTable                            0x08017948   Data           4  stm32f10x_rcc.o(.constdata)
    .constdata                               0x0801794c   Section    10776  font.o(.constdata)
    .constdata                               0x0801a364   Section     2728  debug.o(.constdata)
    .ARM.__AT_0x0807F800                     0x0807f800   Section     2048  debug.o(.ARM.__AT_0x0807F800)
    .data                                    0x20000000   Section       60  apk.o(.data)
    p                                        0x20000024   Data           4  apk.o(.data)
    sum                                      0x20000028   Data           4  apk.o(.data)
    p                                        0x2000002c   Data           4  apk.o(.data)
    sum                                      0x20000030   Data           4  apk.o(.data)
    duty                                     0x20000034   Data           4  apk.o(.data)
    cnt                                      0x20000038   Data           4  apk.o(.data)
    .data                                    0x2000003c   Section       70  driver.o(.data)
    fac_us                                   0x20000040   Data           1  driver.o(.data)
    fac_ms                                   0x20000044   Data           4  driver.o(.data)
    DelayTimeFlag                            0x20000048   Data           1  driver.o(.data)
    Cal_kx                                   0x20000070   Data           4  driver.o(.data)
    Cal_bx                                   0x20000074   Data           4  driver.o(.data)
    Cal_ky                                   0x20000078   Data           4  driver.o(.data)
    Cal_by                                   0x2000007c   Data           4  driver.o(.data)
    SPI_InitFlag                             0x20000080   Data           1  driver.o(.data)
    cnt                                      0x20000081   Data           1  driver.o(.data)
    .data                                    0x20000084   Section       10  cmd.o(.data)
    timeout_cnt                              0x20000084   Data           4  cmd.o(.data)
    PackDataNum                              0x20000088   Data           2  cmd.o(.data)
    PackDataLen                              0x2000008a   Data           2  cmd.o(.data)
    PackTimeout                              0x2000008c   Data           2  cmd.o(.data)
    .data                                    0x20000090   Section       16  ir.o(.data)
    data1                                    0x20000098   Data           1  ir.o(.data)
    data2                                    0x20000099   Data           1  ir.o(.data)
    data3                                    0x2000009a   Data           1  ir.o(.data)
    data4                                    0x2000009b   Data           1  ir.o(.data)
    IR_start                                 0x2000009c   Data           1  ir.o(.data)
    IR_n                                     0x2000009d   Data           1  ir.o(.data)
    IR_Key_last                              0x2000009e   Data           1  ir.o(.data)
    rep_cnt                                  0x2000009f   Data           1  ir.o(.data)
    .data                                    0x200000a0   Section       21  lcd.o(.data)
    PageOffsetX                              0x200000ae   Data           2  lcd.o(.data)
    PageOffsetY                              0x200000b0   Data           2  lcd.o(.data)
    PageId                                   0x200000b2   Data           2  lcd.o(.data)
    LcdInitFlag                              0x200000b4   Data           1  lcd.o(.data)
    .data                                    0x200000b8   Section       18  uart.o(.data)
    UART_InitFlag                            0x200000b8   Data           1  uart.o(.data)
    TxdFifoDataFront                         0x200000c0   Data           2  uart.o(.data)
    TxdFifoDataRear                          0x200000c2   Data           2  uart.o(.data)
    UART_TxdEndFlag                          0x200000c4   Data           1  uart.o(.data)
    RxdFifoDataFront                         0x200000c6   Data           2  uart.o(.data)
    RxdFifoDataRear                          0x200000c8   Data           2  uart.o(.data)
    .data                                    0x200000ca   Section        9  xflash.o(.data)
    UartRxdDataBufferIndex1                  0x200000ca   Data           2  xflash.o(.data)
    UartRxdDataBufferIndex2                  0x200000cc   Data           2  xflash.o(.data)
    UartRxdCheckBufferIndex                  0x200000ce   Data           2  xflash.o(.data)
    UartRxdBufferSele                        0x200000d0   Data           2  xflash.o(.data)
    UartRxdFlag                              0x200000d2   Data           1  xflash.o(.data)
    .data                                    0x200000d4   Section       16  xfont.o(.data)
    .data                                    0x200000e8   Section       16  task.o(.data)
    .data                                    0x200000f8   Section       32  debug.o(.data)
    AutoUpdate                               0x200000fc   Data           4  debug.o(.data)
    sele                                     0x20000100   Data           4  debug.o(.data)
    menu_p                                   0x20000104   Data           4  debug.o(.data)
    key_cnt                                  0x20000108   Data           2  debug.o(.data)
    start                                    0x2000010c   Data           4  debug.o(.data)
    menu_p_last                              0x20000110   Data           4  debug.o(.data)
    sele_last                                0x20000114   Data           4  debug.o(.data)
    .bss                                     0x20000118   Section      520  apk.o(.bss)
    buffer                                   0x20000190   Data         200  apk.o(.bss)
    buffer                                   0x20000258   Data         200  apk.o(.bss)
    .bss                                     0x20000320   Section       32  driver.o(.bss)
    .bss                                     0x20000340   Section       54  cmd.o(.bss)
    .bss                                     0x20000376   Section     3020  uart.o(.bss)
    TxdFifoData                              0x20000376   Data        1010  uart.o(.bss)
    RxdFifoData                              0x20000768   Data        2010  uart.o(.bss)
    .bss                                     0x20000f44   Section       96  libspace.o(.bss)
    .ARM.__AT_0x20001000                     0x20001000   Section    16384  xflash.o(.ARM.__AT_0x20001000)
    .bss                                     0x20005000   Section     8456  xflash.o(.bss)
    UartRxdDataBuffer1                       0x20005000   Data        4100  xflash.o(.bss)
    UartRxdDataBuffer2                       0x20006004   Data        4100  xflash.o(.bss)
    UartRxdCheckBuffer                       0x20007008   Data         256  xflash.o(.bss)
    .bss                                     0x20007108   Section      580  xfont.o(.bss)
    .bss                                     0x2000734c   Section     3124  debug.o(.bss)
    VarMenuBuffer                            0x20007f04   Data         124  debug.o(.bss)
    HEAP                                     0x20007f80   Section     4096  stm32f10x_vector.o(HEAP)
    Heap_Mem                                 0x20007f80   Data        4096  stm32f10x_vector.o(HEAP)
    STACK                                    0x20008f80   Section     4096  stm32f10x_vector.o(STACK)
    Stack_Mem                                0x20008f80   Data        4096  stm32f10x_vector.o(STACK)
    __initial_sp                             0x20009f80   Data           0  stm32f10x_vector.o(STACK)
    .ARM.__AT_0x2000FFFC                     0x2000fffc   Section        4  driver.o(.ARM.__AT_0x2000FFFC)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors                                0x08010000   Data           4  stm32f10x_vector.o(RESET)
    __main                                   0x08010131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08010139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08010139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08010139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08010147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0801016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08010189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x080101a5   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x080101a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x080101ab   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080101af   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080101b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080101b3   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080101b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080101b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080101b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080101b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080101b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080101b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080101b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080101b7   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080101b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080101b7   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080101bd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080101bd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080101c1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080101c1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080101c9   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080101cb   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080101cb   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080101cf   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    __WFI                                    0x080101d5   Thumb Code     0  cortexm3_macro.o(.text)
    __WFE                                    0x080101d9   Thumb Code     0  cortexm3_macro.o(.text)
    __SEV                                    0x080101dd   Thumb Code     0  cortexm3_macro.o(.text)
    __ISB                                    0x080101e1   Thumb Code     0  cortexm3_macro.o(.text)
    __DSB                                    0x080101e7   Thumb Code     0  cortexm3_macro.o(.text)
    __DMB                                    0x080101ed   Thumb Code     0  cortexm3_macro.o(.text)
    __SVC                                    0x080101f3   Thumb Code     0  cortexm3_macro.o(.text)
    __MRS_CONTROL                            0x080101f7   Thumb Code     0  cortexm3_macro.o(.text)
    __MSR_CONTROL                            0x080101fd   Thumb Code     0  cortexm3_macro.o(.text)
    __MRS_PSP                                0x08010207   Thumb Code     0  cortexm3_macro.o(.text)
    __MSR_PSP                                0x0801020d   Thumb Code     0  cortexm3_macro.o(.text)
    __MRS_MSP                                0x08010213   Thumb Code     0  cortexm3_macro.o(.text)
    __MSR_MSP                                0x08010219   Thumb Code     0  cortexm3_macro.o(.text)
    __RESETPRIMASK                           0x0801021f   Thumb Code     0  cortexm3_macro.o(.text)
    __SETPRIMASK                             0x08010223   Thumb Code     0  cortexm3_macro.o(.text)
    __READ_PRIMASK                           0x08010227   Thumb Code     0  cortexm3_macro.o(.text)
    __SETFAULTMASK                           0x0801022d   Thumb Code     0  cortexm3_macro.o(.text)
    __RESETFAULTMASK                         0x08010231   Thumb Code     0  cortexm3_macro.o(.text)
    __READ_FAULTMASK                         0x08010235   Thumb Code     0  cortexm3_macro.o(.text)
    __BASEPRICONFIG                          0x0801023b   Thumb Code     0  cortexm3_macro.o(.text)
    __GetBASEPRI                             0x08010241   Thumb Code     0  cortexm3_macro.o(.text)
    __REV_HalfWord                           0x08010247   Thumb Code     0  cortexm3_macro.o(.text)
    __REV_Word                               0x0801024b   Thumb Code     0  cortexm3_macro.o(.text)
    Reset_Handler                            0x08010251   Thumb Code     8  stm32f10x_vector.o(.text)
    __user_initial_stackheap                 0x08010259   Thumb Code     0  stm32f10x_vector.o(.text)
    __2sprintf                               0x0801027d   Thumb Code    34  noretval__2sprintf.o(.text)
    __printf                                 0x080102a5   Thumb Code   104  __printf.o(.text)
    _printf_int_dec                          0x0801030d   Thumb Code   104  _printf_dec.o(.text)
    __aeabi_memcpy4                          0x08010385   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08010385   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08010385   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080103cd   Thumb Code     0  rt_memcpy_w.o(.text)
    __use_two_region_memory                  0x080103e9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080103eb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080103ed   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x080103ef   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x080104ab   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x080104d1   Thumb Code    10  _sputc.o(.text)
    __user_libspace                          0x080104dd   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080104dd   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080104dd   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080104e5   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0801052f   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x08010541   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0801054d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0801054d   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0801054f   Thumb Code     0  indicate_semi.o(.text)
    ADC1_2_IRQHandler                        0x08010551   Thumb Code    16  stm32f10x_it.o(i.ADC1_2_IRQHandler)
    ADC3_IRQHandler                          0x08010569   Thumb Code    16  stm32f10x_it.o(i.ADC3_IRQHandler)
    ADC_AutoInjectedConvCmd                  0x08010581   Thumb Code    22  stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd)
    ADC_Cmd                                  0x08010597   Thumb Code    22  stm32f10x_adc.o(i.ADC_Cmd)
    ADC_GetCalibrationStatus                 0x080105ad   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    ADC_GetResetCalibrationStatus            0x080105c1   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    ADC_Init                                 0x080105d5   Thumb Code    70  stm32f10x_adc.o(i.ADC_Init)
    ADC_InjectedChannelConfig                0x08010625   Thumb Code   130  stm32f10x_adc.o(i.ADC_InjectedChannelConfig)
    ADC_InjectedSequencerLengthConfig        0x080106a7   Thumb Code    24  stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig)
    ADC_ResetCalibration                     0x080106bf   Thumb Code    10  stm32f10x_adc.o(i.ADC_ResetCalibration)
    ADC_SoftwareStartConvCmd                 0x080106c9   Thumb Code    22  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    ADC_StartCalibration                     0x080106df   Thumb Code    10  stm32f10x_adc.o(i.ADC_StartCalibration)
    APK_Common                               0x080106e9   Thumb Code     8  apk.o(i.APK_Common)
    APK_Continuous                           0x080106f1   Thumb Code    10  apk.o(i.APK_Continuous)
    APK_Ctrl                                 0x08010701   Thumb Code     8  apk.o(i.APK_Ctrl)
    APK_Init                                 0x08010709   Thumb Code    92  apk.o(i.APK_Init)
    APK_Periodicity                          0x08010799   Thumb Code    48  apk.o(i.APK_Periodicity)
    APK_VoltCurrCalc                         0x080107d1   Thumb Code   172  apk.o(i.APK_VoltCurrCalc)
    AdcInit                                  0x08010899   Thumb Code   498  driver.o(i.AdcInit)
    Apk_Main                                 0x08010ab1   Thumb Code   104  apk.o(i.Apk_Main)
    BusFaultException                        0x08010b2d   Thumb Code    16  stm32f10x_it.o(i.BusFaultException)
    CAN_RX1_IRQHandler                       0x08010b45   Thumb Code    16  stm32f10x_it.o(i.CAN_RX1_IRQHandler)
    CAN_SCE_IRQHandler                       0x08010b5d   Thumb Code    16  stm32f10x_it.o(i.CAN_SCE_IRQHandler)
    CMD_Init                                 0x08010b75   Thumb Code    32  cmd.o(i.CMD_Init)
    CMD_MainTask                             0x08010ba9   Thumb Code   314  cmd.o(i.CMD_MainTask)
    CMD_PackCheck                            0x08010cf5   Thumb Code    52  cmd.o(i.CMD_PackCheck)
    CMD_PackRun                              0x08010d29   Thumb Code   274  cmd.o(i.CMD_PackRun)
    CloclkInit                               0x08010ec5   Thumb Code   150  driver.o(i.CloclkInit)
    CommonConfig                             0x08010f69   Thumb Code    34  driver.o(i.CommonConfig)
    DAC_Cmd                                  0x08010f99   Thumb Code    34  stm32f10x_dac.o(i.DAC_Cmd)
    DAC_Init                                 0x08010fc1   Thumb Code    46  stm32f10x_dac.o(i.DAC_Init)
    DEBUG_InitPara                           0x08010ff5   Thumb Code    12  debug.o(i.DEBUG_InitPara)
    DEBUG_SetPara                            0x08011001   Thumb Code  7280  debug.o(i.DEBUG_SetPara)
    DMA1_Channel1_IRQHandler                 0x08012c89   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_IRQHandler                 0x08012ca1   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel2_IRQHandler)
    DMA1_Channel3_IRQHandler                 0x08012cb9   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel3_IRQHandler)
    DMA1_Channel4_IRQHandler                 0x08012cd1   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel4_IRQHandler)
    DMA1_Channel5_IRQHandler                 0x08012ce9   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel5_IRQHandler)
    DMA1_Channel6_IRQHandler                 0x08012d01   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel6_IRQHandler)
    DMA1_Channel7_IRQHandler                 0x08012d19   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel7_IRQHandler)
    DMA2_Channel1_IRQHandler                 0x08012d31   Thumb Code    16  stm32f10x_it.o(i.DMA2_Channel1_IRQHandler)
    DMA2_Channel2_IRQHandler                 0x08012d49   Thumb Code    16  stm32f10x_it.o(i.DMA2_Channel2_IRQHandler)
    DMA2_Channel3_IRQHandler                 0x08012d61   Thumb Code    16  stm32f10x_it.o(i.DMA2_Channel3_IRQHandler)
    DMA2_Channel4_5_IRQHandler               0x08012d79   Thumb Code    16  stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler)
    DacInit                                  0x08012d91   Thumb Code    90  driver.o(i.DacInit)
    DebugMonitor                             0x08012df1   Thumb Code    16  stm32f10x_it.o(i.DebugMonitor)
    DelayHalfUs                              0x08012e09   Thumb Code    74  driver.o(i.DelayHalfUs)
    DelayInit                                0x08012e55   Thumb Code    58  driver.o(i.DelayInit)
    DelayMs                                  0x08012ea5   Thumb Code    62  driver.o(i.DelayMs)
    DelayUs                                  0x08012ee9   Thumb Code    68  driver.o(i.DelayUs)
    EXTI0_IRQHandler                         0x08012f31   Thumb Code    16  stm32f10x_it.o(i.EXTI0_IRQHandler)
    EXTI15_10_IRQHandler                     0x08012f49   Thumb Code    34  driver.o(i.EXTI15_10_IRQHandler)
    EXTI1_IRQHandler                         0x08012f75   Thumb Code    16  stm32f10x_it.o(i.EXTI1_IRQHandler)
    EXTI2_IRQHandler                         0x08012f8d   Thumb Code    16  stm32f10x_it.o(i.EXTI2_IRQHandler)
    EXTI3_IRQHandler                         0x08012fa5   Thumb Code    16  stm32f10x_it.o(i.EXTI3_IRQHandler)
    EXTI4_IRQHandler                         0x08012fbd   Thumb Code    16  stm32f10x_it.o(i.EXTI4_IRQHandler)
    EXTI9_5_IRQHandler                       0x08012fd5   Thumb Code    16  stm32f10x_it.o(i.EXTI9_5_IRQHandler)
    EXTI_GetITStatus                         0x08012fed   Thumb Code    34  stm32f10x_exti.o(i.EXTI_GetITStatus)
    EXTI_Init                                0x08013015   Thumb Code   174  stm32f10x_exti.o(i.EXTI_Init)
    FLASH_ClearFlag                          0x080130cd   Thumb Code     6  stm32f10x_flash.o(i.FLASH_ClearFlag)
    FLASH_ErasePage                          0x080130d9   Thumb Code    76  stm32f10x_flash.o(i.FLASH_ErasePage)
    FLASH_GetStatus                          0x08013129   Thumb Code    48  stm32f10x_flash.o(i.FLASH_GetStatus)
    FLASH_IRQHandler                         0x0801315d   Thumb Code    16  stm32f10x_it.o(i.FLASH_IRQHandler)
    FLASH_Lock                               0x08013175   Thumb Code    14  stm32f10x_flash.o(i.FLASH_Lock)
    FLASH_PrefetchBufferCmd                  0x08013189   Thumb Code    22  stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd)
    FLASH_ProgramWord                        0x080131a5   Thumb Code   100  stm32f10x_flash.o(i.FLASH_ProgramWord)
    FLASH_SetLatency                         0x0801320d   Thumb Code    22  stm32f10x_flash.o(i.FLASH_SetLatency)
    FLASH_Unlock                             0x08013229   Thumb Code    12  stm32f10x_flash.o(i.FLASH_Unlock)
    FLASH_WaitForLastOperation               0x08013241   Thumb Code    42  stm32f10x_flash.o(i.FLASH_WaitForLastOperation)
    FSMC_IRQHandler                          0x0801326d   Thumb Code    16  stm32f10x_it.o(i.FSMC_IRQHandler)
    FlashReadData                            0x08013285   Thumb Code     6  debug.o(i.FlashReadData)
    FlashWriteData                           0x0801328b   Thumb Code    18  debug.o(i.FlashWriteData)
    FlashWriteEnd                            0x0801329d   Thumb Code    16  debug.o(i.FlashWriteEnd)
    FlashWriteStart                          0x080132ad   Thumb Code    22  debug.o(i.FlashWriteStart)
    GPIO_EXTILineConfig                      0x080132c9   Thumb Code    60  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    GPIO_Init                                0x08013309   Thumb Code   276  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x0801341d   Thumb Code   106  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    GetCpuId                                 0x0801348d   Thumb Code    28  driver.o(i.GetCpuId)
    GetDelayTimeFlag                         0x080134b1   Thumb Code    42  driver.o(i.GetDelayTimeFlag)
    GetKeyValue                              0x080134e1   Thumb Code   360  debug.o(i.GetKeyValue)
    HardFaultException                       0x0801365d   Thumb Code    76  driver.o(i.HardFaultException)
    I2C1_ER_IRQHandler                       0x080136d1   Thumb Code    16  stm32f10x_it.o(i.I2C1_ER_IRQHandler)
    I2C1_EV_IRQHandler                       0x080136e9   Thumb Code    16  stm32f10x_it.o(i.I2C1_EV_IRQHandler)
    I2C2_ER_IRQHandler                       0x08013701   Thumb Code    16  stm32f10x_it.o(i.I2C2_ER_IRQHandler)
    I2C2_EV_IRQHandler                       0x08013719   Thumb Code    16  stm32f10x_it.o(i.I2C2_EV_IRQHandler)
    ILI9481Init                              0x08013731   Thumb Code  1308  driver.o(i.ILI9481Init)
    ILI9481SetDisplayWindow                  0x08013c5d   Thumb Code   276  driver.o(i.ILI9481SetDisplayWindow)
    ILI9481WriteRamPrepare                   0x08013d81   Thumb Code    60  driver.o(i.ILI9481WriteRamPrepare)
    IR_Decode                                0x08013dcd   Thumb Code   850  ir.o(i.IR_Decode)
    IR_Init                                  0x0801414d   Thumb Code    24  ir.o(i.IR_Init)
    InitFun                                  0x08014175   Thumb Code    34  debug.o(i.InitFun)
    IrInit                                   0x0801419d   Thumb Code   188  driver.o(i.IrInit)
    LCD_BackLightInit                        0x08014269   Thumb Code   198  driver.o(i.LCD_BackLightInit)
    LCD_Clear                                0x0801433d   Thumb Code    48  lcd.o(i.LCD_Clear)
    LCD_DrawPoint                            0x08014371   Thumb Code    94  lcd.o(i.LCD_DrawPoint)
    LCD_DrawProgress                         0x080143e5   Thumb Code   340  lcd.o(i.LCD_DrawProgress)
    LCD_Init                                 0x08014541   Thumb Code    46  lcd.o(i.LCD_Init)
    LCD_InitGpio                             0x08014585   Thumb Code   226  driver.o(i.LCD_InitGpio)
    LCD_PutChar                              0x0801467d   Thumb Code   204  lcd.o(i.LCD_PutChar)
    LCD_PutHanzi                             0x08014755   Thumb Code   204  lcd.o(i.LCD_PutHanzi)
    LCD_PutStr                               0x0801482d   Thumb Code   304  lcd.o(i.LCD_PutStr)
    LCD_PutStrCenter                         0x08014969   Thumb Code   100  lcd.o(i.LCD_PutStrCenter)
    LCD_PutStrLeftTop                        0x080149d5   Thumb Code    54  lcd.o(i.LCD_PutStrLeftTop)
    LCD_SearchFont                           0x08014a0d   Thumb Code   442  lcd.o(i.LCD_SearchFont)
    LCD_SelectFont                           0x08014bf1   Thumb Code   120  lcd.o(i.LCD_SelectFont)
    LCD_SetBar                               0x08014c85   Thumb Code    90  lcd.o(i.LCD_SetBar)
    LCD_SetBright                            0x08014ce5   Thumb Code    22  driver.o(i.LCD_SetBright)
    LCD_WriteConst                           0x08014d01   Thumb Code    70  driver.o(i.LCD_WriteConst)
    LedInit                                  0x08014d59   Thumb Code    52  driver.o(i.LedInit)
    LoadData                                 0x08014d91   Thumb Code   322  debug.o(i.LoadData)
    LrcCalc                                  0x08014edd   Thumb Code    28  common.o(i.LrcCalc)
    MemManageException                       0x08014ef9   Thumb Code    16  stm32f10x_it.o(i.MemManageException)
    MenuDisplayNumber                        0x08014f11   Thumb Code   392  debug.o(i.MenuDisplayNumber)
    MenuStrLen                               0x0801509d   Thumb Code    24  debug.o(i.MenuStrLen)
    NMIException                             0x080150b5   Thumb Code    16  stm32f10x_it.o(i.NMIException)
    NVIC_GenerateSystemReset                 0x080150cd   Thumb Code     8  stm32f10x_nvic.o(i.NVIC_GenerateSystemReset)
    NVIC_Init                                0x080150dd   Thumb Code   150  stm32f10x_nvic.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08015181   Thumb Code    10  stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig)
    NVIC_RESETPRIMASK                        0x08015195   Thumb Code     8  stm32f10x_nvic.o(i.NVIC_RESETPRIMASK)
    Num2Str                                  0x0801519d   Thumb Code   350  common.o(i.Num2Str)
    OnChipFlashReadData                      0x080152fb   Thumb Code    24  driver.o(i.OnChipFlashReadData)
    OnChipFlashWritePageData                 0x08015313   Thumb Code   122  driver.o(i.OnChipFlashWritePageData)
    PVD_IRQHandler                           0x0801538d   Thumb Code    16  stm32f10x_it.o(i.PVD_IRQHandler)
    PendSVC                                  0x080153a5   Thumb Code    16  stm32f10x_it.o(i.PendSVC)
    PwmInit                                  0x080153bd   Thumb Code   546  driver.o(i.PwmInit)
    RCC_ADCCLKConfig                         0x080155f9   Thumb Code    18  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    RCC_APB1PeriphClockCmd                   0x08015611   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08015631   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_DeInit                               0x08015651   Thumb Code    62  stm32f10x_rcc.o(i.RCC_DeInit)
    RCC_GetClocksFreq                        0x0801569d   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    RCC_GetFlagStatus                        0x08015771   Thumb Code    56  stm32f10x_rcc.o(i.RCC_GetFlagStatus)
    RCC_GetSYSCLKSource                      0x080157ad   Thumb Code    10  stm32f10x_rcc.o(i.RCC_GetSYSCLKSource)
    RCC_HCLKConfig                           0x080157bd   Thumb Code    18  stm32f10x_rcc.o(i.RCC_HCLKConfig)
    RCC_HSEConfig                            0x080157d5   Thumb Code    70  stm32f10x_rcc.o(i.RCC_HSEConfig)
    RCC_IRQHandler                           0x08015821   Thumb Code    16  stm32f10x_it.o(i.RCC_IRQHandler)
    RCC_PCLK1Config                          0x08015839   Thumb Code    18  stm32f10x_rcc.o(i.RCC_PCLK1Config)
    RCC_PCLK2Config                          0x08015851   Thumb Code    20  stm32f10x_rcc.o(i.RCC_PCLK2Config)
    RCC_PLLCmd                               0x08015869   Thumb Code     6  stm32f10x_rcc.o(i.RCC_PLLCmd)
    RCC_PLLConfig                            0x08015875   Thumb Code    24  stm32f10x_rcc.o(i.RCC_PLLConfig)
    RCC_SYSCLKConfig                         0x08015891   Thumb Code    18  stm32f10x_rcc.o(i.RCC_SYSCLKConfig)
    RTCAlarm_IRQHandler                      0x080158a9   Thumb Code    16  stm32f10x_it.o(i.RTCAlarm_IRQHandler)
    RTC_IRQHandler                           0x080158c1   Thumb Code    16  stm32f10x_it.o(i.RTC_IRQHandler)
    SDIO_IRQHandler                          0x080158d9   Thumb Code    16  stm32f10x_it.o(i.SDIO_IRQHandler)
    SPI1_IRQHandler                          0x080158f1   Thumb Code    16  stm32f10x_it.o(i.SPI1_IRQHandler)
    SPI2_IRQHandler                          0x08015909   Thumb Code    16  stm32f10x_it.o(i.SPI2_IRQHandler)
    SPI3_IRQHandler                          0x08015921   Thumb Code    16  stm32f10x_it.o(i.SPI3_IRQHandler)
    SPI_Cmd                                  0x08015939   Thumb Code    24  stm32f10x_spi.o(i.SPI_Cmd)
    SPI_FLASH_EraseSector                    0x08015951   Thumb Code   198  driver.o(i.SPI_FLASH_EraseSector)
    SPI_FLASH_Init                           0x08015a25   Thumb Code   188  driver.o(i.SPI_FLASH_Init)
    SPI_FLASH_ReadData                       0x08015aed   Thumb Code   230  driver.o(i.SPI_FLASH_ReadData)
    SPI_FLASH_ReadId                         0x08015be1   Thumb Code   336  driver.o(i.SPI_FLASH_ReadId)
    SPI_FLASH_WaitBusy                       0x08015d41   Thumb Code   104  driver.o(i.SPI_FLASH_WaitBusy)
    SPI_FLASH_WriteSector                    0x08015db5   Thumb Code   402  driver.o(i.SPI_FLASH_WriteSector)
    SPI_Init                                 0x08015f55   Thumb Code    60  stm32f10x_spi.o(i.SPI_Init)
    SVCHandler                               0x08015f91   Thumb Code    16  stm32f10x_it.o(i.SVCHandler)
    SaveData                                 0x08015fa9   Thumb Code   100  debug.o(i.SaveData)
    SetDelayTimeUs                           0x08016015   Thumb Code    28  driver.o(i.SetDelayTimeUs)
    Spi3Init                                 0x08016039   Thumb Code   232  driver.o(i.Spi3Init)
    StrLen                                   0x08016131   Thumb Code    26  common.o(i.StrLen)
    SysTickHandler                           0x0801614d   Thumb Code    16  stm32f10x_it.o(i.SysTickHandler)
    SystemInit                               0x08016165   Thumb Code    28  main.o(i.SystemInit)
    TAMPER_IRQHandler                        0x0801618d   Thumb Code    16  stm32f10x_it.o(i.TAMPER_IRQHandler)
    TASK_Init                                0x080161a5   Thumb Code    32  task.o(i.TASK_Init)
    TASK_Periodicity                         0x080161d5   Thumb Code    26  task.o(i.TASK_Periodicity)
    TASK_Run                                 0x080161f5   Thumb Code    20  task.o(i.TASK_Run)
    TASK_TimerInit                           0x0801620d   Thumb Code   130  driver.o(i.TASK_TimerInit)
    TIM1_BRK_IRQHandler                      0x0801629d   Thumb Code    16  stm32f10x_it.o(i.TIM1_BRK_IRQHandler)
    TIM1_CC_IRQHandler                       0x080162b5   Thumb Code    16  stm32f10x_it.o(i.TIM1_CC_IRQHandler)
    TIM1_TRG_COM_IRQHandler                  0x080162cd   Thumb Code    16  stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler)
    TIM1_UP_IRQHandler                       0x080162e5   Thumb Code    22  driver.o(i.TIM1_UP_IRQHandler)
    TIM2_IRQHandler                          0x08016305   Thumb Code    16  stm32f10x_it.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x0801631d   Thumb Code    16  stm32f10x_it.o(i.TIM3_IRQHandler)
    TIM4_IRQHandler                          0x08016335   Thumb Code    16  stm32f10x_it.o(i.TIM4_IRQHandler)
    TIM5_IRQHandler                          0x0801634d   Thumb Code    22  driver.o(i.TIM5_IRQHandler)
    TIM6_IRQHandler                          0x0801636d   Thumb Code    16  stm32f10x_it.o(i.TIM6_IRQHandler)
    TIM7_IRQHandler                          0x08016385   Thumb Code    16  stm32f10x_it.o(i.TIM7_IRQHandler)
    TIM8_BRK_IRQHandler                      0x0801639d   Thumb Code    16  stm32f10x_it.o(i.TIM8_BRK_IRQHandler)
    TIM8_CC_IRQHandler                       0x080163b5   Thumb Code    16  stm32f10x_it.o(i.TIM8_CC_IRQHandler)
    TIM8_TRG_COM_IRQHandler                  0x080163cd   Thumb Code    16  stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler)
    TIM8_UP_IRQHandler                       0x080163e5   Thumb Code    16  stm32f10x_it.o(i.TIM8_UP_IRQHandler)
    TIM_ARRPreloadConfig                     0x080163fd   Thumb Code    24  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_BDTRConfig                           0x08016415   Thumb Code    32  stm32f10x_tim.o(i.TIM_BDTRConfig)
    TIM_ClearFlag                            0x08016435   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearFlag)
    TIM_Cmd                                  0x0801643b   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x08016453   Thumb Code    30  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_ITConfig                             0x08016471   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_OC1Init                              0x08016485   Thumb Code   110  stm32f10x_tim.o(i.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x080164fd   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    TIM_OC2Init                              0x08016511   Thumb Code   148  stm32f10x_tim.o(i.TIM_OC2Init)
    TIM_OC2PreloadConfig                     0x080165ad   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC2PreloadConfig)
    TIM_OC3Init                              0x080165c9   Thumb Code   144  stm32f10x_tim.o(i.TIM_OC3Init)
    TIM_OC3PreloadConfig                     0x08016661   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    TIM_OC4Init                              0x08016675   Thumb Code   106  stm32f10x_tim.o(i.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x080166e9   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    TIM_TimeBaseInit                         0x08016705   Thumb Code    50  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    TP_Init                                  0x08016741   Thumb Code    64  driver.o(i.TP_Init)
    UART4_IRQHandler                         0x080167a9   Thumb Code    16  stm32f10x_it.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x080167c1   Thumb Code    16  stm32f10x_it.o(i.UART5_IRQHandler)
    UART_ClearRxdBuffer                      0x080167d9   Thumb Code    16  uart.o(i.UART_ClearRxdBuffer)
    UART_GetRxdData                          0x080167f5   Thumb Code    70  uart.o(i.UART_GetRxdData)
    UART_GetRxdFifoLen                       0x08016849   Thumb Code    44  uart.o(i.UART_GetRxdFifoLen)
    UART_Init                                0x0801687d   Thumb Code    66  uart.o(i.UART_Init)
    UART_RxdIsr                              0x080168ed   Thumb Code    50  uart.o(i.UART_RxdIsr)
    UART_SendData                            0x0801692d   Thumb Code   114  uart.o(i.UART_SendData)
    UART_SendStr                             0x080169b5   Thumb Code    36  uart.o(i.UART_SendStr)
    UART_TxdIsr                              0x080169d9   Thumb Code    58  uart.o(i.UART_TxdIsr)
    USART1_IRQHandler                        0x08016a29   Thumb Code    16  stm32f10x_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08016a41   Thumb Code    76  driver.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08016a99   Thumb Code    16  stm32f10x_it.o(i.USART3_IRQHandler)
    USART_Cmd                                0x08016ab1   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_ITConfig                           0x08016ac9   Thumb Code    64  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08016b09   Thumb Code   150  stm32f10x_usart.o(i.USART_Init)
    USBWakeUp_IRQHandler                     0x08016ba5   Thumb Code    16  stm32f10x_it.o(i.USBWakeUp_IRQHandler)
    USB_HP_CAN_TX_IRQHandler                 0x08016bbd   Thumb Code    16  stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler)
    USB_LP_CAN_RX0_IRQHandler                0x08016bd5   Thumb Code    16  stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler)
    Uart2Init                                0x08016bed   Thumb Code   174  driver.o(i.Uart2Init)
    UpdateOneTime                            0x08016cad   Thumb Code     2  debug.o(i.UpdateOneTime)
    UpdatePeriod                             0x08016cb1   Thumb Code    60  debug.o(i.UpdatePeriod)
    UsageFaultException                      0x08016cf9   Thumb Code    16  stm32f10x_it.o(i.UsageFaultException)
    WWDG_IRQHandler                          0x08016d11   Thumb Code    16  stm32f10x_it.o(i.WWDG_IRQHandler)
    XFLASH_GetDataFromUart                   0x08016d29   Thumb Code  1604  xflash.o(i.XFLASH_GetDataFromUart)
    XFLASH_UartRxdIsr                        0x080173c9   Thumb Code   228  xflash.o(i.XFLASH_UartRxdIsr)
    XFLASH_WriteData                         0x080174d1   Thumb Code   342  xflash.o(i.XFLASH_WriteData)
    XFONT_GetFontInf                         0x08017631   Thumb Code    60  xfont.o(i.XFONT_GetFontInf)
    XFONT_Init                               0x08017679   Thumb Code    14  xfont.o(i.XFONT_Init)
    XFONT_SeleFont                           0x08017689   Thumb Code   180  xfont.o(i.XFONT_SeleFont)
    changeFun_DacSetValue0                   0x08017761   Thumb Code    22  debug.o(i.changeFun_DacSetValue0)
    changeFun_DacSetValue1                   0x08017781   Thumb Code    26  debug.o(i.changeFun_DacSetValue1)
    changeFun_Duty                           0x080177a5   Thumb Code    80  debug.o(i.changeFun_Duty)
    changeFun_LcdBkLight                     0x080177fd   Thumb Code    12  debug.o(i.changeFun_LcdBkLight)
    changeFun_PwmDead                        0x0801780d   Thumb Code    20  debug.o(i.changeFun_PwmDead)
    changeFun_PwmFreq                        0x08017829   Thumb Code    48  debug.o(i.changeFun_PwmFreq)
    changeFun_RunState                       0x08017869   Thumb Code   148  debug.o(i.changeFun_RunState)
    main                                     0x08017923   Thumb Code    22  main.o(i.main)
    ASCII_12X24                              0x0801794c   Data        4608  font.o(.constdata)
    ASCII_16X32                              0x08018b4c   Data        6144  font.o(.constdata)
    FONT_32                                  0x0801a34c   Data          12  font.o(.constdata)
    FONT_24                                  0x0801a358   Data          12  font.o(.constdata)
    VarMenu                                  0x0801a364   Data        2728  debug.o(.constdata)
    Region$$Table$$Base                      0x0801ae0c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0801ae2c   Number         0  anon$$obj.o(Region$$Table)
    DEBUG_FLASH_DATA                         0x0807f800   Data        2048  debug.o(.ARM.__AT_0x0807F800)
    PwmFreq                                  0x20000000   Data           4  apk.o(.data)
    PwmDead                                  0x20000004   Data           4  apk.o(.data)
    RunState                                 0x20000008   Data           4  apk.o(.data)
    LcdBkLight                               0x2000000c   Data           4  apk.o(.data)
    DacSetValue                              0x20000010   Data           8  apk.o(.data)
    TaskTimeSec                              0x20000018   Data           4  apk.o(.data)
    ptrApkTask                               0x2000001c   Data           4  apk.o(.data)
    ptrApkTaskPre                            0x20000020   Data           4  apk.o(.data)
    SystemCoreClock                          0x2000003c   Data           4  driver.o(.data)
    Uart2RxdIsr                              0x2000004c   Data           4  driver.o(.data)
    Uart2TxdIsr                              0x20000050   Data           4  driver.o(.data)
    IrExtiIsr                                0x20000054   Data           4  driver.o(.data)
    PwmIsr                                   0x20000058   Data           4  driver.o(.data)
    TaskTimerIsr                             0x2000005c   Data           4  driver.o(.data)
    XFlashId                                 0x20000060   Data           8  driver.o(.data)
    TP_TouchNum                              0x20000068   Data           2  driver.o(.data)
    SPI_FLASH_InitFlag                       0x2000006a   Data           1  driver.o(.data)
    DbValue                                  0x2000006c   Data           2  driver.o(.data)
    IR_Key                                   0x20000090   Data           1  ir.o(.data)
    IR_LedTimeout                            0x20000094   Data           4  ir.o(.data)
    Font_ASCII_p                             0x200000a0   Data           4  lcd.o(.data)
    Font_CHINESE_p                           0x200000a4   Data           4  lcd.o(.data)
    Font_ASCII_WIDTH                         0x200000a8   Data           1  lcd.o(.data)
    Font_ASCII_HEIGHT                        0x200000a9   Data           1  lcd.o(.data)
    nbyte0                                   0x200000aa   Data           1  lcd.o(.data)
    nbyte1                                   0x200000ab   Data           1  lcd.o(.data)
    FontExternalFlag                         0x200000ac   Data           2  lcd.o(.data)
    UART_BdRate                              0x200000bc   Data           4  uart.o(.data)
    FontEncode                               0x200000d4   Data           2  xfont.o(.data)
    FontDataLenAscii                         0x200000d6   Data           2  xfont.o(.data)
    FontDataLenOther                         0x200000d8   Data           2  xfont.o(.data)
    XFONT_FontNum                            0x200000dc   Data           4  xfont.o(.data)
    XFONT_FontEncode                         0x200000e0   Data           4  xfont.o(.data)
    TaskTimeCnt                              0x200000e8   Data           8  task.o(.data)
    TaskTimeCntNext                          0x200000f0   Data           8  task.o(.data)
    AnalogDataIndex                          0x200000f8   Data           2  debug.o(.data)
    WaveBufferIndex                          0x200000fa   Data           2  debug.o(.data)
    Duty                                     0x20000118   Data          12  apk.o(.bss)
    AdcRawData                               0x20000124   Data          24  apk.o(.bss)
    VoltL                                    0x2000013c   Data          12  apk.o(.bss)
    Curr                                     0x20000148   Data          12  apk.o(.bss)
    K_VoltL                                  0x20000154   Data          12  apk.o(.bss)
    K_Curr                                   0x20000160   Data          12  apk.o(.bss)
    B_Curr                                   0x2000016c   Data          12  apk.o(.bss)
    SetVoltL                                 0x20000178   Data          12  apk.o(.bss)
    SetCurr                                  0x20000184   Data          12  apk.o(.bss)
    TP_TouchPoint                            0x20000320   Data          20  driver.o(.bss)
    CpuId                                    0x20000334   Data          12  driver.o(.bss)
    CMD_Pack                                 0x20000340   Data          54  cmd.o(.bss)
    __libspace_start                         0x20000f44   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000fa4   Data           0  libspace.o(.bss)
    XFLASH_TempBuffer                        0x20001000   Data       16384  xflash.o(.ARM.__AT_0x20001000)
    XFONT_Inf                                0x20007108   Data         528  xfont.o(.bss)
    XFONT_CurrHeader                         0x20007318   Data          52  xfont.o(.bss)
    AnalogData                               0x2000734c   Data        2000  debug.o(.bss)
    WaveBuffer                               0x20007b1c   Data        1000  debug.o(.bss)
    SystemRunMode                            0x2000fffc   Data           4  driver.o(.ARM.__AT_0x2000FFFC)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08010131

  Load Region LR_IROM1 (Base: 0x08010000, Size: 0x0000af44, Max: 0x0006f000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08010000, Load base: 0x08010000, Size: 0x0000ae2c, Max: 0x0006f000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08010000   0x08010000   0x00000130   Data   RO            8    RESET               stm32f10x_vector.o
    0x08010130   0x08010130   0x00000008   Code   RO         3641  * !!!main             c_w.l(__main.o)
    0x08010138   0x08010138   0x00000034   Code   RO         3920    !!!scatter          c_w.l(__scatter.o)
    0x0801016c   0x0801016c   0x0000001a   Code   RO         3922    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08010186   0x08010186   0x00000002   PAD
    0x08010188   0x08010188   0x0000001c   Code   RO         3924    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080101a4   0x080101a4   0x00000000   Code   RO         3636    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080101a4   0x080101a4   0x00000006   Code   RO         3635    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080101aa   0x080101aa   0x00000004   Code   RO         3700    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080101ae   0x080101ae   0x00000002   Code   RO         3787    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3795    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3797    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3800    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3802    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3804    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3807    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3809    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3811    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3813    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3815    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3817    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3819    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3821    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3823    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3825    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3827    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3831    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3833    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3835    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000000   Code   RO         3837    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080101b0   0x080101b0   0x00000002   Code   RO         3838    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080101b2   0x080101b2   0x00000002   Code   RO         3860    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080101b4   0x080101b4   0x00000000   Code   RO         3873    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080101b4   0x080101b4   0x00000000   Code   RO         3875    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080101b4   0x080101b4   0x00000000   Code   RO         3878    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080101b4   0x080101b4   0x00000000   Code   RO         3881    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080101b4   0x080101b4   0x00000000   Code   RO         3883    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080101b4   0x080101b4   0x00000000   Code   RO         3886    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080101b4   0x080101b4   0x00000002   Code   RO         3887    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080101b6   0x080101b6   0x00000000   Code   RO         3691    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080101b6   0x080101b6   0x00000000   Code   RO         3748    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080101b6   0x080101b6   0x00000006   Code   RO         3760    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080101bc   0x080101bc   0x00000000   Code   RO         3750    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080101bc   0x080101bc   0x00000004   Code   RO         3751    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080101c0   0x080101c0   0x00000000   Code   RO         3753    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080101c0   0x080101c0   0x00000008   Code   RO         3754    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080101c8   0x080101c8   0x00000002   Code   RO         3792    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080101ca   0x080101ca   0x00000000   Code   RO         3842    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080101ca   0x080101ca   0x00000004   Code   RO         3843    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080101ce   0x080101ce   0x00000006   Code   RO         3844    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080101d4   0x080101d4   0x0000007a   Code   RO            1    .text               cortexm3_macro.o
    0x0801024e   0x0801024e   0x00000002   PAD
    0x08010250   0x08010250   0x0000002c   Code   RO            9    .text               stm32f10x_vector.o
    0x0801027c   0x0801027c   0x00000028   Code   RO         3609    .text               c_w.l(noretval__2sprintf.o)
    0x080102a4   0x080102a4   0x00000068   Code   RO         3611    .text               c_w.l(__printf.o)
    0x0801030c   0x0801030c   0x00000078   Code   RO         3613    .text               c_w.l(_printf_dec.o)
    0x08010384   0x08010384   0x00000064   Code   RO         3637    .text               c_w.l(rt_memcpy_w.o)
    0x080103e8   0x080103e8   0x00000006   Code   RO         3639    .text               c_w.l(heapauxi.o)
    0x080103ee   0x080103ee   0x000000b2   Code   RO         3694    .text               c_w.l(_printf_intcommon.o)
    0x080104a0   0x080104a0   0x00000030   Code   RO         3696    .text               c_w.l(_printf_char_common.o)
    0x080104d0   0x080104d0   0x0000000a   Code   RO         3698    .text               c_w.l(_sputc.o)
    0x080104da   0x080104da   0x00000002   PAD
    0x080104dc   0x080104dc   0x00000008   Code   RO         3775    .text               c_w.l(libspace.o)
    0x080104e4   0x080104e4   0x0000004a   Code   RO         3778    .text               c_w.l(sys_stackheap_outer.o)
    0x0801052e   0x0801052e   0x00000012   Code   RO         3780    .text               c_w.l(exit.o)
    0x08010540   0x08010540   0x0000000c   Code   RO         3852    .text               c_w.l(sys_exit.o)
    0x0801054c   0x0801054c   0x00000002   Code   RO         3863    .text               c_w.l(use_no_semi.o)
    0x0801054e   0x0801054e   0x00000000   Code   RO         3865    .text               c_w.l(indicate_semi.o)
    0x0801054e   0x0801054e   0x00000002   PAD
    0x08010550   0x08010550   0x00000018   Code   RO         2141    i.ADC1_2_IRQHandler  stm32f10x_it.o
    0x08010568   0x08010568   0x00000018   Code   RO         2142    i.ADC3_IRQHandler   stm32f10x_it.o
    0x08010580   0x08010580   0x00000016   Code   RO           16    i.ADC_AutoInjectedConvCmd  stm32f10x_adc.o
    0x08010596   0x08010596   0x00000016   Code   RO           19    i.ADC_Cmd           stm32f10x_adc.o
    0x080105ac   0x080105ac   0x00000014   Code   RO           27    i.ADC_GetCalibrationStatus  stm32f10x_adc.o
    0x080105c0   0x080105c0   0x00000014   Code   RO           33    i.ADC_GetResetCalibrationStatus  stm32f10x_adc.o
    0x080105d4   0x080105d4   0x00000050   Code   RO           37    i.ADC_Init          stm32f10x_adc.o
    0x08010624   0x08010624   0x00000082   Code   RO           38    i.ADC_InjectedChannelConfig  stm32f10x_adc.o
    0x080106a6   0x080106a6   0x00000018   Code   RO           40    i.ADC_InjectedSequencerLengthConfig  stm32f10x_adc.o
    0x080106be   0x080106be   0x0000000a   Code   RO           42    i.ADC_ResetCalibration  stm32f10x_adc.o
    0x080106c8   0x080106c8   0x00000016   Code   RO           44    i.ADC_SoftwareStartConvCmd  stm32f10x_adc.o
    0x080106de   0x080106de   0x0000000a   Code   RO           46    i.ADC_StartCalibration  stm32f10x_adc.o
    0x080106e8   0x080106e8   0x00000008   Code   RO         2558    i.APK_Common        apk.o
    0x080106f0   0x080106f0   0x00000010   Code   RO         2559    i.APK_Continuous    apk.o
    0x08010700   0x08010700   0x00000008   Code   RO         2560    i.APK_Ctrl          apk.o
    0x08010708   0x08010708   0x00000090   Code   RO         2561    i.APK_Init          apk.o
    0x08010798   0x08010798   0x00000038   Code   RO         2566    i.APK_Periodicity   apk.o
    0x080107d0   0x080107d0   0x000000c8   Code   RO         2567    i.APK_VoltCurrCalc  apk.o
    0x08010898   0x08010898   0x00000218   Code   RO         2650    i.AdcInit           driver.o
    0x08010ab0   0x08010ab0   0x0000007c   Code   RO         2568    i.Apk_Main          apk.o
    0x08010b2c   0x08010b2c   0x00000018   Code   RO         2143    i.BusFaultException  stm32f10x_it.o
    0x08010b44   0x08010b44   0x00000018   Code   RO         2144    i.CAN_RX1_IRQHandler  stm32f10x_it.o
    0x08010b5c   0x08010b5c   0x00000018   Code   RO         2145    i.CAN_SCE_IRQHandler  stm32f10x_it.o
    0x08010b74   0x08010b74   0x00000034   Code   RO         3003    i.CMD_Init          cmd.o
    0x08010ba8   0x08010ba8   0x0000014c   Code   RO         3004    i.CMD_MainTask      cmd.o
    0x08010cf4   0x08010cf4   0x00000034   Code   RO         3005    i.CMD_PackCheck     cmd.o
    0x08010d28   0x08010d28   0x0000019c   Code   RO         3006    i.CMD_PackRun       cmd.o
    0x08010ec4   0x08010ec4   0x000000a4   Code   RO         2651    i.CloclkInit        driver.o
    0x08010f68   0x08010f68   0x00000030   Code   RO         2652    i.CommonConfig      driver.o
    0x08010f98   0x08010f98   0x00000028   Code   RO          254    i.DAC_Cmd           stm32f10x_dac.o
    0x08010fc0   0x08010fc0   0x00000034   Code   RO          259    i.DAC_Init          stm32f10x_dac.o
    0x08010ff4   0x08010ff4   0x0000000c   Code   RO         3454    i.DEBUG_InitPara    debug.o
    0x08011000   0x08011000   0x00001c88   Code   RO         3455    i.DEBUG_SetPara     debug.o
    0x08012c88   0x08012c88   0x00000018   Code   RO         2146    i.DMA1_Channel1_IRQHandler  stm32f10x_it.o
    0x08012ca0   0x08012ca0   0x00000018   Code   RO         2147    i.DMA1_Channel2_IRQHandler  stm32f10x_it.o
    0x08012cb8   0x08012cb8   0x00000018   Code   RO         2148    i.DMA1_Channel3_IRQHandler  stm32f10x_it.o
    0x08012cd0   0x08012cd0   0x00000018   Code   RO         2149    i.DMA1_Channel4_IRQHandler  stm32f10x_it.o
    0x08012ce8   0x08012ce8   0x00000018   Code   RO         2150    i.DMA1_Channel5_IRQHandler  stm32f10x_it.o
    0x08012d00   0x08012d00   0x00000018   Code   RO         2151    i.DMA1_Channel6_IRQHandler  stm32f10x_it.o
    0x08012d18   0x08012d18   0x00000018   Code   RO         2152    i.DMA1_Channel7_IRQHandler  stm32f10x_it.o
    0x08012d30   0x08012d30   0x00000018   Code   RO         2153    i.DMA2_Channel1_IRQHandler  stm32f10x_it.o
    0x08012d48   0x08012d48   0x00000018   Code   RO         2154    i.DMA2_Channel2_IRQHandler  stm32f10x_it.o
    0x08012d60   0x08012d60   0x00000018   Code   RO         2155    i.DMA2_Channel3_IRQHandler  stm32f10x_it.o
    0x08012d78   0x08012d78   0x00000018   Code   RO         2156    i.DMA2_Channel4_5_IRQHandler  stm32f10x_it.o
    0x08012d90   0x08012d90   0x00000060   Code   RO         2653    i.DacInit           driver.o
    0x08012df0   0x08012df0   0x00000018   Code   RO         2157    i.DebugMonitor      stm32f10x_it.o
    0x08012e08   0x08012e08   0x0000004a   Code   RO         2654    i.DelayHalfUs       driver.o
    0x08012e52   0x08012e52   0x00000002   PAD
    0x08012e54   0x08012e54   0x00000050   Code   RO         2655    i.DelayInit         driver.o
    0x08012ea4   0x08012ea4   0x00000044   Code   RO         2656    i.DelayMs           driver.o
    0x08012ee8   0x08012ee8   0x00000048   Code   RO         2657    i.DelayUs           driver.o
    0x08012f30   0x08012f30   0x00000018   Code   RO         2158    i.EXTI0_IRQHandler  stm32f10x_it.o
    0x08012f48   0x08012f48   0x0000002c   Code   RO         2658    i.EXTI15_10_IRQHandler  driver.o
    0x08012f74   0x08012f74   0x00000018   Code   RO         2160    i.EXTI1_IRQHandler  stm32f10x_it.o
    0x08012f8c   0x08012f8c   0x00000018   Code   RO         2161    i.EXTI2_IRQHandler  stm32f10x_it.o
    0x08012fa4   0x08012fa4   0x00000018   Code   RO         2162    i.EXTI3_IRQHandler  stm32f10x_it.o
    0x08012fbc   0x08012fbc   0x00000018   Code   RO         2163    i.EXTI4_IRQHandler  stm32f10x_it.o
    0x08012fd4   0x08012fd4   0x00000018   Code   RO         2164    i.EXTI9_5_IRQHandler  stm32f10x_it.o
    0x08012fec   0x08012fec   0x00000028   Code   RO          340    i.EXTI_GetITStatus  stm32f10x_exti.o
    0x08013014   0x08013014   0x000000b8   Code   RO          341    i.EXTI_Init         stm32f10x_exti.o
    0x080130cc   0x080130cc   0x0000000c   Code   RO         1825    i.FLASH_ClearFlag   stm32f10x_flash.o
    0x080130d8   0x080130d8   0x00000050   Code   RO         1829    i.FLASH_ErasePage   stm32f10x_flash.o
    0x08013128   0x08013128   0x00000034   Code   RO         1833    i.FLASH_GetStatus   stm32f10x_flash.o
    0x0801315c   0x0801315c   0x00000018   Code   RO         2165    i.FLASH_IRQHandler  stm32f10x_it.o
    0x08013174   0x08013174   0x00000014   Code   RO         1838    i.FLASH_Lock        stm32f10x_flash.o
    0x08013188   0x08013188   0x0000001c   Code   RO         1839    i.FLASH_PrefetchBufferCmd  stm32f10x_flash.o
    0x080131a4   0x080131a4   0x00000068   Code   RO         1842    i.FLASH_ProgramWord  stm32f10x_flash.o
    0x0801320c   0x0801320c   0x0000001c   Code   RO         1844    i.FLASH_SetLatency  stm32f10x_flash.o
    0x08013228   0x08013228   0x00000018   Code   RO         1845    i.FLASH_Unlock      stm32f10x_flash.o
    0x08013240   0x08013240   0x0000002a   Code   RO         1847    i.FLASH_WaitForLastOperation  stm32f10x_flash.o
    0x0801326a   0x0801326a   0x00000002   PAD
    0x0801326c   0x0801326c   0x00000018   Code   RO         2166    i.FSMC_IRQHandler   stm32f10x_it.o
    0x08013284   0x08013284   0x00000006   Code   RO         3456    i.FlashReadData     debug.o
    0x0801328a   0x0801328a   0x00000012   Code   RO         3457    i.FlashWriteData    debug.o
    0x0801329c   0x0801329c   0x00000010   Code   RO         3458    i.FlashWriteEnd     debug.o
    0x080132ac   0x080132ac   0x0000001c   Code   RO         3459    i.FlashWriteStart   debug.o
    0x080132c8   0x080132c8   0x00000040   Code   RO          394    i.GPIO_EXTILineConfig  stm32f10x_gpio.o
    0x08013308   0x08013308   0x00000114   Code   RO          397    i.GPIO_Init         stm32f10x_gpio.o
    0x0801341c   0x0801341c   0x00000070   Code   RO          399    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x0801348c   0x0801348c   0x00000024   Code   RO         2659    i.GetCpuId          driver.o
    0x080134b0   0x080134b0   0x00000030   Code   RO         2660    i.GetDelayTimeFlag  driver.o
    0x080134e0   0x080134e0   0x0000017c   Code   RO         3460    i.GetKeyValue       debug.o
    0x0801365c   0x0801365c   0x00000074   Code   RO         2661    i.HardFaultException  driver.o
    0x080136d0   0x080136d0   0x00000018   Code   RO         2168    i.I2C1_ER_IRQHandler  stm32f10x_it.o
    0x080136e8   0x080136e8   0x00000018   Code   RO         2169    i.I2C1_EV_IRQHandler  stm32f10x_it.o
    0x08013700   0x08013700   0x00000018   Code   RO         2170    i.I2C2_ER_IRQHandler  stm32f10x_it.o
    0x08013718   0x08013718   0x00000018   Code   RO         2171    i.I2C2_EV_IRQHandler  stm32f10x_it.o
    0x08013730   0x08013730   0x0000052c   Code   RO         2670    i.ILI9481Init       driver.o
    0x08013c5c   0x08013c5c   0x00000124   Code   RO         2672    i.ILI9481SetDisplayWindow  driver.o
    0x08013d80   0x08013d80   0x0000004c   Code   RO         2673    i.ILI9481WriteRamPrepare  driver.o
    0x08013dcc   0x08013dcc   0x00000380   Code   RO         3091    i.IR_Decode         ir.o
    0x0801414c   0x0801414c   0x00000028   Code   RO         3092    i.IR_Init           ir.o
    0x08014174   0x08014174   0x00000028   Code   RO         3461    i.InitFun           debug.o
    0x0801419c   0x0801419c   0x000000cc   Code   RO         2674    i.IrInit            driver.o
    0x08014268   0x08014268   0x000000d4   Code   RO         2675    i.LCD_BackLightInit  driver.o
    0x0801433c   0x0801433c   0x00000034   Code   RO         3115    i.LCD_Clear         lcd.o
    0x08014370   0x08014370   0x00000074   Code   RO         3122    i.LCD_DrawPoint     lcd.o
    0x080143e4   0x080143e4   0x0000015c   Code   RO         3123    i.LCD_DrawProgress  lcd.o
    0x08014540   0x08014540   0x00000044   Code   RO         3125    i.LCD_Init          lcd.o
    0x08014584   0x08014584   0x000000f8   Code   RO         2676    i.LCD_InitGpio      driver.o
    0x0801467c   0x0801467c   0x000000d8   Code   RO         3126    i.LCD_PutChar       lcd.o
    0x08014754   0x08014754   0x000000d8   Code   RO         3127    i.LCD_PutHanzi      lcd.o
    0x0801482c   0x0801482c   0x0000013c   Code   RO         3128    i.LCD_PutStr        lcd.o
    0x08014968   0x08014968   0x0000006c   Code   RO         3129    i.LCD_PutStrCenter  lcd.o
    0x080149d4   0x080149d4   0x00000036   Code   RO         3130    i.LCD_PutStrLeftTop  lcd.o
    0x08014a0a   0x08014a0a   0x00000002   PAD
    0x08014a0c   0x08014a0c   0x000001e4   Code   RO         3133    i.LCD_SearchFont    lcd.o
    0x08014bf0   0x08014bf0   0x00000094   Code   RO         3134    i.LCD_SelectFont    lcd.o
    0x08014c84   0x08014c84   0x00000060   Code   RO         3135    i.LCD_SetBar        lcd.o
    0x08014ce4   0x08014ce4   0x0000001c   Code   RO         2677    i.LCD_SetBright     driver.o
    0x08014d00   0x08014d00   0x00000058   Code   RO         2680    i.LCD_WriteConst    driver.o
    0x08014d58   0x08014d58   0x00000038   Code   RO         2681    i.LedInit           driver.o
    0x08014d90   0x08014d90   0x0000014c   Code   RO         3462    i.LoadData          debug.o
    0x08014edc   0x08014edc   0x0000001c   Code   RO         3038    i.LrcCalc           common.o
    0x08014ef8   0x08014ef8   0x00000018   Code   RO         2172    i.MemManageException  stm32f10x_it.o
    0x08014f10   0x08014f10   0x0000018c   Code   RO         3463    i.MenuDisplayNumber  debug.o
    0x0801509c   0x0801509c   0x00000018   Code   RO         3464    i.MenuStrLen        debug.o
    0x080150b4   0x080150b4   0x00000018   Code   RO         2173    i.NMIException      stm32f10x_it.o
    0x080150cc   0x080150cc   0x00000010   Code   RO          508    i.NVIC_GenerateSystemReset  stm32f10x_nvic.o
    0x080150dc   0x080150dc   0x000000a4   Code   RO          519    i.NVIC_Init         stm32f10x_nvic.o
    0x08015180   0x08015180   0x00000014   Code   RO          520    i.NVIC_PriorityGroupConfig  stm32f10x_nvic.o
    0x08015194   0x08015194   0x00000008   Code   RO          522    i.NVIC_RESETPRIMASK  stm32f10x_nvic.o
    0x0801519c   0x0801519c   0x0000015e   Code   RO         3039    i.Num2Str           common.o
    0x080152fa   0x080152fa   0x00000018   Code   RO         2682    i.OnChipFlashReadData  driver.o
    0x08015312   0x08015312   0x0000007a   Code   RO         2683    i.OnChipFlashWritePageData  driver.o
    0x0801538c   0x0801538c   0x00000018   Code   RO         2174    i.PVD_IRQHandler    stm32f10x_it.o
    0x080153a4   0x080153a4   0x00000018   Code   RO         2175    i.PendSVC           stm32f10x_it.o
    0x080153bc   0x080153bc   0x0000023c   Code   RO         2684    i.PwmInit           driver.o
    0x080155f8   0x080155f8   0x00000018   Code   RO          755    i.RCC_ADCCLKConfig  stm32f10x_rcc.o
    0x08015610   0x08015610   0x00000020   Code   RO          757    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08015630   0x08015630   0x00000020   Code   RO          759    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08015650   0x08015650   0x0000004c   Code   RO          766    i.RCC_DeInit        stm32f10x_rcc.o
    0x0801569c   0x0801569c   0x000000d4   Code   RO          767    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08015770   0x08015770   0x0000003c   Code   RO          768    i.RCC_GetFlagStatus  stm32f10x_rcc.o
    0x080157ac   0x080157ac   0x00000010   Code   RO          770    i.RCC_GetSYSCLKSource  stm32f10x_rcc.o
    0x080157bc   0x080157bc   0x00000018   Code   RO          771    i.RCC_HCLKConfig    stm32f10x_rcc.o
    0x080157d4   0x080157d4   0x0000004c   Code   RO          772    i.RCC_HSEConfig     stm32f10x_rcc.o
    0x08015820   0x08015820   0x00000018   Code   RO         2176    i.RCC_IRQHandler    stm32f10x_it.o
    0x08015838   0x08015838   0x00000018   Code   RO          778    i.RCC_PCLK1Config   stm32f10x_rcc.o
    0x08015850   0x08015850   0x00000018   Code   RO          779    i.RCC_PCLK2Config   stm32f10x_rcc.o
    0x08015868   0x08015868   0x0000000c   Code   RO          780    i.RCC_PLLCmd        stm32f10x_rcc.o
    0x08015874   0x08015874   0x0000001c   Code   RO          781    i.RCC_PLLConfig     stm32f10x_rcc.o
    0x08015890   0x08015890   0x00000018   Code   RO          784    i.RCC_SYSCLKConfig  stm32f10x_rcc.o
    0x080158a8   0x080158a8   0x00000018   Code   RO         2177    i.RTCAlarm_IRQHandler  stm32f10x_it.o
    0x080158c0   0x080158c0   0x00000018   Code   RO         2178    i.RTC_IRQHandler    stm32f10x_it.o
    0x080158d8   0x080158d8   0x00000018   Code   RO         2179    i.SDIO_IRQHandler   stm32f10x_it.o
    0x080158f0   0x080158f0   0x00000018   Code   RO         2180    i.SPI1_IRQHandler   stm32f10x_it.o
    0x08015908   0x08015908   0x00000018   Code   RO         2181    i.SPI2_IRQHandler   stm32f10x_it.o
    0x08015920   0x08015920   0x00000018   Code   RO         2182    i.SPI3_IRQHandler   stm32f10x_it.o
    0x08015938   0x08015938   0x00000018   Code   RO          961    i.SPI_Cmd           stm32f10x_spi.o
    0x08015950   0x08015950   0x000000d4   Code   RO         2685    i.SPI_FLASH_EraseSector  driver.o
    0x08015a24   0x08015a24   0x000000c8   Code   RO         2686    i.SPI_FLASH_Init    driver.o
    0x08015aec   0x08015aec   0x000000f4   Code   RO         2687    i.SPI_FLASH_ReadData  driver.o
    0x08015be0   0x08015be0   0x00000160   Code   RO         2688    i.SPI_FLASH_ReadId  driver.o
    0x08015d40   0x08015d40   0x00000074   Code   RO         2979    i.SPI_FLASH_WaitBusy  driver.o
    0x08015db4   0x08015db4   0x000001a0   Code   RO         2689    i.SPI_FLASH_WriteSector  driver.o
    0x08015f54   0x08015f54   0x0000003c   Code   RO          974    i.SPI_Init          stm32f10x_spi.o
    0x08015f90   0x08015f90   0x00000018   Code   RO         2183    i.SVCHandler        stm32f10x_it.o
    0x08015fa8   0x08015fa8   0x0000006c   Code   RO         3465    i.SaveData          debug.o
    0x08016014   0x08016014   0x00000024   Code   RO         2690    i.SetDelayTimeUs    driver.o
    0x08016038   0x08016038   0x000000f8   Code   RO         2691    i.Spi3Init          driver.o
    0x08016130   0x08016130   0x0000001a   Code   RO         3042    i.StrLen            common.o
    0x0801614a   0x0801614a   0x00000002   PAD
    0x0801614c   0x0801614c   0x00000018   Code   RO         2184    i.SysTickHandler    stm32f10x_it.o
    0x08016164   0x08016164   0x00000028   Code   RO         2022    i.SystemInit        main.o
    0x0801618c   0x0801618c   0x00000018   Code   RO         2185    i.TAMPER_IRQHandler  stm32f10x_it.o
    0x080161a4   0x080161a4   0x00000030   Code   RO         3422    i.TASK_Init         task.o
    0x080161d4   0x080161d4   0x00000020   Code   RO         3423    i.TASK_Periodicity  task.o
    0x080161f4   0x080161f4   0x00000018   Code   RO         3424    i.TASK_Run          task.o
    0x0801620c   0x0801620c   0x00000090   Code   RO         2692    i.TASK_TimerInit    driver.o
    0x0801629c   0x0801629c   0x00000018   Code   RO         2186    i.TIM1_BRK_IRQHandler  stm32f10x_it.o
    0x080162b4   0x080162b4   0x00000018   Code   RO         2187    i.TIM1_CC_IRQHandler  stm32f10x_it.o
    0x080162cc   0x080162cc   0x00000018   Code   RO         2188    i.TIM1_TRG_COM_IRQHandler  stm32f10x_it.o
    0x080162e4   0x080162e4   0x00000020   Code   RO         2693    i.TIM1_UP_IRQHandler  driver.o
    0x08016304   0x08016304   0x00000018   Code   RO         2189    i.TIM2_IRQHandler   stm32f10x_it.o
    0x0801631c   0x0801631c   0x00000018   Code   RO         2190    i.TIM3_IRQHandler   stm32f10x_it.o
    0x08016334   0x08016334   0x00000018   Code   RO         2191    i.TIM4_IRQHandler   stm32f10x_it.o
    0x0801634c   0x0801634c   0x00000020   Code   RO         2694    i.TIM5_IRQHandler   driver.o
    0x0801636c   0x0801636c   0x00000018   Code   RO         2193    i.TIM6_IRQHandler   stm32f10x_it.o
    0x08016384   0x08016384   0x00000018   Code   RO         2194    i.TIM7_IRQHandler   stm32f10x_it.o
    0x0801639c   0x0801639c   0x00000018   Code   RO         2195    i.TIM8_BRK_IRQHandler  stm32f10x_it.o
    0x080163b4   0x080163b4   0x00000018   Code   RO         2196    i.TIM8_CC_IRQHandler  stm32f10x_it.o
    0x080163cc   0x080163cc   0x00000018   Code   RO         2197    i.TIM8_TRG_COM_IRQHandler  stm32f10x_it.o
    0x080163e4   0x080163e4   0x00000018   Code   RO         2198    i.TIM8_UP_IRQHandler  stm32f10x_it.o
    0x080163fc   0x080163fc   0x00000018   Code   RO         1107    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08016414   0x08016414   0x00000020   Code   RO         1108    i.TIM_BDTRConfig    stm32f10x_tim.o
    0x08016434   0x08016434   0x00000006   Code   RO         1113    i.TIM_ClearFlag     stm32f10x_tim.o
    0x0801643a   0x0801643a   0x00000018   Code   RO         1119    i.TIM_Cmd           stm32f10x_tim.o
    0x08016452   0x08016452   0x0000001e   Code   RO         1121    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x08016470   0x08016470   0x00000012   Code   RO         1144    i.TIM_ITConfig      stm32f10x_tim.o
    0x08016482   0x08016482   0x00000002   PAD
    0x08016484   0x08016484   0x00000078   Code   RO         1148    i.TIM_OC1Init       stm32f10x_tim.o
    0x080164fc   0x080164fc   0x00000012   Code   RO         1151    i.TIM_OC1PreloadConfig  stm32f10x_tim.o
    0x0801650e   0x0801650e   0x00000002   PAD
    0x08016510   0x08016510   0x0000009c   Code   RO         1153    i.TIM_OC2Init       stm32f10x_tim.o
    0x080165ac   0x080165ac   0x0000001a   Code   RO         1156    i.TIM_OC2PreloadConfig  stm32f10x_tim.o
    0x080165c6   0x080165c6   0x00000002   PAD
    0x080165c8   0x080165c8   0x00000098   Code   RO         1158    i.TIM_OC3Init       stm32f10x_tim.o
    0x08016660   0x08016660   0x00000012   Code   RO         1161    i.TIM_OC3PreloadConfig  stm32f10x_tim.o
    0x08016672   0x08016672   0x00000002   PAD
    0x08016674   0x08016674   0x00000074   Code   RO         1163    i.TIM_OC4Init       stm32f10x_tim.o
    0x080166e8   0x080166e8   0x0000001a   Code   RO         1165    i.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x08016702   0x08016702   0x00000002   PAD
    0x08016704   0x08016704   0x0000003c   Code   RO         1190    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08016740   0x08016740   0x00000068   Code   RO         2695    i.TP_Init           driver.o
    0x080167a8   0x080167a8   0x00000018   Code   RO         2199    i.UART4_IRQHandler  stm32f10x_it.o
    0x080167c0   0x080167c0   0x00000018   Code   RO         2200    i.UART5_IRQHandler  stm32f10x_it.o
    0x080167d8   0x080167d8   0x0000001c   Code   RO         3297    i.UART_ClearRxdBuffer  uart.o
    0x080167f4   0x080167f4   0x00000054   Code   RO         3298    i.UART_GetRxdData   uart.o
    0x08016848   0x08016848   0x00000034   Code   RO         3299    i.UART_GetRxdFifoLen  uart.o
    0x0801687c   0x0801687c   0x00000070   Code   RO         3300    i.UART_Init         uart.o
    0x080168ec   0x080168ec   0x00000040   Code   RO         3301    i.UART_RxdIsr       uart.o
    0x0801692c   0x0801692c   0x00000088   Code   RO         3302    i.UART_SendData     uart.o
    0x080169b4   0x080169b4   0x00000024   Code   RO         3303    i.UART_SendStr      uart.o
    0x080169d8   0x080169d8   0x00000050   Code   RO         3304    i.UART_TxdIsr       uart.o
    0x08016a28   0x08016a28   0x00000018   Code   RO         2201    i.USART1_IRQHandler  stm32f10x_it.o
    0x08016a40   0x08016a40   0x00000058   Code   RO         2700    i.USART2_IRQHandler  driver.o
    0x08016a98   0x08016a98   0x00000018   Code   RO         2203    i.USART3_IRQHandler  stm32f10x_it.o
    0x08016ab0   0x08016ab0   0x00000018   Code   RO         1658    i.USART_Cmd         stm32f10x_usart.o
    0x08016ac8   0x08016ac8   0x00000040   Code   RO         1664    i.USART_ITConfig    stm32f10x_usart.o
    0x08016b08   0x08016b08   0x0000009c   Code   RO         1665    i.USART_Init        stm32f10x_usart.o
    0x08016ba4   0x08016ba4   0x00000018   Code   RO         2204    i.USBWakeUp_IRQHandler  stm32f10x_it.o
    0x08016bbc   0x08016bbc   0x00000018   Code   RO         2205    i.USB_HP_CAN_TX_IRQHandler  stm32f10x_it.o
    0x08016bd4   0x08016bd4   0x00000018   Code   RO         2206    i.USB_LP_CAN_RX0_IRQHandler  stm32f10x_it.o
    0x08016bec   0x08016bec   0x000000c0   Code   RO         2701    i.Uart2Init         driver.o
    0x08016cac   0x08016cac   0x00000002   Code   RO         3466    i.UpdateOneTime     debug.o
    0x08016cae   0x08016cae   0x00000002   PAD
    0x08016cb0   0x08016cb0   0x00000048   Code   RO         3467    i.UpdatePeriod      debug.o
    0x08016cf8   0x08016cf8   0x00000018   Code   RO         2207    i.UsageFaultException  stm32f10x_it.o
    0x08016d10   0x08016d10   0x00000018   Code   RO         2208    i.WWDG_IRQHandler   stm32f10x_it.o
    0x08016d28   0x08016d28   0x000006a0   Code   RO         3358    i.XFLASH_GetDataFromUart  xflash.o
    0x080173c8   0x080173c8   0x00000108   Code   RO         3359    i.XFLASH_UartRxdIsr  xflash.o
    0x080174d0   0x080174d0   0x00000160   Code   RO         3360    i.XFLASH_WriteData  xflash.o
    0x08017630   0x08017630   0x00000048   Code   RO         3390    i.XFONT_GetFontInf  xfont.o
    0x08017678   0x08017678   0x0000000e   Code   RO         3391    i.XFONT_Init        xfont.o
    0x08017686   0x08017686   0x00000002   PAD
    0x08017688   0x08017688   0x000000d8   Code   RO         3392    i.XFONT_SeleFont    xfont.o
    0x08017760   0x08017760   0x00000020   Code   RO         3468    i.changeFun_DacSetValue0  debug.o
    0x08017780   0x08017780   0x00000024   Code   RO         3469    i.changeFun_DacSetValue1  debug.o
    0x080177a4   0x080177a4   0x00000058   Code   RO         3470    i.changeFun_Duty    debug.o
    0x080177fc   0x080177fc   0x00000010   Code   RO         3471    i.changeFun_LcdBkLight  debug.o
    0x0801780c   0x0801780c   0x0000001c   Code   RO         3472    i.changeFun_PwmDead  debug.o
    0x08017828   0x08017828   0x00000040   Code   RO         3473    i.changeFun_PwmFreq  debug.o
    0x08017868   0x08017868   0x000000a0   Code   RO         3474    i.changeFun_RunState  debug.o
    0x08017908   0x08017908   0x0000001a   Code   RO         1848    i.delay             stm32f10x_flash.o
    0x08017922   0x08017922   0x00000016   Code   RO         2023    i.main              main.o
    0x08017938   0x08017938   0x00000014   Data   RO          787    .constdata          stm32f10x_rcc.o
    0x0801794c   0x0801794c   0x00002a18   Data   RO         3079    .constdata          font.o
    0x0801a364   0x0801a364   0x00000aa8   Data   RO         3477    .constdata          debug.o
    0x0801ae0c   0x0801ae0c   0x00000020   Data   RO         3918    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0801ae2c, Size: 0x00009f80, Max: 0x0000fff0, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0801ae2c   0x0000003c   Data   RW         2570    .data               apk.o
    0x2000003c   0x0801ae68   0x00000046   Data   RW         2705    .data               driver.o
    0x20000082   0x0801aeae   0x00000002   PAD
    0x20000084   0x0801aeb0   0x0000000a   Data   RW         3008    .data               cmd.o
    0x2000008e   0x0801aeba   0x00000002   PAD
    0x20000090   0x0801aebc   0x00000010   Data   RW         3093    .data               ir.o
    0x200000a0   0x0801aecc   0x00000015   Data   RW         3137    .data               lcd.o
    0x200000b5   0x0801aee1   0x00000003   PAD
    0x200000b8   0x0801aee4   0x00000012   Data   RW         3306    .data               uart.o
    0x200000ca   0x0801aef6   0x00000009   Data   RW         3363    .data               xflash.o
    0x200000d3   0x0801aeff   0x00000001   PAD
    0x200000d4   0x0801af00   0x00000010   Data   RW         3394    .data               xfont.o
    0x200000e4   0x0801af10   0x00000004   PAD
    0x200000e8   0x0801af14   0x00000010   Data   RW         3425    .data               task.o
    0x200000f8   0x0801af24   0x00000020   Data   RW         3478    .data               debug.o
    0x20000118        -       0x00000208   Zero   RW         2569    .bss                apk.o
    0x20000320        -       0x00000020   Zero   RW         2704    .bss                driver.o
    0x20000340        -       0x00000036   Zero   RW         3007    .bss                cmd.o
    0x20000376        -       0x00000bcc   Zero   RW         3305    .bss                uart.o
    0x20000f42   0x0801af44   0x00000002   PAD
    0x20000f44        -       0x00000060   Zero   RW         3776    .bss                c_w.l(libspace.o)
    0x20000fa4   0x0801af44   0x0000005c   PAD
    0x20001000        -       0x00004000   Zero   RW         3361    .ARM.__AT_0x20001000  xflash.o
    0x20005000        -       0x00002108   Zero   RW         3362    .bss                xflash.o
    0x20007108        -       0x00000244   Zero   RW         3393    .bss                xfont.o
    0x2000734c        -       0x00000c34   Zero   RW         3476    .bss                debug.o
    0x20007f80        -       0x00001000   Zero   RW            7    HEAP                stm32f10x_vector.o
    0x20008f80        -       0x00001000   Zero   RW            6    STACK               stm32f10x_vector.o



  Load Region LR$$.ARM.__AT_0x0807F800 (Base: 0x0807f800, Size: 0x00000800, Max: 0x00000800, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x0807F800 (Exec base: 0x0807f800, Load base: 0x0807f800, Size: 0x00000800, Max: 0x00000800, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x0807f800   0x0807f800   0x00000800   Data   RO         3475    .ARM.__AT_0x0807F800  debug.o



  Load Region LR$$.ARM.__AT_0x2000FFFC (Base: 0x2000fffc, Size: 0x00000000, Max: 0x00000004, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x2000FFFC (Exec base: 0x2000fffc, Load base: 0x2000fffc, Size: 0x00000004, Max: 0x00000004, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x2000fffc        -       0x00000004   Zero   RW         2703    .ARM.__AT_0x2000FFFC  driver.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       556        114          0         60        520       7505   apk.o
       848        176          0         10         54       4199   cmd.o
       404          0          0          0          0       3237   common.o
       122          0          0          0          0        484   cortexm3_macro.o
      9162        320       4776         32       3124      18918   debug.o
      7044        548          0         70         36      34660   driver.o
         0          0      10776          0          0       1654   font.o
       936         76          0         16          0       2441   ir.o
      2222        176          0         21          0      14070   lcd.o
        62         12          0          0          0       1988   main.o
       360         10          0          0          0      42655   stm32f10x_adc.o
        92         12          0          0          0       6813   stm32f10x_dac.o
       224         16          0          0          0       4139   stm32f10x_exti.o
       416         48          0          0          0      12504   stm32f10x_flash.o
       452         10          0          0          0      10256   stm32f10x_gpio.o
      1536        512          0          0          0      39317   stm32f10x_it.o
       208         32          0          0          0      11298   stm32f10x_nvic.o
       664        100         20          0          0      12789   stm32f10x_rcc.o
        84          0          0          0          0       9213   stm32f10x_spi.o
       826         46          0          0          0      36293   stm32f10x_tim.o
       244          6          0          0          0      10518   stm32f10x_usart.o
        44         26        304          0       8192        468   stm32f10x_vector.o
       104         26          0         16          0       2284   task.o
       592        138          0         18       3020       7158   uart.o
      2312        258          0          9      24840       6072   xflash.o
       302         48          0         16        580       4328   xfont.o

    ----------------------------------------------------------------------
     29840       <USER>      <GROUP>        280      40368     305261   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          0          0         12          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0         68   _sputc.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        40          6          0          0          0         84   noretval__2sprintf.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       888         <USER>          <GROUP>          0        188       1176   Library Totals
         6          0          0          0         92          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       882         44          0          0         96       1176   c_w.l

    ----------------------------------------------------------------------
       888         <USER>          <GROUP>          0        188       1176   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     30728       2754      15908        280      40556     293953   Grand Totals
     30728       2754      15908        280      40556     293953   ELF Image Totals
     30728       2754      15908        280          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                46636 (  45.54kB)
    Total RW  Size (RW Data + ZI Data)             40836 (  39.88kB)
    Total ROM Size (Code + RO Data + RW Data)      46916 (  45.82kB)

==============================================================================

