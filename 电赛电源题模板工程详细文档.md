# 🔋 电赛电源题模板工程详细文档

## 📖 目录
- [1. 工程概述](#1-工程概述)
- [2. 系统架构](#2-系统架构)
- [3. 硬件平台](#3-硬件平台)
- [4. 软件架构](#4-软件架构)
- [5. 核心功能模块](#5-核心功能模块)
- [6. 硬件接口详解](#6-硬件接口详解)
- [7. 开发指南](#7-开发指南)
- [8. 调试与测试](#8-调试与测试)
- [9. 电赛应用实例](#9-电赛应用实例)
- [10. 常见问题解答](#10-常见问题解答)

---

## 1. 工程概述

### 1.1 项目简介
本模板工程是专门为**全国大学生电子设计竞赛（电赛）电源题**设计的完整解决方案。基于STM32F103微控制器，集成了电源控制所需的全部功能模块，包括：
- 多路电压电流采集与控制
- PWM功率调节
- 人机交互界面
- 数据存储与通信
- 调试与测试工具

### 1.2 技术特点
- ✅ **高精度采集**：12位ADC，支持6路同步采集
- ✅ **灵活控制**：3路独立PWM输出，频率可调
- ✅ **丰富接口**：LCD显示、触摸屏、按键、串口
- ✅ **实时系统**：基于时间片的任务调度
- ✅ **易于扩展**：模块化设计，便于功能扩展

### 1.3 适用场景
- 开关电源设计题
- 电源管理系统题
- 无线充电系统题
- 太阳能充电控制器题
- 电池管理系统题
- 其他电源相关竞赛题目

---

## 2. 系统架构

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    电赛电源题模板工程                        │
├─────────────────────────────────────────────────────────────┤
│  应用层 (UserCode/User/)                                    │
│  ├── main.c          # 主程序入口                           │
│  ├── apk.c           # 应用程序核心                         │
│  └── includes.h      # 头文件包含                           │
├─────────────────────────────────────────────────────────────┤
│  API层 (qiankunTeamLib/api/)                               │
│  ├── task.c/h        # 任务调度系统                         │
│  ├── lcd.c/h         # LCD显示接口                          │
│  ├── uart.c/h        # 串口通信接口                         │
│  ├── debug.c/h       # 调试工具接口                         │
│  └── ...             # 其他功能接口                         │
├─────────────────────────────────────────────────────────────┤
│  驱动层 (qiankunTeamLib/driver/)                           │
│  ├── driver.c/h      # 硬件驱动核心                         │
│  └── 各种外设驱动                                           │
├─────────────────────────────────────────────────────────────┤
│  硬件抽象层 (STM32 HAL)                                     │
│  └── STM32F103标准库                                        │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 文件结构
```
📁 模板工程/
├── 📁 UserCode/                    # 用户代码目录
│   ├── 📁 User/                   # 用户应用程序
│   │   ├── main.c                 # 主程序
│   │   ├── apk.c                  # 应用核心
│   │   ├── includes.h             # 头文件
│   │   └── ...
│   ├── 📁 Lib/                    # STM32标准库
│   ├── 📁 Startup/                # 启动文件
│   └── test.uvprojx               # Keil工程文件
├── 📁 qiankunTeamLib/             # 乾坤团队库
│   ├── 📁 api/                    # 应用接口层
│   │   ├── task.c/h               # 任务系统
│   │   ├── lcd.c/h                # LCD接口
│   │   ├── uart.c/h               # 串口接口
│   │   ├── debug.c/h              # 调试接口
│   │   └── ...
│   └── 📁 driver/                 # 驱动层
│       ├── driver.c/h             # 核心驱动
│       └── ...
└── 工程修改说明.txt                # 使用说明
```

---

## 3. 硬件平台

### 3.1 主控芯片
- **型号**：STM32F103系列（推荐STM32F103VET6）
- **架构**：ARM Cortex-M3内核
- **主频**：72MHz
- **Flash**：512KB
- **RAM**：64KB
- **封装**：LQFP100

### 3.2 外设资源
| 外设类型 | 数量 | 用途 | 备注 |
|---------|------|------|------|
| ADC | 6路 | 电压电流采集 | 12位精度 |
| DAC | 2路 | 模拟输出 | 12位精度 |
| PWM | 3路 | 功率控制 | 互补输出 |
| UART | 1路 | 串口通信 | 最高2Mbps |
| SPI | 1路 | Flash通信 | 高速模式 |
| I2C | 1路 | 扩展接口 | 标准模式 |
| GPIO | 多路 | 按键/LED等 | 可配置 |

### 3.3 接口定义
```c
// ADC通道定义
#define ADC_CH_NUM      (6)     // ADC通道数
// CH1,CH3,CH5: 电压采集
// CH2,CH4,CH6: 电流采集

// DAC通道定义
#define DAC_CH_NUM      (2)     // DAC通道数

// PWM相数定义
#define PWM_PHASE_NUM   (3)     // PWM相数
```

---

## 4. 软件架构

### 4.1 程序流程
```mermaid
graph TD
    A[系统启动] --> B[SystemInit]
    B --> C[时钟初始化 CloclkInit]
    C --> D[通用配置 CommonConfig]
    D --> E[任务初始化 TASK_Init]
    E --> F[APK初始化 APK_Init]
    F --> G[任务运行 TASK_Run]
    G --> H[主循环]
    H --> I[任务调度]
    I --> J[APK_Continuous]
    J --> K[用户应用]
    K --> I
```

### 4.2 任务调度系统
系统采用**协作式多任务**调度：
- **时间片轮转**：基于定时器中断
- **任务等待**：TASK_Wait()实现延时
- **任务切换**：通过函数指针实现

```c
// 任务调度核心宏定义
#define TASK_SetTimer(cnt)  (TaskTimeCntNext = TaskTimeCnt + (cnt))
#define TASK_Wait()         {while(TaskTimeCntNext > TaskTimeCnt);}
```

### 4.3 中断系统
| 中断源 | 优先级 | 功能 | 频率 |
|--------|--------|------|------|
| PWM中断 | 最高 | 控制算法执行 | 50kHz |
| 定时器中断 | 高 | 任务调度 | 1kHz |
| 串口中断 | 中 | 数据收发 | 异步 |
| 外部中断 | 低 | 按键/触摸 | 异步 |

---

## 5. 核心功能模块

### 5.1 电源控制模块 (APK)

#### 5.1.1 电压电流采集
```c
void APK_VoltCurrCalc(void)
{
    // 电流采集 (mA)
    Curr[0] = (GET_AD_CH2_RAW_DATA - B_Curr[0]) * K_Curr[0] / 1000;
    Curr[1] = (GET_AD_CH4_RAW_DATA - B_Curr[1]) * K_Curr[1] / 1000;
    Curr[2] = (GET_AD_CH6_RAW_DATA - B_Curr[2]) * K_Curr[2] / 1000;

    // 电压采集 (mV)
    VoltL[0] = GET_AD_CH1_RAW_DATA * K_VoltL[0] / 1000;
    VoltL[1] = GET_AD_CH3_RAW_DATA * K_VoltL[1] / 1000;
    VoltL[2] = GET_AD_CH5_RAW_DATA * K_VoltL[2] / 1000;
}
```

**关键参数说明：**
- `K_VoltL[]`：电压校准系数（3位小数）
- `K_Curr[]`：电流校准系数（3位小数）
- `B_Curr[]`：电流偏移补偿
- `VoltL[]`：实际电压值（mV）
- `Curr[]`：实际电流值（mA）

#### 5.1.2 数字滤波器
```c
s32 APK_Mean1(s32 input_data, s32 reset)
{
    #define TEMP_FILTER_LEN (50)    // 滤波长度
    static s32 buffer[TEMP_FILTER_LEN];
    static s32 p = 0;
    static s32 sum = 0;

    if(reset) {
        // 重置滤波器
        sum = 0; p = 0;
        for(i = 0; i < TEMP_FILTER_LEN; i++) buffer[i] = 0;
        return 0;
    }

    // 滑动平均滤波
    sum -= buffer[p];           // 减去最老数据
    buffer[p] = input_data;     // 存入新数据
    sum += input_data;          // 加上新数据
    p = (p + 1) % TEMP_FILTER_LEN;

    return sum / TEMP_FILTER_LEN;   // 返回平均值
}
```

**滤波器特点：**
- **滑动平均**：50点滑动窗口
- **实时处理**：每次调用更新一个数据点
- **低通特性**：有效滤除高频噪声
- **可重置**：支持滤波器状态重置