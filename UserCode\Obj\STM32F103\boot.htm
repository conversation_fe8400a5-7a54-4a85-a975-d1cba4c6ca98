<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Obj\STM32F103\boot.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Obj\STM32F103\boot.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5050169: Last Updated: Sun Oct 21 13:58:23 2018
<BR><P>
<H3>Maximum Stack Usage =       1928 bytes + Unknown(Functions without stacksize, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
Apk_Main &rArr; APK_Common &rArr; CMD_MainTask &rArr; CMD_PackRun &rArr; XFLASH_GetDataFromUart &rArr; LCD_PutStrLeftTop &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[70]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[20]">ADC1_2_IRQHandler</a> from stm32f10x_it.o(i.ADC1_2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3d]">ADC3_IRQHandler</a> from stm32f10x_it.o(i.ADC3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[4e]">Apk_Main</a> from apk.o(i.Apk_Main) referenced from apk.o(i.APK_Init)
 <LI><a href="#[8]">BusFaultException</a> from stm32f10x_it.o(i.BusFaultException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[23]">CAN_RX1_IRQHandler</a> from stm32f10x_it.o(i.CAN_RX1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[24]">CAN_SCE_IRQHandler</a> from stm32f10x_it.o(i.CAN_SCE_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[19]">DMA1_Channel1_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel2_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel3_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel4_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel5_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1e]">DMA1_Channel6_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel6_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1f]">DMA1_Channel7_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel7_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[46]">DMA2_Channel1_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[47]">DMA2_Channel2_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[48]">DMA2_Channel3_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[49]">DMA2_Channel4_5_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[b]">DebugMonitor</a> from stm32f10x_it.o(i.DebugMonitor) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[14]">EXTI0_IRQHandler</a> from stm32f10x_it.o(i.EXTI0_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[36]">EXTI15_10_IRQHandler</a> from driver.o(i.EXTI15_10_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[15]">EXTI1_IRQHandler</a> from stm32f10x_it.o(i.EXTI1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[16]">EXTI2_IRQHandler</a> from stm32f10x_it.o(i.EXTI2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[17]">EXTI3_IRQHandler</a> from stm32f10x_it.o(i.EXTI3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[18]">EXTI4_IRQHandler</a> from stm32f10x_it.o(i.EXTI4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[25]">EXTI9_5_IRQHandler</a> from stm32f10x_it.o(i.EXTI9_5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[12]">FLASH_IRQHandler</a> from stm32f10x_it.o(i.FLASH_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3e]">FSMC_IRQHandler</a> from stm32f10x_it.o(i.FSMC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[6]">HardFaultException</a> from stm32f10x_it.o(i.HardFaultException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from stm32f10x_it.o(i.I2C1_ER_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from stm32f10x_it.o(i.I2C1_EV_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[30]">I2C2_ER_IRQHandler</a> from stm32f10x_it.o(i.I2C2_ER_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2f]">I2C2_EV_IRQHandler</a> from stm32f10x_it.o(i.I2C2_EV_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[7]">MemManageException</a> from stm32f10x_it.o(i.MemManageException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[5]">NMIException</a> from stm32f10x_it.o(i.NMIException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[f]">PVD_IRQHandler</a> from stm32f10x_it.o(i.PVD_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[c]">PendSVC</a> from stm32f10x_it.o(i.PendSVC) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[13]">RCC_IRQHandler</a> from stm32f10x_it.o(i.RCC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[37]">RTCAlarm_IRQHandler</a> from stm32f10x_it.o(i.RTCAlarm_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[11]">RTC_IRQHandler</a> from stm32f10x_it.o(i.RTC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[4]">Reset_Handler</a> from stm32f10x_vector.o(.text) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3f]">SDIO_IRQHandler</a> from stm32f10x_it.o(i.SDIO_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[31]">SPI1_IRQHandler</a> from stm32f10x_it.o(i.SPI1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[32]">SPI2_IRQHandler</a> from stm32f10x_it.o(i.SPI2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[41]">SPI3_IRQHandler</a> from stm32f10x_it.o(i.SPI3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[a]">SVCHandler</a> from stm32f10x_it.o(i.SVCHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[d]">SysTickHandler</a> from stm32f10x_it.o(i.SysTickHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[4a]">SystemInit</a> from main.o(i.SystemInit) referenced from stm32f10x_vector.o(.text)
 <LI><a href="#[10]">TAMPER_IRQHandler</a> from stm32f10x_it.o(i.TAMPER_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[4f]">TASK_Periodicity</a> from task.o(i.TASK_Periodicity) referenced from task.o(i.TASK_Init)
 <LI><a href="#[26]">TIM1_BRK_IRQHandler</a> from stm32f10x_it.o(i.TIM1_BRK_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[29]">TIM1_CC_IRQHandler</a> from stm32f10x_it.o(i.TIM1_CC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[28]">TIM1_TRG_COM_IRQHandler</a> from stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[27]">TIM1_UP_IRQHandler</a> from driver.o(i.TIM1_UP_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2a]">TIM2_IRQHandler</a> from stm32f10x_it.o(i.TIM2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2b]">TIM3_IRQHandler</a> from stm32f10x_it.o(i.TIM3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2c]">TIM4_IRQHandler</a> from stm32f10x_it.o(i.TIM4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[40]">TIM5_IRQHandler</a> from driver.o(i.TIM5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[44]">TIM6_IRQHandler</a> from stm32f10x_it.o(i.TIM6_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[45]">TIM7_IRQHandler</a> from stm32f10x_it.o(i.TIM7_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[39]">TIM8_BRK_IRQHandler</a> from stm32f10x_it.o(i.TIM8_BRK_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3c]">TIM8_CC_IRQHandler</a> from stm32f10x_it.o(i.TIM8_CC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3b]">TIM8_TRG_COM_IRQHandler</a> from stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3a]">TIM8_UP_IRQHandler</a> from stm32f10x_it.o(i.TIM8_UP_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[42]">UART4_IRQHandler</a> from stm32f10x_it.o(i.UART4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[43]">UART5_IRQHandler</a> from stm32f10x_it.o(i.UART5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[50]">UART_RxdIsr</a> from uart.o(i.UART_RxdIsr) referenced from uart.o(i.UART_Init)
 <LI><a href="#[51]">UART_TxdIsr</a> from uart.o(i.UART_TxdIsr) referenced from uart.o(i.UART_Init)
 <LI><a href="#[33]">USART1_IRQHandler</a> from stm32f10x_it.o(i.USART1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[34]">USART2_IRQHandler</a> from driver.o(i.USART2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[35]">USART3_IRQHandler</a> from stm32f10x_it.o(i.USART3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[38]">USBWakeUp_IRQHandler</a> from stm32f10x_it.o(i.USBWakeUp_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[21]">USB_HP_CAN_TX_IRQHandler</a> from stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[22]">USB_LP_CAN_RX0_IRQHandler</a> from stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[9]">UsageFaultException</a> from stm32f10x_it.o(i.UsageFaultException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[e]">WWDG_IRQHandler</a> from stm32f10x_it.o(i.WWDG_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[52]">XFLASH_UartRxdIsr</a> from xflash.o(i.XFLASH_UartRxdIsr) referenced from xflash.o(i.XFLASH_GetDataFromUart)
 <LI><a href="#[53]">__main</a> from __main.o(!!!main) referenced from stm32f10x_vector.o(.text)
 <LI><a href="#[4d]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[4c]">_sputc</a> from _sputc.o(.text) referenced from noretval__2sprintf.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[53]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[54]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[56]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[cc]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[cd]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[57]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[ce]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[58]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[6c]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[cf]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[5d]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[d0]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[d1]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[d2]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[d3]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[d4]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[d5]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[d6]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[d7]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[d8]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[d9]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[da]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[db]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[dc]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[dd]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[de]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[df]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[e0]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[e1]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[e2]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[e3]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[e4]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[62]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[e5]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[e6]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[e7]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[e8]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[e9]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000003))

<P><STRONG><a name="[ea]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B))

<P><STRONG><a name="[55]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[eb]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[5a]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[5c]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[ec]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[5e]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; TASK_Init &rArr; APK_Init &rArr; SPI_FLASH_Init &rArr; SPI_FLASH_ReadId
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[ed]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[71]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[61]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[ee]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[63]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[4]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_vector.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, stm32f10x_vector.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[81]"></a>__aeabi_ldivmod</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_ldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[65]"></a>_ll_sdiv</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, llsdiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[67]"></a>__2sprintf</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, noretval__2sprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[6d]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>

<P><STRONG><a name="[6e]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>

<P><STRONG><a name="[59]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[6a]"></a>__printf</STRONG> (Thumb, 308 bytes, Stack size 40 bytes, __printf_flags_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[ef]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[f0]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[f1]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[66]"></a>__aeabi_uldivmod</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_sdiv
</UL>

<P><STRONG><a name="[f2]"></a>_ll_udiv</STRONG> (Thumb, 238 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)

<P><STRONG><a name="[69]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[68]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>

<P><STRONG><a name="[4c]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> noretval__2sprintf.o(.text)
</UL>
<P><STRONG><a name="[f3]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[6f]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[f4]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[5b]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[60]"></a>exit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[64]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[f5]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[f6]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[f7]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[20]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.ADC1_2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>ADC3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.ADC3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>APK_Common</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, apk.o(i.APK_Common))
<BR><BR>[Stack]<UL><LI>Max Depth = 1816 + Unknown Stack Size
<LI>Call Chain = APK_Common &rArr; CMD_MainTask &rArr; CMD_PackRun &rArr; XFLASH_GetDataFromUart &rArr; LCD_PutStrLeftTop &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[c1]"></a>APK_Continuous</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, apk.o(i.APK_Continuous))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = APK_Continuous
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Run
</UL>

<P><STRONG><a name="[74]"></a>APK_Init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, apk.o(i.APK_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = APK_Init &rArr; SPI_FLASH_Init &rArr; SPI_FLASH_ReadId
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendStr
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_Init
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedInit
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBright
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Init
</UL>

<P><STRONG><a name="[bf]"></a>APK_Periodicity</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, apk.o(i.APK_Periodicity))
<BR><BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Periodicity
</UL>

<P><STRONG><a name="[4e]"></a>Apk_Main</STRONG> (Thumb, 422 bytes, Stack size 112 bytes, apk.o(i.Apk_Main))
<BR><BR>[Stack]<UL><LI>Max Depth = 1928 + Unknown Stack Size
<LI>Call Chain = Apk_Main &rArr; APK_Common &rArr; CMD_MainTask &rArr; CMD_PackRun &rArr; XFLASH_GetDataFromUart &rArr; LCD_PutStrLeftTop &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayMs
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendStr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBright
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SelectFont
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrRightCenter
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrLeftTop
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrCenter
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Common
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>
<BR>[Address Reference Count : 1]<UL><LI> apk.o(i.APK_Init)
</UL>
<P><STRONG><a name="[8]"></a>BusFaultException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.BusFaultException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = BusFaultException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN_RX1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.CAN_RX1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CAN_RX1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN_SCE_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.CAN_SCE_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CAN_SCE_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>CMD_Init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, cmd.o(i.CMD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMD_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_ClearRxdBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[73]"></a>CMD_MainTask</STRONG> (Thumb, 302 bytes, Stack size 16 bytes, cmd.o(i.CMD_MainTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 1808 + Unknown Stack Size
<LI>Call Chain = CMD_MainTask &rArr; CMD_PackRun &rArr; XFLASH_GetDataFromUart &rArr; LCD_PutStrLeftTop &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetRxdFifoLen
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetRxdData
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Common
</UL>

<P><STRONG><a name="[86]"></a>CMD_PackCheck</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, cmd.o(i.CMD_PackCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CMD_PackCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>

<P><STRONG><a name="[87]"></a>CMD_PackRun</STRONG> (Thumb, 196 bytes, Stack size 24 bytes, cmd.o(i.CMD_PackRun))
<BR><BR>[Stack]<UL><LI>Max Depth = 1792 + Unknown Stack Size
<LI>Call Chain = CMD_PackRun &rArr; XFLASH_GetDataFromUart &rArr; LCD_PutStrLeftTop &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_WriteData
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadData
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendStr
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrCenter
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>

<P><STRONG><a name="[8b]"></a>CloclkInit</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, driver.o(i.CloclkInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CloclkInit &rArr; RCC_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SYSCLKConfig
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLConfig
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLCmd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK2Config
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK1Config
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSEConfig
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HCLKConfig
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetSYSCLKSource
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_DeInit
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayInit
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_SetLatency
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_PrefetchBufferCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9a]"></a>CommonConfig</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, driver.o(i.CommonConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CommonConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[19]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel6_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel7_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA2_Channel4_5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel4_5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>DebugMonitor</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DebugMonitor))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DebugMonitor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[99]"></a>DelayInit</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, driver.o(i.DelayInit))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[80]"></a>DelayMs</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, driver.o(i.DelayMs))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481Init
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[b7]"></a>DelayUs</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, driver.o(i.DelayUs))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_Init
</UL>

<P><STRONG><a name="[14]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, driver.o(i.EXTI15_10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI15_10_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetITStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI9_5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI9_5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[9c]"></a>EXTI_GetITStatus</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f10x_exti.o(i.EXTI_GetITStatus))
<BR><BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[12]"></a>FLASH_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.FLASH_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FLASH_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>FLASH_PrefetchBufferCmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[91]"></a>FLASH_SetLatency</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_SetLatency))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[3e]"></a>FSMC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.FSMC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FSMC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[9f]"></a>GPIO_Init</STRONG> (Thumb, 276 bytes, Stack size 24 bytes, stm32f10x_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_InitGpio
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedInit
</UL>

<P><STRONG><a name="[ae]"></a>GPIO_PinRemapConfig</STRONG> (Thumb, 106 bytes, Stack size 20 bytes, stm32f10x_gpio.o(i.GPIO_PinRemapConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_PinRemapConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_InitGpio
</UL>

<P><STRONG><a name="[6]"></a>HardFaultException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.HardFaultException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HardFaultException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C1_ER_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C1_ER_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C1_EV_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C1_EV_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C2_ER_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C2_ER_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C2_EV_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C2_EV_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[9d]"></a>ILI9481Init</STRONG> (Thumb, 1308 bytes, Stack size 4 bytes, driver.o(i.ILI9481Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = ILI9481Init
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayMs
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[a7]"></a>ILI9481SetDisplayWindow</STRONG> (Thumb, 276 bytes, Stack size 12 bytes, driver.o(i.ILI9481SetDisplayWindow))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ILI9481SetDisplayWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBar
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>

<P><STRONG><a name="[a8]"></a>ILI9481WriteRamPrepare</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, driver.o(i.ILI9481WriteRamPrepare))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBar
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>

<P><STRONG><a name="[9e]"></a>LCD_BackLightInit</STRONG> (Thumb, 198 bytes, Stack size 40 bytes, driver.o(i.LCD_BackLightInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = LCD_BackLightInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4PreloadConfig
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBright
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[7b]"></a>LCD_Clear</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, lcd.o(i.LCD_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LCD_Clear &rArr; LCD_WriteConst
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteConst
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481WriteRamPrepare
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481SetDisplayWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[aa]"></a>LCD_DrawPoint</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, lcd.o(i.LCD_DrawPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = LCD_DrawPoint &rArr; ILI9481SetDisplayWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481WriteRamPrepare
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481SetDisplayWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutHanzi
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutChar
</UL>

<P><STRONG><a name="[ab]"></a>LCD_DrawProgress</STRONG> (Thumb, 330 bytes, Stack size 40 bytes, lcd.o(i.LCD_DrawProgress))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = LCD_DrawProgress &rArr; LCD_SetBar &rArr; LCD_WriteConst
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBar
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[7a]"></a>LCD_Init</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, lcd.o(i.LCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = LCD_Init &rArr; LCD_BackLightInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_InitGpio
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SelectFont
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[ad]"></a>LCD_InitGpio</STRONG> (Thumb, 226 bytes, Stack size 8 bytes, driver.o(i.LCD_InitGpio))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LCD_InitGpio &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinRemapConfig
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[af]"></a>LCD_PutChar</STRONG> (Thumb, 194 bytes, Stack size 1200 bytes, lcd.o(i.LCD_PutChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 1256<LI>Call Chain = LCD_PutChar &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SearchFont
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStr
</UL>

<P><STRONG><a name="[b1]"></a>LCD_PutHanzi</STRONG> (Thumb, 194 bytes, Stack size 1208 bytes, lcd.o(i.LCD_PutHanzi))
<BR><BR>[Stack]<UL><LI>Max Depth = 1264<LI>Call Chain = LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SearchFont
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStr
</UL>

<P><STRONG><a name="[b2]"></a>LCD_PutStr</STRONG> (Thumb, 294 bytes, Stack size 56 bytes, lcd.o(i.LCD_PutStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 1320<LI>Call Chain = LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutHanzi
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutChar
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrRightCenter
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrLeftTop
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrCenter
</UL>

<P><STRONG><a name="[7f]"></a>LCD_PutStrCenter</STRONG> (Thumb, 100 bytes, Stack size 72 bytes, lcd.o(i.LCD_PutStrCenter))
<BR><BR>[Stack]<UL><LI>Max Depth = 1392<LI>Call Chain = LCD_PutStrCenter &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStr
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StrLen
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[7e]"></a>LCD_PutStrLeftTop</STRONG> (Thumb, 54 bytes, Stack size 80 bytes, lcd.o(i.LCD_PutStrLeftTop))
<BR><BR>[Stack]<UL><LI>Max Depth = 1400<LI>Call Chain = LCD_PutStrLeftTop &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStr
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[82]"></a>LCD_PutStrRightCenter</STRONG> (Thumb, 98 bytes, Stack size 72 bytes, lcd.o(i.LCD_PutStrRightCenter))
<BR><BR>[Stack]<UL><LI>Max Depth = 1392<LI>Call Chain = LCD_PutStrRightCenter &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStr
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StrLen
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[b0]"></a>LCD_SearchFont</STRONG> (Thumb, 432 bytes, Stack size 40 bytes, lcd.o(i.LCD_SearchFont))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadData
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutHanzi
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutChar
</UL>

<P><STRONG><a name="[7d]"></a>LCD_SelectFont</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, lcd.o(i.LCD_SelectFont))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[ac]"></a>LCD_SetBar</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, lcd.o(i.LCD_SetBar))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = LCD_SetBar &rArr; LCD_WriteConst
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteConst
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481WriteRamPrepare
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481SetDisplayWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawProgress
</UL>

<P><STRONG><a name="[7c]"></a>LCD_SetBright</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, driver.o(i.LCD_SetBright))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[a9]"></a>LCD_WriteConst</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, driver.o(i.LCD_WriteConst))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LCD_WriteConst
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBar
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>

<P><STRONG><a name="[76]"></a>LedInit</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, driver.o(i.LedInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LedInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[ca]"></a>LrcCalc</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, common.o(i.LrcCalc))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LrcCalc
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[7]"></a>MemManageException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.MemManageException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MemManageException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>NMIException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.NMIException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NMIException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[c3]"></a>NVIC_Init</STRONG> (Thumb, 150 bytes, Stack size 20 bytes, stm32f10x_nvic.o(i.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
</UL>

<P><STRONG><a name="[9b]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig))
<BR><BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommonConfig
</UL>

<P><STRONG><a name="[cb]"></a>Num2Str</STRONG> (Thumb, 350 bytes, Stack size 24 bytes, common.o(i.Num2Str))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Num2Str
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[f]"></a>PVD_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.PVD_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PVD_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>PendSVC</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.PendSVC))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PendSVC
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[ba]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
</UL>

<P><STRONG><a name="[98]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_InitGpio
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
</UL>

<P><STRONG><a name="[8c]"></a>RCC_DeInit</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[c7]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 192 bytes, Stack size 12 bytes, stm32f10x_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[8f]"></a>RCC_GetFlagStatus</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f10x_rcc.o(i.RCC_GetFlagStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[97]"></a>RCC_GetSYSCLKSource</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_GetSYSCLKSource))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[92]"></a>RCC_HCLKConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_HCLKConfig))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[8d]"></a>RCC_HSEConfig</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_HSEConfig))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[13]"></a>RCC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.RCC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[94]"></a>RCC_PCLK1Config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PCLK1Config))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[93]"></a>RCC_PCLK2Config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PCLK2Config))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[8e]"></a>RCC_PLLCmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PLLCmd))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[95]"></a>RCC_PLLConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PLLConfig))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[96]"></a>RCC_SYSCLKConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_SYSCLKConfig))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[37]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.RTCAlarm_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RTCAlarm_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RTC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.RTC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RTC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SDIO_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SDIO_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDIO_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SPI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>SPI2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SPI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>SPI3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SPI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[bc]"></a>SPI_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_spi.o(i.SPI_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
</UL>

<P><STRONG><a name="[b4]"></a>SPI_FLASH_EraseSector</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, driver.o(i.SPI_FLASH_EraseSector))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_FLASH_EraseSector
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_WriteData
</UL>

<P><STRONG><a name="[75]"></a>SPI_FLASH_Init</STRONG> (Thumb, 160 bytes, Stack size 8 bytes, driver.o(i.SPI_FLASH_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = SPI_FLASH_Init &rArr; SPI_FLASH_ReadId
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadId
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[8a]"></a>SPI_FLASH_ReadData</STRONG> (Thumb, 216 bytes, Stack size 16 bytes, driver.o(i.SPI_FLASH_ReadData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SearchFont
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_WriteData
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
</UL>

<P><STRONG><a name="[b8]"></a>SPI_FLASH_ReadId</STRONG> (Thumb, 406 bytes, Stack size 112 bytes, driver.o(i.SPI_FLASH_ReadId))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = SPI_FLASH_ReadId
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_Init
</UL>

<P><STRONG><a name="[b5]"></a>SPI_FLASH_WaitBusy</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, driver.o(i.SPI_FLASH_WaitBusy))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WriteSector
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadId
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadData
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_EraseSector
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_Init
</UL>

<P><STRONG><a name="[b9]"></a>SPI_FLASH_WriteSector</STRONG> (Thumb, 354 bytes, Stack size 24 bytes, driver.o(i.SPI_FLASH_WriteSector))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SPI_FLASH_WriteSector
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_WriteData
</UL>

<P><STRONG><a name="[bb]"></a>SPI_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stm32f10x_spi.o(i.SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
</UL>

<P><STRONG><a name="[a]"></a>SVCHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SVCHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SVCHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[b6]"></a>Spi3Init</STRONG> (Thumb, 216 bytes, Stack size 32 bytes, driver.o(i.Spi3Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Spi3Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinRemapConfig
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Cmd
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_Init
</UL>

<P><STRONG><a name="[b3]"></a>StrLen</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, common.o(i.StrLen))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrRightCenter
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrCenter
</UL>

<P><STRONG><a name="[d]"></a>SysTickHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SysTickHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTickHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>SystemInit</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, main.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SystemInit
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayMs
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(.text)
</UL>
<P><STRONG><a name="[10]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TAMPER_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TAMPER_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[bd]"></a>TASK_Init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, task.o(i.TASK_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = TASK_Init &rArr; APK_Init &rArr; SPI_FLASH_Init &rArr; SPI_FLASH_ReadId
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4f]"></a>TASK_Periodicity</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, task.o(i.TASK_Periodicity))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TASK_Periodicity
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Periodicity
</UL>
<BR>[Address Reference Count : 1]<UL><LI> task.o(i.TASK_Init)
</UL>
<P><STRONG><a name="[c0]"></a>TASK_Run</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, task.o(i.TASK_Run))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TASK_Run &rArr; APK_Continuous
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Continuous
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[be]"></a>TASK_TimerInit</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, driver.o(i.TASK_TimerInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TASK_TimerInit &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Init
</UL>

<P><STRONG><a name="[26]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM1_BRK_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_BRK_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM1_CC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_CC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_TRG_COM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, driver.o(i.TIM1_UP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_UP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM5_IRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, driver.o(i.TIM5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM6_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM6_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>TIM7_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM7_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_BRK_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_BRK_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_CC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_CC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_TRG_COM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_UP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_UP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[a3]"></a>TIM_ARRPreloadConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ARRPreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
</UL>

<P><STRONG><a name="[a4]"></a>TIM_ClearFlag</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
</UL>

<P><STRONG><a name="[a5]"></a>TIM_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Run
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
</UL>

<P><STRONG><a name="[a6]"></a>TIM_CtrlPWMOutputs</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_CtrlPWMOutputs))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
</UL>

<P><STRONG><a name="[c2]"></a>TIM_ITConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ITConfig))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
</UL>

<P><STRONG><a name="[a0]"></a>TIM_OC4Init</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, stm32f10x_tim.o(i.TIM_OC4Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC4Init
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
</UL>

<P><STRONG><a name="[a1]"></a>TIM_OC4PreloadConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_OC4PreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
</UL>

<P><STRONG><a name="[a2]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_TimeBaseInit))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
</UL>

<P><STRONG><a name="[42]"></a>UART4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.UART4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>UART5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.UART5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[83]"></a>UART_ClearRxdBuffer</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, uart.o(i.UART_ClearRxdBuffer))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_Init
</UL>

<P><STRONG><a name="[84]"></a>UART_GetRxdData</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, uart.o(i.UART_GetRxdData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_GetRxdData
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>

<P><STRONG><a name="[85]"></a>UART_GetRxdFifoLen</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, uart.o(i.UART_GetRxdFifoLen))
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxdIsr
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>

<P><STRONG><a name="[77]"></a>UART_Init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, uart.o(i.UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = UART_Init &rArr; Uart2Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[50]"></a>UART_RxdIsr</STRONG> (Thumb, 50 bytes, Stack size 4 bytes, uart.o(i.UART_RxdIsr))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = UART_RxdIsr
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetRxdFifoLen
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart.o(i.UART_Init)
</UL>
<P><STRONG><a name="[c5]"></a>UART_SendData</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, uart.o(i.UART_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = UART_SendData &rArr; UART_TxdIsr
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_TxdIsr
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendStr
</UL>

<P><STRONG><a name="[79]"></a>UART_SendStr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, uart.o(i.UART_SendStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_SendStr &rArr; UART_SendData &rArr; UART_TxdIsr
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[51]"></a>UART_TxdIsr</STRONG> (Thumb, 58 bytes, Stack size 12 bytes, uart.o(i.UART_TxdIsr))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = UART_TxdIsr
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendData
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart.o(i.UART_Init)
</UL>
<P><STRONG><a name="[33]"></a>USART1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART2_IRQHandler</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, driver.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USART3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[c8]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
</UL>

<P><STRONG><a name="[c9]"></a>USART_ITConfig</STRONG> (Thumb, 64 bytes, Stack size 20 bytes, stm32f10x_usart.o(i.USART_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
</UL>

<P><STRONG><a name="[c6]"></a>USART_Init</STRONG> (Thumb, 150 bytes, Stack size 56 bytes, stm32f10x_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
</UL>

<P><STRONG><a name="[38]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USBWakeUp_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBWakeUp_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>USB_HP_CAN_TX_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_HP_CAN_TX_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>USB_LP_CAN_RX0_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_LP_CAN_RX0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[c4]"></a>Uart2Init</STRONG> (Thumb, 174 bytes, Stack size 32 bytes, driver.o(i.Uart2Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = Uart2Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
</UL>

<P><STRONG><a name="[9]"></a>UsageFaultException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.UsageFaultException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UsageFaultException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>WWDG_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.WWDG_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WWDG_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[88]"></a>XFLASH_GetDataFromUart</STRONG> (Thumb, 1464 bytes, Stack size 368 bytes, xflash.o(i.XFLASH_GetDataFromUart))
<BR><BR>[Stack]<UL><LI>Max Depth = 1768 + Unknown Stack Size
<LI>Call Chain = XFLASH_GetDataFromUart &rArr; LCD_PutStrLeftTop &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayMs
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendData
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawProgress
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Num2Str
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LrcCalc
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_WriteData
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadData
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayUs
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SelectFont
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrLeftTop
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
</UL>

<P><STRONG><a name="[52]"></a>XFLASH_UartRxdIsr</STRONG> (Thumb, 228 bytes, Stack size 0 bytes, xflash.o(i.XFLASH_UartRxdIsr))
<BR>[Address Reference Count : 1]<UL><LI> xflash.o(i.XFLASH_GetDataFromUart)
</UL>
<P><STRONG><a name="[89]"></a>XFLASH_WriteData</STRONG> (Thumb, 342 bytes, Stack size 56 bytes, xflash.o(i.XFLASH_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = XFLASH_WriteData &rArr; SPI_FLASH_WriteSector
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WriteSector
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadData
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_EraseSector
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
</UL>

<P><STRONG><a name="[6b]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[5f]"></a>main</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = main &rArr; TASK_Init &rArr; APK_Init &rArr; SPI_FLASH_Init &rArr; SPI_FLASH_ReadId
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Run
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommonConfig
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[4d]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
