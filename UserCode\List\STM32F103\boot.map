Component: ARM Compiler 5.05 update 2 (build 169) Tool: armlink [4d0f33]

==============================================================================

Section Cross References

    stm32f10x_vector.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(RESET) refers to stm32f10x_vector.o(STACK) for __initial_sp
    stm32f10x_vector.o(RESET) refers to stm32f10x_vector.o(.text) for Reset_Handler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.NMIException) for NMIException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.HardFaultException) for HardFaultException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.MemManageException) for MemManageException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.BusFaultException) for BusFaultException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.UsageFaultException) for UsageFaultException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SVCHandler) for SVCHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DebugMonitor) for DebugMonitor
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.PendSVC) for PendSVC
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SysTickHandler) for SysTickHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.WWDG_IRQHandler) for WWDG_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.PVD_IRQHandler) for PVD_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TAMPER_IRQHandler) for TAMPER_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.RTC_IRQHandler) for RTC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.FLASH_IRQHandler) for FLASH_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.RCC_IRQHandler) for RCC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel3_IRQHandler) for DMA1_Channel3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel4_IRQHandler) for DMA1_Channel4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel6_IRQHandler) for DMA1_Channel6_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel7_IRQHandler) for DMA1_Channel7_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.ADC1_2_IRQHandler) for ADC1_2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler) for USB_HP_CAN_TX_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler) for USB_LP_CAN_RX0_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.CAN_RX1_IRQHandler) for CAN_RX1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.CAN_SCE_IRQHandler) for CAN_SCE_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI9_5_IRQHandler) for EXTI9_5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM1_BRK_IRQHandler) for TIM1_BRK_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.TIM1_UP_IRQHandler) for TIM1_UP_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler) for TIM1_TRG_COM_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM1_CC_IRQHandler) for TIM1_CC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C1_EV_IRQHandler) for I2C1_EV_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C1_ER_IRQHandler) for I2C1_ER_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C2_EV_IRQHandler) for I2C2_EV_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C2_ER_IRQHandler) for I2C2_ER_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SPI1_IRQHandler) for SPI1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SPI2_IRQHandler) for SPI2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.USART2_IRQHandler) for USART2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.EXTI15_10_IRQHandler) for EXTI15_10_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.RTCAlarm_IRQHandler) for RTCAlarm_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USBWakeUp_IRQHandler) for USBWakeUp_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_BRK_IRQHandler) for TIM8_BRK_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_UP_IRQHandler) for TIM8_UP_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler) for TIM8_TRG_COM_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_CC_IRQHandler) for TIM8_CC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.ADC3_IRQHandler) for ADC3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.FSMC_IRQHandler) for FSMC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.TIM5_IRQHandler) for TIM5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SPI3_IRQHandler) for SPI3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM6_IRQHandler) for TIM6_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel1_IRQHandler) for DMA2_Channel1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel2_IRQHandler) for DMA2_Channel2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel3_IRQHandler) for DMA2_Channel3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler) for DMA2_Channel4_5_IRQHandler
    stm32f10x_vector.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(.text) refers to main.o(i.SystemInit) for SystemInit
    stm32f10x_vector.o(.text) refers to __main.o(!!!main) for __main
    stm32f10x_vector.o(.text) refers to stm32f10x_vector.o(HEAP) for Heap_Mem
    stm32f10x_vector.o(.text) refers to stm32f10x_vector.o(STACK) for Stack_Mem
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_nvic.o(i.NVIC_BASEPRICONFIG) refers to cortexm3_macro.o(.text) for __BASEPRICONFIG
    stm32f10x_nvic.o(i.NVIC_GetBASEPRI) refers to cortexm3_macro.o(.text) for __GetBASEPRI
    stm32f10x_nvic.o(i.NVIC_RESETFAULTMASK) refers to cortexm3_macro.o(.text) for __RESETFAULTMASK
    stm32f10x_nvic.o(i.NVIC_RESETPRIMASK) refers to cortexm3_macro.o(.text) for __RESETPRIMASK
    stm32f10x_nvic.o(i.NVIC_SETFAULTMASK) refers to cortexm3_macro.o(.text) for __SETFAULTMASK
    stm32f10x_nvic.o(i.NVIC_SETPRIMASK) refers to cortexm3_macro.o(.text) for __SETPRIMASK
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode) refers to cortexm3_macro.o(.text) for __WFI
    stm32f10x_pwr.o(i.PWR_EnterSTOPMode) refers to cortexm3_macro.o(.text) for __WFI
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.constdata) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(.data) for StartUpCounter
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.delay) for delay
    main.o(i.SystemInit) refers to driver.o(i.DelayInit) for DelayInit
    main.o(i.SystemInit) refers to driver.o(i.DelayMs) for DelayMs
    main.o(i.SystemInit) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    main.o(i.SystemInit) refers to main.o(.data) for SystemCoreClock
    main.o(i.main) refers to driver.o(i.CloclkInit) for CloclkInit
    main.o(i.main) refers to driver.o(i.CommonConfig) for CommonConfig
    main.o(i.main) refers to task.o(i.TASK_Init) for TASK_Init
    main.o(i.main) refers to task.o(i.TASK_Run) for TASK_Run
    stm32f10x_it.o(i.ADC1_2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.ADC3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.BusFaultException) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.CAN_RX1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.CAN_SCE_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel4_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel6_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel7_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DebugMonitor) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI15_10_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI4_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI9_5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.FLASH_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.FSMC_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.HardFaultException) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C1_ER_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C1_EV_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C2_ER_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C2_EV_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.MemManageException) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.NMIException) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.PVD_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.PendSVC) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.RCC_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.RTCAlarm_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.RTC_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SDIO_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SPI1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SPI2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SPI3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SVCHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SysTickHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TAMPER_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_BRK_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_CC_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_UP_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM4_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM6_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM7_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_BRK_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_CC_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_UP_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.UART4_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.UART5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USART1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USART2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USART3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USBWakeUp_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.UsageFaultException) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.WWDG_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    apk.o(i.APK_Common) refers to cmd.o(i.CMD_MainTask) for CMD_MainTask
    apk.o(i.APK_Continuous) refers to apk.o(.data) for ptrApkTask
    apk.o(i.APK_Init) refers to driver.o(i.SPI_FLASH_Init) for SPI_FLASH_Init
    apk.o(i.APK_Init) refers to driver.o(i.LedInit) for LedInit
    apk.o(i.APK_Init) refers to uart.o(i.UART_Init) for UART_Init
    apk.o(i.APK_Init) refers to cmd.o(i.CMD_Init) for CMD_Init
    apk.o(i.APK_Init) refers to uart.o(i.UART_SendStr) for UART_SendStr
    apk.o(i.APK_Init) refers to lcd.o(i.LCD_Init) for LCD_Init
    apk.o(i.APK_Init) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    apk.o(i.APK_Init) refers to driver.o(i.LCD_SetBright) for LCD_SetBright
    apk.o(i.APK_Init) refers to apk.o(i.Apk_Main) for Apk_Main
    apk.o(i.APK_Init) refers to apk.o(.data) for ptrApkTask
    apk.o(i.APK_Init) refers to task.o(.data) for TaskTimeCntNext
    apk.o(i.APK_Jump) refers to apk.o(.data) for ptrApkTask
    apk.o(i.APK_Jump2Pre) refers to apk.o(.data) for ptrApkTaskPre
    apk.o(i.Apk_Main) refers to _printf_pad.o(.text) for _printf_pre_padding
    apk.o(i.Apk_Main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    apk.o(i.Apk_Main) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    apk.o(i.Apk_Main) refers to _printf_dec.o(.text) for _printf_int_dec
    apk.o(i.Apk_Main) refers to lcd.o(i.LCD_SelectFont) for LCD_SelectFont
    apk.o(i.Apk_Main) refers to lcd.o(i.LCD_PutStrLeftTop) for LCD_PutStrLeftTop
    apk.o(i.Apk_Main) refers to lcd.o(i.LCD_PutStrCenter) for LCD_PutStrCenter
    apk.o(i.Apk_Main) refers to driver.o(i.LCD_SetBright) for LCD_SetBright
    apk.o(i.Apk_Main) refers to driver.o(i.DelayMs) for DelayMs
    apk.o(i.Apk_Main) refers to apk.o(i.APK_Common) for APK_Common
    apk.o(i.Apk_Main) refers to llsdiv.o(.text) for __aeabi_ldivmod
    apk.o(i.Apk_Main) refers to noretval__2sprintf.o(.text) for __2sprintf
    apk.o(i.Apk_Main) refers to uart.o(i.UART_SendStr) for UART_SendStr
    apk.o(i.Apk_Main) refers to lcd.o(i.LCD_PutStrRightCenter) for LCD_PutStrRightCenter
    apk.o(i.Apk_Main) refers to font.o(.constdata) for FONT_24
    apk.o(i.Apk_Main) refers to task.o(.data) for TaskTimeCnt
    driver.o(i.AdcInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.AdcInit) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    driver.o(i.AdcInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig) for ADC_InjectedSequencerLengthConfig
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_InjectedChannelConfig) for ADC_InjectedChannelConfig
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd) for ADC_AutoInjectedConvCmd
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    driver.o(i.AdcInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_DeInit) for RCC_DeInit
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_HSEConfig) for RCC_HSEConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PLLCmd) for RCC_PLLCmd
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    driver.o(i.CloclkInit) refers to stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd) for FLASH_PrefetchBufferCmd
    driver.o(i.CloclkInit) refers to stm32f10x_flash.o(i.FLASH_SetLatency) for FLASH_SetLatency
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_HCLKConfig) for RCC_HCLKConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PCLK2Config) for RCC_PCLK2Config
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PCLK1Config) for RCC_PCLK1Config
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PLLConfig) for RCC_PLLConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_SYSCLKConfig) for RCC_SYSCLKConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_GetSYSCLKSource) for RCC_GetSYSCLKSource
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.CloclkInit) refers to driver.o(i.DelayInit) for DelayInit
    driver.o(i.CloclkInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.CommonConfig) refers to stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    driver.o(i.DacInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.DacInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.DacInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.DacInit) refers to stm32f10x_dac.o(i.DAC_Init) for DAC_Init
    driver.o(i.DacInit) refers to stm32f10x_dac.o(i.DAC_Cmd) for DAC_Cmd
    driver.o(i.DelayInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.DelayInit) refers to driver.o(.data) for fac_us
    driver.o(i.DelayMs) refers to driver.o(.data) for fac_ms
    driver.o(i.DelayUs) refers to driver.o(.data) for fac_us
    driver.o(i.EXTI15_10_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    driver.o(i.EXTI15_10_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    driver.o(i.EXTI15_10_IRQHandler) refers to driver.o(.data) for IrExtiIsr
    driver.o(i.ILI9481Init) refers to driver.o(i.DelayMs) for DelayMs
    driver.o(i.IrInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.IrInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.IrInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.IrInit) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    driver.o(i.IrInit) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    driver.o(i.IrInit) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.IrInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.IrInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.IrInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.IrInit) refers to driver.o(.data) for IrExtiIsr
    driver.o(i.IrInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    driver.o(i.LCD_BackLightInit) refers to driver.o(i.LCD_SetBright) for LCD_SetBright
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    driver.o(i.LCD_BackLightInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.LCD_InitGpio) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.LCD_InitGpio) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.LCD_InitGpio) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    driver.o(i.LCD_WriteArrayFromXflash) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    driver.o(i.LCD_WriteArrayFromXflash) refers to xflash.o(.bss) for XFLASH_TempBuffer
    driver.o(i.LedInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.OnChipFlashErasePage) refers to stm32f10x_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    driver.o(i.OnChipFlashErasePage) refers to stm32f10x_flash.o(i.FLASH_ClearFlag) for FLASH_ClearFlag
    driver.o(i.OnChipFlashErasePage) refers to stm32f10x_flash.o(i.FLASH_ErasePage) for FLASH_ErasePage
    driver.o(i.OnChipFlashErasePage) refers to stm32f10x_flash.o(i.FLASH_Lock) for FLASH_Lock
    driver.o(i.OnChipFlashWritePageData) refers to stm32f10x_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    driver.o(i.OnChipFlashWritePageData) refers to stm32f10x_flash.o(i.FLASH_ClearFlag) for FLASH_ClearFlag
    driver.o(i.OnChipFlashWritePageData) refers to stm32f10x_flash.o(i.FLASH_ProgramWord) for FLASH_ProgramWord
    driver.o(i.OnChipFlashWritePageData) refers to stm32f10x_flash.o(i.FLASH_Lock) for FLASH_Lock
    driver.o(i.PwmInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.PwmInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.PwmInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_BDTRConfig) for TIM_BDTRConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.PwmInit) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.PwmInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.SPI_FLASH_EraseSector) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.Spi3Init) for Spi3Init
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.DelayUs) for DelayUs
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.SPI_FLASH_ReadId) for SPI_FLASH_ReadId
    driver.o(i.SPI_FLASH_ReadData) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_ReadId) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_ReadId) refers to driver.o(.data) for ExFlashId
    driver.o(i.SPI_FLASH_WriteSector) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.Spi3Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.Spi3Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.Spi3Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.Spi3Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    driver.o(i.Spi3Init) refers to stm32f10x_spi.o(i.SPI_Init) for SPI_Init
    driver.o(i.Spi3Init) refers to stm32f10x_spi.o(i.SPI_Cmd) for SPI_Cmd
    driver.o(i.TASK_TimerInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    driver.o(i.TASK_TimerInit) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.TASK_TimerInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.TASK_TimerInit) refers to driver.o(.data) for TaskTimerIsr
    driver.o(i.TIM1_UP_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    driver.o(i.TIM5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    driver.o(i.TIM5_IRQHandler) refers to driver.o(.data) for TaskTimerIsr
    driver.o(i.TP_Init) refers to driver.o(.data) for TP_TouchNum
    driver.o(i.TP_TouchScan) refers to driver.o(.data) for TP_TouchNum
    driver.o(i.USART2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    driver.o(i.USART2_IRQHandler) refers to driver.o(.data) for Uart2TxdIsr
    driver.o(i.Uart2Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.Uart2Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.Uart2Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    driver.o(i.Uart2Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    driver.o(i.Uart2Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    driver.o(i.Uart2Init) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.Uart2Init) refers to driver.o(.data) for Uart2RxdIsr
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd) for IWDG_WriteAccessCmd
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_SetPrescaler) for IWDG_SetPrescaler
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_SetReload) for IWDG_SetReload
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_ReloadCounter) for IWDG_ReloadCounter
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_Enable) for IWDG_Enable
    cmd.o(i.CMD_Init) refers to uart.o(i.UART_ClearRxdBuffer) for UART_ClearRxdBuffer
    cmd.o(i.CMD_Init) refers to cmd.o(.data) for timeout_cnt
    cmd.o(i.CMD_Init) refers to cmd.o(.bss) for CMD_Pack
    cmd.o(i.CMD_MainTask) refers to uart.o(i.UART_GetRxdData) for UART_GetRxdData
    cmd.o(i.CMD_MainTask) refers to uart.o(i.UART_GetRxdFifoLen) for UART_GetRxdFifoLen
    cmd.o(i.CMD_MainTask) refers to cmd.o(i.CMD_PackCheck) for CMD_PackCheck
    cmd.o(i.CMD_MainTask) refers to cmd.o(i.CMD_PackRun) for CMD_PackRun
    cmd.o(i.CMD_MainTask) refers to cmd.o(.data) for PackTimeout
    cmd.o(i.CMD_MainTask) refers to cmd.o(.bss) for CMD_Pack
    cmd.o(i.CMD_PackRun) refers to uart.o(i.UART_SendStr) for UART_SendStr
    cmd.o(i.CMD_PackRun) refers to xflash.o(i.XFLASH_GetDataFromUart) for XFLASH_GetDataFromUart
    cmd.o(i.CMD_PackRun) refers to xflash.o(i.XFLASH_WriteData) for XFLASH_WriteData
    cmd.o(i.CMD_PackRun) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    cmd.o(i.CMD_PackRun) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    cmd.o(i.CMD_PackRun) refers to lcd.o(i.LCD_PutStrCenter) for LCD_PutStrCenter
    cmd.o(i.CMD_PackRun) refers to cmd.o(.bss) for CMD_Pack
    common.o(i.Str2Double) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    common.o(i.Str2Double) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    common.o(i.Str2Double) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    common.o(i.Str2Double) refers to pow.o(i.pow) for pow
    ir.o(i.IR_Decode) refers to ir.o(.data) for IR_start
    ir.o(i.IR_Init) refers to driver.o(i.IrInit) for IrInit
    ir.o(i.IR_Init) refers to ir.o(i.IR_Decode) for IR_Decode
    ir.o(i.IR_Init) refers to driver.o(.data) for IrExtiIsr
    ir.o(i.IR_Init) refers to ir.o(.data) for IR_LedTimeout
    lcd.o(i.LCD_Clear) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_Clear) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_Clear) refers to driver.o(i.LCD_WriteConst) for LCD_WriteConst
    lcd.o(i.LCD_DrawBmpFromRawData) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_DrawBmpFromRawData) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_DrawBmpFromRawData) refers to driver.o(i.LCD_WriteArray) for LCD_WriteArray
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.LCD_WriteArrayFromXflash) for LCD_WriteArrayFromXflash
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to xflash.o(.bss) for XFLASH_TempBuffer
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to lcd.o(.data) for PageId
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to touch.o(.data) for TouchRectInfoNum
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to touch.o(.bss) for TouchRectInfo
    lcd.o(i.LCD_DrawChildPic) refers to lcd.o(i.LCD_DrawBmpFromXflashPackData) for LCD_DrawBmpFromXflashPackData
    lcd.o(i.LCD_DrawChildPic) refers to lcd.o(.data) for PageId
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_DrawPage) refers to lcd.o(i.LCD_DrawBmpFromXflashPackData) for LCD_DrawBmpFromXflashPackData
    lcd.o(i.LCD_DrawPageCenter) refers to lcd.o(i.LCD_DrawBmpFromXflashPackData) for LCD_DrawBmpFromXflashPackData
    lcd.o(i.LCD_DrawPoint) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_DrawPoint) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_DrawProgress) refers to lcd.o(i.LCD_SetBar) for LCD_SetBar
    lcd.o(i.LCD_Init) refers to driver.o(i.LCD_InitGpio) for LCD_InitGpio
    lcd.o(i.LCD_Init) refers to driver.o(i.ILI9481Init) for ILI9481Init
    lcd.o(i.LCD_Init) refers to driver.o(i.LCD_BackLightInit) for LCD_BackLightInit
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_SelectFont) for LCD_SelectFont
    lcd.o(i.LCD_Init) refers to lcd.o(.data) for PageOffsetX
    lcd.o(i.LCD_Init) refers to font.o(.constdata) for FONT_32
    lcd.o(i.LCD_PutChar) refers to lcd.o(i.LCD_SearchFont) for LCD_SearchFont
    lcd.o(i.LCD_PutChar) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_PutChar) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_PutHanzi) refers to lcd.o(i.LCD_SearchFont) for LCD_SearchFont
    lcd.o(i.LCD_PutHanzi) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_PutHanzi) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_PutStr) refers to lcd.o(i.LCD_PutHanzi) for LCD_PutHanzi
    lcd.o(i.LCD_PutStr) refers to lcd.o(i.LCD_PutChar) for LCD_PutChar
    lcd.o(i.LCD_PutStr) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_PutStrCenter) refers to common.o(i.StrLen) for StrLen
    lcd.o(i.LCD_PutStrCenter) refers to lcd.o(i.LCD_PutStr) for LCD_PutStr
    lcd.o(i.LCD_PutStrCenter) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_PutStrLeftTop) refers to lcd.o(i.LCD_PutStr) for LCD_PutStr
    lcd.o(i.LCD_PutStrRightCenter) refers to common.o(i.StrLen) for StrLen
    lcd.o(i.LCD_PutStrRightCenter) refers to lcd.o(i.LCD_PutStr) for LCD_PutStr
    lcd.o(i.LCD_PutStrRightCenter) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_SearchFont) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    lcd.o(i.LCD_SearchFont) refers to lcd.o(.data) for FontExternalFlag
    lcd.o(i.LCD_SearchFont) refers to xfont.o(.bss) for XFONT_CurrHeader
    lcd.o(i.LCD_SearchFont) refers to xfont.o(.data) for FontDataLenAscii
    lcd.o(i.LCD_SelectFont) refers to lcd.o(.data) for Font_ASCII_p
    lcd.o(i.LCD_SetBar) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_SetBar) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_SetBar) refers to driver.o(i.LCD_WriteConst) for LCD_WriteConst
    lcd.o(i.LCD_SetCursor) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    touch.o(i.TOUCH_DispPos) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    touch.o(i.TOUCH_DispPos) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    touch.o(i.TOUCH_DispPos) refers to _printf_dec.o(.text) for _printf_int_dec
    touch.o(i.TOUCH_DispPos) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    touch.o(i.TOUCH_DispPos) refers to lcd.o(i.LCD_PutStrLeftTop) for LCD_PutStrLeftTop
    touch.o(i.TOUCH_DispPos) refers to noretval__2sprintf.o(.text) for __2sprintf
    touch.o(i.TOUCH_DispPos) refers to driver.o(i.TP_TouchScan) for TP_TouchScan
    touch.o(i.TOUCH_DispPos) refers to common.o(i.Num2Str) for Num2Str
    touch.o(i.TOUCH_DispPos) refers to driver.o(i.DelayMs) for DelayMs
    touch.o(i.TOUCH_DispPos) refers to touch.o(.bss) for TouchState
    touch.o(i.TOUCH_DispPos) refers to driver.o(.data) for TP_TouchNum
    touch.o(i.TOUCH_DispPos) refers to driver.o(.bss) for TP_TouchPoint
    touch.o(i.TOUCH_DrawLine) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    touch.o(i.TOUCH_DrawLine) refers to driver.o(i.TP_TouchScan) for TP_TouchScan
    touch.o(i.TOUCH_DrawLine) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    touch.o(i.TOUCH_DrawLine) refers to driver.o(i.DelayMs) for DelayMs
    touch.o(i.TOUCH_DrawLine) refers to driver.o(.data) for TP_TouchNum
    touch.o(i.TOUCH_DrawLine) refers to driver.o(.bss) for TP_TouchPoint
    touch.o(i.TOUCH_GetIdByPoint) refers to touch.o(.bss) for TouchRectInfo
    touch.o(i.TOUCH_GetIdByPoint) refers to touch.o(.data) for TouchRectInfoNum
    touch.o(i.TOUCH_GetState) refers to driver.o(i.TP_TouchScan) for TP_TouchScan
    touch.o(i.TOUCH_GetState) refers to touch.o(.data) for num_last
    touch.o(i.TOUCH_GetState) refers to driver.o(.data) for TP_TouchNum
    touch.o(i.TOUCH_GetState) refers to touch.o(.bss) for TouchState
    touch.o(i.TOUCH_GetState) refers to driver.o(.bss) for TP_TouchPoint
    uart.o(i.UART_ClearRxdBuffer) refers to uart.o(.data) for RxdFifoDataFront
    uart.o(i.UART_ClearRxdBuffer) refers to uart.o(.bss) for RxdFifoData
    uart.o(i.UART_GetRxdData) refers to uart.o(.bss) for RxdFifoData
    uart.o(i.UART_GetRxdData) refers to uart.o(.data) for RxdFifoDataFront
    uart.o(i.UART_GetRxdFifoLen) refers to uart.o(.data) for RxdFifoDataRear
    uart.o(i.UART_Init) refers to driver.o(i.Uart2Init) for Uart2Init
    uart.o(i.UART_Init) refers to uart.o(.data) for UART_InitFlag
    uart.o(i.UART_Init) refers to uart.o(i.UART_RxdIsr) for UART_RxdIsr
    uart.o(i.UART_Init) refers to driver.o(.data) for Uart2RxdIsr
    uart.o(i.UART_Init) refers to uart.o(i.UART_TxdIsr) for UART_TxdIsr
    uart.o(i.UART_RxdIsr) refers to uart.o(i.UART_GetRxdFifoLen) for UART_GetRxdFifoLen
    uart.o(i.UART_RxdIsr) refers to uart.o(.bss) for RxdFifoData
    uart.o(i.UART_RxdIsr) refers to uart.o(.data) for RxdFifoDataRear
    uart.o(i.UART_SendData) refers to uart.o(i.UART_TxdIsr) for UART_TxdIsr
    uart.o(i.UART_SendData) refers to uart.o(.data) for UART_InitFlag
    uart.o(i.UART_SendData) refers to uart.o(.bss) for TxdFifoData
    uart.o(i.UART_SendStr) refers to uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.UART_TxdIsr) refers to uart.o(.data) for TxdFifoDataRear
    uart.o(i.UART_TxdIsr) refers to uart.o(.bss) for TxdFifoData
    xflash.o(i.XFLASH_GetDataFromUart) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    xflash.o(i.XFLASH_GetDataFromUart) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    xflash.o(i.XFLASH_GetDataFromUart) refers to _printf_dec.o(.text) for _printf_int_dec
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_SelectFont) for LCD_SelectFont
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_PutStrLeftTop) for LCD_PutStrLeftTop
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.DelayMs) for DelayMs
    xflash.o(i.XFLASH_GetDataFromUart) refers to noretval__2sprintf.o(.text) for __2sprintf
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_DrawProgress) for LCD_DrawProgress
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.DelayUs) for DelayUs
    xflash.o(i.XFLASH_GetDataFromUart) refers to common.o(i.LrcCalc) for LrcCalc
    xflash.o(i.XFLASH_GetDataFromUart) refers to uart.o(i.UART_SendData) for UART_SendData
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(i.XFLASH_WriteData) for XFLASH_WriteData
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    xflash.o(i.XFLASH_GetDataFromUart) refers to common.o(i.Num2Str) for Num2Str
    xflash.o(i.XFLASH_GetDataFromUart) refers to font.o(.constdata) for FONT_32
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(.data) for UartRxdFlag
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(.bss) for UartRxdDataBuffer1
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(.data) for Uart2RxdIsr
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(i.XFLASH_UartRxdIsr) for XFLASH_UartRxdIsr
    xflash.o(i.XFLASH_GetDataFromUart) refers to uart.o(.data) for UART_BdRate
    xflash.o(i.XFLASH_UartRxdIsr) refers to xflash.o(.data) for UartRxdFlag
    xflash.o(i.XFLASH_UartRxdIsr) refers to xflash.o(.bss) for UartRxdDataBuffer1
    xflash.o(i.XFLASH_WriteData) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    xflash.o(i.XFLASH_WriteData) refers to driver.o(i.SPI_FLASH_EraseSector) for SPI_FLASH_EraseSector
    xflash.o(i.XFLASH_WriteData) refers to driver.o(i.SPI_FLASH_WriteSector) for SPI_FLASH_WriteSector
    xflash.o(i.XFLASH_WriteData) refers to xflash.o(.bss) for XFLASH_TempBuffer
    xfont.o(i.XFONT_GetFontInf) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    xfont.o(i.XFONT_GetFontInf) refers to xfont.o(.bss) for XFONT_Inf
    xfont.o(i.XFONT_GetFontInf) refers to xfont.o(.data) for XFONT_FontNum
    xfont.o(i.XFONT_Init) refers to xfont.o(i.XFONT_GetFontInf) for XFONT_GetFontInf
    xfont.o(i.XFONT_Init) refers to xfont.o(i.XFONT_SeleFont) for XFONT_SeleFont
    xfont.o(i.XFONT_SeleFont) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    xfont.o(i.XFONT_SeleFont) refers to lcd.o(.data) for FontExternalFlag
    xfont.o(i.XFONT_SeleFont) refers to xfont.o(.bss) for XFONT_Inf
    xfont.o(i.XFONT_SeleFont) refers to xfont.o(.data) for FontDataLenAscii
    task.o(i.TASK_Init) refers to apk.o(i.APK_Init) for APK_Init
    task.o(i.TASK_Init) refers to driver.o(i.TASK_TimerInit) for TASK_TimerInit
    task.o(i.TASK_Init) refers to task.o(.data) for TaskTimeCnt
    task.o(i.TASK_Init) refers to task.o(i.TASK_Periodicity) for TASK_Periodicity
    task.o(i.TASK_Init) refers to driver.o(.data) for TaskTimerIsr
    task.o(i.TASK_Periodicity) refers to apk.o(i.APK_Periodicity) for APK_Periodicity
    task.o(i.TASK_Periodicity) refers to task.o(.data) for TaskTimeCnt
    task.o(i.TASK_Run) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    task.o(i.TASK_Run) refers to apk.o(i.APK_Continuous) for APK_Continuous
    llsdiv.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.__pow$lsc) for __pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.__pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.__pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.__pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.__pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.__pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.__pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.__pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.__pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.__pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.__pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_wp.o(.text) for __printf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dsqrt_noumaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_noumaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to stm32f10x_vector.o(.text) for __user_initial_stackheap
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing cortexm3_macro.o(.text), (122 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (96 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (4 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (16 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (16 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (184 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (196 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_BASEPRICONFIG), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_ClearIRQChannelPendingBit), (24 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_ClearSystemHandlerPendingBit), (32 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_DeInit), (68 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GenerateCoreReset), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GenerateSystemReset), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetBASEPRI), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetCPUID), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetCurrentActiveHandler), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetCurrentPendingIRQChannel), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetFaultAddress), (32 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetFaultHandlerSources), (68 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetIRQChannelActiveBitStatus), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetIRQChannelPendingBitStatus), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetSystemHandlerActiveBitStatus), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetSystemHandlerPendingBitStatus), (48 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_RESETFAULTMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_RESETPRIMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SCBDeInit), (84 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SETFAULTMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SETPRIMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SetIRQChannelPendingBit), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SetSystemHandlerPendingBit), (28 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_StructInit), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SystemHandlerConfig), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SystemHandlerPriorityConfig), (112 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (76 bytes).
    Removing stm32f10x_rcc.o(.data), (8 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (210 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (34 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (48 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (92 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (56 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (224 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (110 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (120 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (156 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (108 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (14 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (76 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (160 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (80 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (68 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (42 bytes).
    Removing stm32f10x_flash.o(i.delay), (26 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_it.o(i.EXTI15_10_IRQHandler), (24 bytes).
    Removing stm32f10x_it.o(i.TIM1_UP_IRQHandler), (24 bytes).
    Removing stm32f10x_it.o(i.TIM5_IRQHandler), (24 bytes).
    Removing stm32f10x_it.o(i.USART2_IRQHandler), (24 bytes).
    Removing apk.o(i.APK_Jump), (24 bytes).
    Removing apk.o(i.APK_Jump2Pre), (20 bytes).
    Removing driver.o(i.AdcInit), (536 bytes).
    Removing driver.o(i.DacInit), (104 bytes).
    Removing driver.o(i.DelayHalfUs), (74 bytes).
    Removing driver.o(i.ILI9481ReadRamPrepare), (76 bytes).
    Removing driver.o(i.IrInit), (204 bytes).
    Removing driver.o(i.LCD_WriteArray), (120 bytes).
    Removing driver.o(i.LCD_WriteArrayFromXflash), (164 bytes).
    Removing driver.o(i.OnChipFlashErasePage), (32 bytes).
    Removing driver.o(i.OnChipFlashWritePageData), (66 bytes).
    Removing driver.o(i.PwmInit), (560 bytes).
    Removing driver.o(i.TP_Init), (12 bytes).
    Removing driver.o(i.TP_TouchScan), (12 bytes).
    Removing driver.o(i.WatchDogInit), (54 bytes).
    Removing driver.o(.bss), (20 bytes).
    Removing common.o(i.IntPower), (28 bytes).
    Removing common.o(i.Str2Double), (236 bytes).
    Removing common.o(i.Str2Num), (204 bytes).
    Removing ir.o(i.IR_Decode), (896 bytes).
    Removing ir.o(i.IR_Init), (40 bytes).
    Removing ir.o(.data), (16 bytes).
    Removing lcd.o(i.LCD_DrawBmpFromRawData), (124 bytes).
    Removing lcd.o(i.LCD_DrawBmpFromXflashPackData), (528 bytes).
    Removing lcd.o(i.LCD_DrawChildPic), (40 bytes).
    Removing lcd.o(i.LCD_DrawLine), (508 bytes).
    Removing lcd.o(i.LCD_DrawPage), (26 bytes).
    Removing lcd.o(i.LCD_DrawPageCenter), (24 bytes).
    Removing lcd.o(i.LCD_GetBar), (6 bytes).
    Removing lcd.o(i.LCD_ReadPoint), (6 bytes).
    Removing lcd.o(i.LCD_SetCursor), (20 bytes).
    Removing touch.o(i.TOUCH_DispPos), (488 bytes).
    Removing touch.o(i.TOUCH_DrawLine), (136 bytes).
    Removing touch.o(i.TOUCH_GetIdByPoint), (188 bytes).
    Removing touch.o(i.TOUCH_GetState), (208 bytes).
    Removing touch.o(.bss), (516 bytes).
    Removing touch.o(.data), (8 bytes).
    Removing xfont.o(i.XFONT_GetFontInf), (72 bytes).
    Removing xfont.o(i.XFONT_Init), (14 bytes).
    Removing xfont.o(i.XFONT_SeleFont), (216 bytes).

328 unused section(s) (total 16202 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llsdiv.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_noumaal.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\qiankunTeamLib\api\cmd.c              0x00000000   Number         0  cmd.o ABSOLUTE
    ..\qiankunTeamLib\api\common.c           0x00000000   Number         0  common.o ABSOLUTE
    ..\qiankunTeamLib\api\font.c             0x00000000   Number         0  font.o ABSOLUTE
    ..\qiankunTeamLib\api\ir.c               0x00000000   Number         0  ir.o ABSOLUTE
    ..\qiankunTeamLib\api\lcd.c              0x00000000   Number         0  lcd.o ABSOLUTE
    ..\qiankunTeamLib\api\task.c             0x00000000   Number         0  task.o ABSOLUTE
    ..\qiankunTeamLib\api\touch.c            0x00000000   Number         0  touch.o ABSOLUTE
    ..\qiankunTeamLib\api\uart.c             0x00000000   Number         0  uart.o ABSOLUTE
    ..\qiankunTeamLib\api\xflash.c           0x00000000   Number         0  xflash.o ABSOLUTE
    ..\qiankunTeamLib\api\xfont.c            0x00000000   Number         0  xfont.o ABSOLUTE
    ..\qiankunTeamLib\driver\driver.c        0x00000000   Number         0  driver.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_adc.c  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_dac.c  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_exti.c 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_iwdg.c 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_nvic.c 0x00000000   Number         0  stm32f10x_nvic.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_pwr.c  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_rcc.c  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_spi.c  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_tim.c  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Startup\cortexm3_macro.s                 0x00000000   Number         0  cortexm3_macro.o ABSOLUTE
    Startup\stm32f10x_vector.s               0x00000000   Number         0  stm32f10x_vector.o ABSOLUTE
    User\apk.c                               0x00000000   Number         0  apk.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      304  stm32f10x_vector.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x080001a4   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x080001aa   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001ae   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001b0   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001b2   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x080001b4   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x080001b6   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001b6   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001bc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001bc   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001c0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001c0   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001c8   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001ca   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001ca   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001ce   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001d4   Section       44  stm32f10x_vector.o(.text)
    .text                                    0x08000200   Section       72  llsdiv.o(.text)
    .text                                    0x08000248   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08000270   Section        0  _printf_pad.o(.text)
    .text                                    0x080002c0   Section        0  _printf_dec.o(.text)
    .text                                    0x08000338   Section        0  __printf_flags_wp.o(.text)
    .text                                    0x08000470   Section        0  heapauxi.o(.text)
    .text                                    0x08000476   Section      238  lludivv7m.o(.text)
    .text                                    0x08000564   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000618   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000619   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000648   Section        0  _sputc.o(.text)
    .text                                    0x08000654   Section        8  libspace.o(.text)
    .text                                    0x0800065c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080006a6   Section        0  exit.o(.text)
    .text                                    0x080006b4   Section        0  sys_exit.o(.text)
    .text                                    0x080006c0   Section        2  use_no_semi.o(.text)
    .text                                    0x080006c2   Section        0  indicate_semi.o(.text)
    i.ADC1_2_IRQHandler                      0x080006c4   Section        0  stm32f10x_it.o(i.ADC1_2_IRQHandler)
    i.ADC3_IRQHandler                        0x080006dc   Section        0  stm32f10x_it.o(i.ADC3_IRQHandler)
    i.APK_Common                             0x080006f4   Section        0  apk.o(i.APK_Common)
    i.APK_Continuous                         0x080006fc   Section        0  apk.o(i.APK_Continuous)
    i.APK_Init                               0x0800070c   Section        0  apk.o(i.APK_Init)
    i.APK_Periodicity                        0x0800077c   Section        0  apk.o(i.APK_Periodicity)
    i.Apk_Main                               0x08000780   Section        0  apk.o(i.Apk_Main)
    i.BusFaultException                      0x080009a0   Section        0  stm32f10x_it.o(i.BusFaultException)
    i.CAN_RX1_IRQHandler                     0x080009b8   Section        0  stm32f10x_it.o(i.CAN_RX1_IRQHandler)
    i.CAN_SCE_IRQHandler                     0x080009d0   Section        0  stm32f10x_it.o(i.CAN_SCE_IRQHandler)
    i.CMD_Init                               0x080009e8   Section        0  cmd.o(i.CMD_Init)
    i.CMD_MainTask                           0x08000a1c   Section        0  cmd.o(i.CMD_MainTask)
    i.CMD_PackCheck                          0x08000b5c   Section        0  cmd.o(i.CMD_PackCheck)
    i.CMD_PackRun                            0x08000b90   Section        0  cmd.o(i.CMD_PackRun)
    i.CloclkInit                             0x08000cdc   Section        0  driver.o(i.CloclkInit)
    i.CommonConfig                           0x08000d80   Section        0  driver.o(i.CommonConfig)
    i.DMA1_Channel1_IRQHandler               0x08000d8c   Section        0  stm32f10x_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA1_Channel2_IRQHandler               0x08000da4   Section        0  stm32f10x_it.o(i.DMA1_Channel2_IRQHandler)
    i.DMA1_Channel3_IRQHandler               0x08000dbc   Section        0  stm32f10x_it.o(i.DMA1_Channel3_IRQHandler)
    i.DMA1_Channel4_IRQHandler               0x08000dd4   Section        0  stm32f10x_it.o(i.DMA1_Channel4_IRQHandler)
    i.DMA1_Channel5_IRQHandler               0x08000dec   Section        0  stm32f10x_it.o(i.DMA1_Channel5_IRQHandler)
    i.DMA1_Channel6_IRQHandler               0x08000e04   Section        0  stm32f10x_it.o(i.DMA1_Channel6_IRQHandler)
    i.DMA1_Channel7_IRQHandler               0x08000e1c   Section        0  stm32f10x_it.o(i.DMA1_Channel7_IRQHandler)
    i.DMA2_Channel1_IRQHandler               0x08000e34   Section        0  stm32f10x_it.o(i.DMA2_Channel1_IRQHandler)
    i.DMA2_Channel2_IRQHandler               0x08000e4c   Section        0  stm32f10x_it.o(i.DMA2_Channel2_IRQHandler)
    i.DMA2_Channel3_IRQHandler               0x08000e64   Section        0  stm32f10x_it.o(i.DMA2_Channel3_IRQHandler)
    i.DMA2_Channel4_5_IRQHandler             0x08000e7c   Section        0  stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler)
    i.DebugMonitor                           0x08000e94   Section        0  stm32f10x_it.o(i.DebugMonitor)
    i.DelayInit                              0x08000eac   Section        0  driver.o(i.DelayInit)
    i.DelayMs                                0x08000ef0   Section        0  driver.o(i.DelayMs)
    i.DelayUs                                0x08000f34   Section        0  driver.o(i.DelayUs)
    i.EXTI0_IRQHandler                       0x08000f7c   Section        0  stm32f10x_it.o(i.EXTI0_IRQHandler)
    i.EXTI15_10_IRQHandler                   0x08000f94   Section        0  driver.o(i.EXTI15_10_IRQHandler)
    i.EXTI1_IRQHandler                       0x08000fd4   Section        0  stm32f10x_it.o(i.EXTI1_IRQHandler)
    i.EXTI2_IRQHandler                       0x08000fec   Section        0  stm32f10x_it.o(i.EXTI2_IRQHandler)
    i.EXTI3_IRQHandler                       0x08001004   Section        0  stm32f10x_it.o(i.EXTI3_IRQHandler)
    i.EXTI4_IRQHandler                       0x0800101c   Section        0  stm32f10x_it.o(i.EXTI4_IRQHandler)
    i.EXTI9_5_IRQHandler                     0x08001034   Section        0  stm32f10x_it.o(i.EXTI9_5_IRQHandler)
    i.EXTI_GetITStatus                       0x0800104c   Section        0  stm32f10x_exti.o(i.EXTI_GetITStatus)
    i.FLASH_IRQHandler                       0x08001074   Section        0  stm32f10x_it.o(i.FLASH_IRQHandler)
    i.FLASH_PrefetchBufferCmd                0x0800108c   Section        0  stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd)
    i.FLASH_SetLatency                       0x080010a8   Section        0  stm32f10x_flash.o(i.FLASH_SetLatency)
    i.FSMC_IRQHandler                        0x080010c4   Section        0  stm32f10x_it.o(i.FSMC_IRQHandler)
    i.GPIO_Init                              0x080010dc   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x080011f0   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.HardFaultException                     0x08001260   Section        0  stm32f10x_it.o(i.HardFaultException)
    i.I2C1_ER_IRQHandler                     0x08001278   Section        0  stm32f10x_it.o(i.I2C1_ER_IRQHandler)
    i.I2C1_EV_IRQHandler                     0x08001290   Section        0  stm32f10x_it.o(i.I2C1_EV_IRQHandler)
    i.I2C2_ER_IRQHandler                     0x080012a8   Section        0  stm32f10x_it.o(i.I2C2_ER_IRQHandler)
    i.I2C2_EV_IRQHandler                     0x080012c0   Section        0  stm32f10x_it.o(i.I2C2_EV_IRQHandler)
    i.ILI9481Init                            0x080012d8   Section        0  driver.o(i.ILI9481Init)
    i.ILI9481SetDisplayWindow                0x08001804   Section        0  driver.o(i.ILI9481SetDisplayWindow)
    i.ILI9481WriteRamPrepare                 0x08001928   Section        0  driver.o(i.ILI9481WriteRamPrepare)
    i.LCD_BackLightInit                      0x08001974   Section        0  driver.o(i.LCD_BackLightInit)
    i.LCD_Clear                              0x08001a48   Section        0  lcd.o(i.LCD_Clear)
    i.LCD_DrawPoint                          0x08001a70   Section        0  lcd.o(i.LCD_DrawPoint)
    i.LCD_DrawProgress                       0x08001ad4   Section        0  lcd.o(i.LCD_DrawProgress)
    i.LCD_Init                               0x08001c24   Section        0  lcd.o(i.LCD_Init)
    i.LCD_InitGpio                           0x08001c58   Section        0  driver.o(i.LCD_InitGpio)
    i.LCD_PutChar                            0x08001d50   Section        0  lcd.o(i.LCD_PutChar)
    i.LCD_PutHanzi                           0x08001e1c   Section        0  lcd.o(i.LCD_PutHanzi)
    i.LCD_PutStr                             0x08001ee8   Section        0  lcd.o(i.LCD_PutStr)
    i.LCD_PutStrCenter                       0x08002018   Section        0  lcd.o(i.LCD_PutStrCenter)
    i.LCD_PutStrLeftTop                      0x08002084   Section        0  lcd.o(i.LCD_PutStrLeftTop)
    i.LCD_PutStrRightCenter                  0x080020bc   Section        0  lcd.o(i.LCD_PutStrRightCenter)
    i.LCD_SearchFont                         0x08002128   Section        0  lcd.o(i.LCD_SearchFont)
    i.LCD_SelectFont                         0x080022fc   Section        0  lcd.o(i.LCD_SelectFont)
    i.LCD_SetBar                             0x08002390   Section        0  lcd.o(i.LCD_SetBar)
    i.LCD_SetBright                          0x080023e4   Section        0  driver.o(i.LCD_SetBright)
    i.LCD_WriteConst                         0x08002400   Section        0  driver.o(i.LCD_WriteConst)
    i.LedInit                                0x08002458   Section        0  driver.o(i.LedInit)
    i.LrcCalc                                0x08002490   Section        0  common.o(i.LrcCalc)
    i.MemManageException                     0x080024ac   Section        0  stm32f10x_it.o(i.MemManageException)
    i.NMIException                           0x080024c4   Section        0  stm32f10x_it.o(i.NMIException)
    i.NVIC_Init                              0x080024dc   Section        0  stm32f10x_nvic.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08002580   Section        0  stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig)
    i.Num2Str                                0x08002594   Section        0  common.o(i.Num2Str)
    i.PVD_IRQHandler                         0x080026f4   Section        0  stm32f10x_it.o(i.PVD_IRQHandler)
    i.PendSVC                                0x0800270c   Section        0  stm32f10x_it.o(i.PendSVC)
    i.RCC_APB1PeriphClockCmd                 0x08002724   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08002744   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_DeInit                             0x08002764   Section        0  stm32f10x_rcc.o(i.RCC_DeInit)
    i.RCC_GetClocksFreq                      0x080027b0   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.RCC_GetFlagStatus                      0x08002884   Section        0  stm32f10x_rcc.o(i.RCC_GetFlagStatus)
    i.RCC_GetSYSCLKSource                    0x080028c0   Section        0  stm32f10x_rcc.o(i.RCC_GetSYSCLKSource)
    i.RCC_HCLKConfig                         0x080028d0   Section        0  stm32f10x_rcc.o(i.RCC_HCLKConfig)
    i.RCC_HSEConfig                          0x080028e8   Section        0  stm32f10x_rcc.o(i.RCC_HSEConfig)
    i.RCC_IRQHandler                         0x08002934   Section        0  stm32f10x_it.o(i.RCC_IRQHandler)
    i.RCC_PCLK1Config                        0x0800294c   Section        0  stm32f10x_rcc.o(i.RCC_PCLK1Config)
    i.RCC_PCLK2Config                        0x08002964   Section        0  stm32f10x_rcc.o(i.RCC_PCLK2Config)
    i.RCC_PLLCmd                             0x0800297c   Section        0  stm32f10x_rcc.o(i.RCC_PLLCmd)
    i.RCC_PLLConfig                          0x08002988   Section        0  stm32f10x_rcc.o(i.RCC_PLLConfig)
    i.RCC_SYSCLKConfig                       0x080029a4   Section        0  stm32f10x_rcc.o(i.RCC_SYSCLKConfig)
    i.RTCAlarm_IRQHandler                    0x080029bc   Section        0  stm32f10x_it.o(i.RTCAlarm_IRQHandler)
    i.RTC_IRQHandler                         0x080029d4   Section        0  stm32f10x_it.o(i.RTC_IRQHandler)
    i.SDIO_IRQHandler                        0x080029ec   Section        0  stm32f10x_it.o(i.SDIO_IRQHandler)
    i.SPI1_IRQHandler                        0x08002a04   Section        0  stm32f10x_it.o(i.SPI1_IRQHandler)
    i.SPI2_IRQHandler                        0x08002a1c   Section        0  stm32f10x_it.o(i.SPI2_IRQHandler)
    i.SPI3_IRQHandler                        0x08002a34   Section        0  stm32f10x_it.o(i.SPI3_IRQHandler)
    i.SPI_Cmd                                0x08002a4c   Section        0  stm32f10x_spi.o(i.SPI_Cmd)
    i.SPI_FLASH_EraseSector                  0x08002a64   Section        0  driver.o(i.SPI_FLASH_EraseSector)
    i.SPI_FLASH_Init                         0x08002b1c   Section        0  driver.o(i.SPI_FLASH_Init)
    i.SPI_FLASH_ReadData                     0x08002bc4   Section        0  driver.o(i.SPI_FLASH_ReadData)
    i.SPI_FLASH_ReadId                       0x08002ca4   Section        0  driver.o(i.SPI_FLASH_ReadId)
    i.SPI_FLASH_WaitBusy                     0x08002e48   Section        0  driver.o(i.SPI_FLASH_WaitBusy)
    i.SPI_FLASH_WriteSector                  0x08002ea4   Section        0  driver.o(i.SPI_FLASH_WriteSector)
    i.SPI_Init                               0x08003010   Section        0  stm32f10x_spi.o(i.SPI_Init)
    i.SVCHandler                             0x0800304c   Section        0  stm32f10x_it.o(i.SVCHandler)
    i.Spi3Init                               0x08003064   Section        0  driver.o(i.Spi3Init)
    i.StrLen                                 0x08003148   Section        0  common.o(i.StrLen)
    i.SysTickHandler                         0x08003164   Section        0  stm32f10x_it.o(i.SysTickHandler)
    i.SystemInit                             0x0800317c   Section        0  main.o(i.SystemInit)
    i.TAMPER_IRQHandler                      0x080031a4   Section        0  stm32f10x_it.o(i.TAMPER_IRQHandler)
    i.TASK_Init                              0x080031bc   Section        0  task.o(i.TASK_Init)
    i.TASK_Periodicity                       0x080031ec   Section        0  task.o(i.TASK_Periodicity)
    i.TASK_Run                               0x0800320c   Section        0  task.o(i.TASK_Run)
    i.TASK_TimerInit                         0x08003220   Section        0  driver.o(i.TASK_TimerInit)
    i.TIM1_BRK_IRQHandler                    0x080032b0   Section        0  stm32f10x_it.o(i.TIM1_BRK_IRQHandler)
    i.TIM1_CC_IRQHandler                     0x080032c8   Section        0  stm32f10x_it.o(i.TIM1_CC_IRQHandler)
    i.TIM1_TRG_COM_IRQHandler                0x080032e0   Section        0  stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler)
    i.TIM1_UP_IRQHandler                     0x080032f8   Section        0  driver.o(i.TIM1_UP_IRQHandler)
    i.TIM2_IRQHandler                        0x0800331c   Section        0  stm32f10x_it.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x08003334   Section        0  stm32f10x_it.o(i.TIM3_IRQHandler)
    i.TIM4_IRQHandler                        0x0800334c   Section        0  stm32f10x_it.o(i.TIM4_IRQHandler)
    i.TIM5_IRQHandler                        0x08003364   Section        0  driver.o(i.TIM5_IRQHandler)
    i.TIM6_IRQHandler                        0x08003398   Section        0  stm32f10x_it.o(i.TIM6_IRQHandler)
    i.TIM7_IRQHandler                        0x080033b0   Section        0  stm32f10x_it.o(i.TIM7_IRQHandler)
    i.TIM8_BRK_IRQHandler                    0x080033c8   Section        0  stm32f10x_it.o(i.TIM8_BRK_IRQHandler)
    i.TIM8_CC_IRQHandler                     0x080033e0   Section        0  stm32f10x_it.o(i.TIM8_CC_IRQHandler)
    i.TIM8_TRG_COM_IRQHandler                0x080033f8   Section        0  stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler)
    i.TIM8_UP_IRQHandler                     0x08003410   Section        0  stm32f10x_it.o(i.TIM8_UP_IRQHandler)
    i.TIM_ARRPreloadConfig                   0x08003428   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_ClearFlag                          0x08003440   Section        0  stm32f10x_tim.o(i.TIM_ClearFlag)
    i.TIM_Cmd                                0x08003446   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x0800345e   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_ITConfig                           0x0800347c   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_OC4Init                            0x08003490   Section        0  stm32f10x_tim.o(i.TIM_OC4Init)
    i.TIM_OC4PreloadConfig                   0x08003504   Section        0  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    i.TIM_TimeBaseInit                       0x08003520   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.UART4_IRQHandler                       0x0800355c   Section        0  stm32f10x_it.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x08003574   Section        0  stm32f10x_it.o(i.UART5_IRQHandler)
    i.UART_ClearRxdBuffer                    0x0800358c   Section        0  uart.o(i.UART_ClearRxdBuffer)
    i.UART_GetRxdData                        0x080035a8   Section        0  uart.o(i.UART_GetRxdData)
    i.UART_GetRxdFifoLen                     0x080035fc   Section        0  uart.o(i.UART_GetRxdFifoLen)
    i.UART_Init                              0x08003630   Section        0  uart.o(i.UART_Init)
    i.UART_RxdIsr                            0x080036a0   Section        0  uart.o(i.UART_RxdIsr)
    i.UART_SendData                          0x080036e0   Section        0  uart.o(i.UART_SendData)
    i.UART_SendStr                           0x08003768   Section        0  uart.o(i.UART_SendStr)
    i.UART_TxdIsr                            0x0800378c   Section        0  uart.o(i.UART_TxdIsr)
    i.USART1_IRQHandler                      0x080037dc   Section        0  stm32f10x_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080037f4   Section        0  driver.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08003864   Section        0  stm32f10x_it.o(i.USART3_IRQHandler)
    i.USART_Cmd                              0x0800387c   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_ITConfig                         0x08003894   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x080038d4   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USBWakeUp_IRQHandler                   0x08003970   Section        0  stm32f10x_it.o(i.USBWakeUp_IRQHandler)
    i.USB_HP_CAN_TX_IRQHandler               0x08003988   Section        0  stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler)
    i.USB_LP_CAN_RX0_IRQHandler              0x080039a0   Section        0  stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler)
    i.Uart2Init                              0x080039b8   Section        0  driver.o(i.Uart2Init)
    i.UsageFaultException                    0x08003a78   Section        0  stm32f10x_it.o(i.UsageFaultException)
    i.WWDG_IRQHandler                        0x08003a90   Section        0  stm32f10x_it.o(i.WWDG_IRQHandler)
    i.XFLASH_GetDataFromUart                 0x08003aa8   Section        0  xflash.o(i.XFLASH_GetDataFromUart)
    i.XFLASH_UartRxdIsr                      0x080040b4   Section        0  xflash.o(i.XFLASH_UartRxdIsr)
    i.XFLASH_WriteData                       0x080041bc   Section        0  xflash.o(i.XFLASH_WriteData)
    i._is_digit                              0x0800431c   Section        0  __printf_wp.o(i._is_digit)
    i.main                                   0x0800432a   Section        0  main.o(i.main)
    .constdata                               0x08004340   Section       20  stm32f10x_rcc.o(.constdata)
    APBAHBPrescTable                         0x08004340   Data          16  stm32f10x_rcc.o(.constdata)
    ADCPrescTable                            0x08004350   Data           4  stm32f10x_rcc.o(.constdata)
    .constdata                               0x08004354   Section    10776  font.o(.constdata)
    .constdata                               0x08006d6c   Section       17  __printf_flags_wp.o(.constdata)
    maptable                                 0x08006d6c   Data          17  __printf_flags_wp.o(.constdata)
    .data                                    0x20000000   Section        4  main.o(.data)
    .data                                    0x20000004   Section        8  apk.o(.data)
    .data                                    0x2000000c   Section       34  driver.o(.data)
    fac_us                                   0x2000000c   Data           1  driver.o(.data)
    fac_ms                                   0x20000010   Data           4  driver.o(.data)
    .data                                    0x20000030   Section       10  cmd.o(.data)
    timeout_cnt                              0x20000030   Data           4  cmd.o(.data)
    PackDataNum                              0x20000034   Data           2  cmd.o(.data)
    PackDataLen                              0x20000036   Data           2  cmd.o(.data)
    PackTimeout                              0x20000038   Data           2  cmd.o(.data)
    .data                                    0x2000003c   Section       20  lcd.o(.data)
    PageOffsetX                              0x2000004a   Data           2  lcd.o(.data)
    PageOffsetY                              0x2000004c   Data           2  lcd.o(.data)
    PageId                                   0x2000004e   Data           2  lcd.o(.data)
    .data                                    0x20000050   Section       18  uart.o(.data)
    UART_InitFlag                            0x20000050   Data           1  uart.o(.data)
    TxdFifoDataFront                         0x20000058   Data           2  uart.o(.data)
    TxdFifoDataRear                          0x2000005a   Data           2  uart.o(.data)
    UART_TxdEndFlag                          0x2000005c   Data           1  uart.o(.data)
    RxdFifoDataFront                         0x2000005e   Data           2  uart.o(.data)
    RxdFifoDataRear                          0x20000060   Data           2  uart.o(.data)
    .data                                    0x20000062   Section        9  xflash.o(.data)
    UartRxdDataBufferIndex1                  0x20000062   Data           2  xflash.o(.data)
    UartRxdDataBufferIndex2                  0x20000064   Data           2  xflash.o(.data)
    UartRxdCheckBufferIndex                  0x20000066   Data           2  xflash.o(.data)
    UartRxdBufferSele                        0x20000068   Data           2  xflash.o(.data)
    UartRxdFlag                              0x2000006a   Data           1  xflash.o(.data)
    .data                                    0x2000006c   Section       16  xfont.o(.data)
    .data                                    0x20000080   Section       16  task.o(.data)
    .bss                                     0x20000090   Section       54  cmd.o(.bss)
    .bss                                     0x200000c6   Section     3020  uart.o(.bss)
    TxdFifoData                              0x200000c6   Data        1010  uart.o(.bss)
    RxdFifoData                              0x200004b8   Data        2010  uart.o(.bss)
    .bss                                     0x20000c92   Section    24840  xflash.o(.bss)
    UartRxdDataBuffer1                       0x20004c92   Data        4100  xflash.o(.bss)
    UartRxdDataBuffer2                       0x20005c96   Data        4100  xflash.o(.bss)
    UartRxdCheckBuffer                       0x20006c9a   Data         256  xflash.o(.bss)
    .bss                                     0x20006d9c   Section      580  xfont.o(.bss)
    .bss                                     0x20006fe0   Section       96  libspace.o(.bss)
    HEAP                                     0x20007040   Section     4096  stm32f10x_vector.o(HEAP)
    Heap_Mem                                 0x20007040   Data        4096  stm32f10x_vector.o(HEAP)
    STACK                                    0x20008040   Section     4096  stm32f10x_vector.o(STACK)
    Stack_Mem                                0x20008040   Data        4096  stm32f10x_vector.o(STACK)
    __initial_sp                             0x20009040   Data           0  stm32f10x_vector.o(STACK)
    .ARM.__AT_0x2000FFFC                     0x2000fffc   Section        4  main.o(.ARM.__AT_0x2000FFFC)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors                                0x08000000   Data           4  stm32f10x_vector.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x080001a5   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x080001ab   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001af   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001b3   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x080001b7   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001b7   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001bd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001bd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001c1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001c1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001c9   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001cb   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001cb   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001cf   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001d5   Thumb Code     8  stm32f10x_vector.o(.text)
    __user_initial_stackheap                 0x080001dd   Thumb Code     0  stm32f10x_vector.o(.text)
    __aeabi_ldivmod                          0x08000201   Thumb Code     0  llsdiv.o(.text)
    _ll_sdiv                                 0x08000201   Thumb Code    72  llsdiv.o(.text)
    __2sprintf                               0x08000249   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x08000271   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x0800029d   Thumb Code    34  _printf_pad.o(.text)
    _printf_int_dec                          0x080002c1   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x08000339   Thumb Code   308  __printf_flags_wp.o(.text)
    __use_two_region_memory                  0x08000471   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000473   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000475   Thumb Code     2  heapauxi.o(.text)
    __aeabi_uldivmod                         0x08000477   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000477   Thumb Code   238  lludivv7m.o(.text)
    _printf_int_common                       0x08000565   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x08000623   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000649   Thumb Code    10  _sputc.o(.text)
    __user_libspace                          0x08000655   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000655   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000655   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800065d   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080006a7   Thumb Code    12  exit.o(.text)
    _sys_exit                                0x080006b5   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080006c1   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080006c1   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080006c3   Thumb Code     0  indicate_semi.o(.text)
    ADC1_2_IRQHandler                        0x080006c5   Thumb Code    16  stm32f10x_it.o(i.ADC1_2_IRQHandler)
    ADC3_IRQHandler                          0x080006dd   Thumb Code    16  stm32f10x_it.o(i.ADC3_IRQHandler)
    APK_Common                               0x080006f5   Thumb Code     8  apk.o(i.APK_Common)
    APK_Continuous                           0x080006fd   Thumb Code    10  apk.o(i.APK_Continuous)
    APK_Init                                 0x0800070d   Thumb Code    66  apk.o(i.APK_Init)
    APK_Periodicity                          0x0800077d   Thumb Code     2  apk.o(i.APK_Periodicity)
    Apk_Main                                 0x08000781   Thumb Code   422  apk.o(i.Apk_Main)
    BusFaultException                        0x080009a1   Thumb Code    16  stm32f10x_it.o(i.BusFaultException)
    CAN_RX1_IRQHandler                       0x080009b9   Thumb Code    16  stm32f10x_it.o(i.CAN_RX1_IRQHandler)
    CAN_SCE_IRQHandler                       0x080009d1   Thumb Code    16  stm32f10x_it.o(i.CAN_SCE_IRQHandler)
    CMD_Init                                 0x080009e9   Thumb Code    32  cmd.o(i.CMD_Init)
    CMD_MainTask                             0x08000a1d   Thumb Code   302  cmd.o(i.CMD_MainTask)
    CMD_PackCheck                            0x08000b5d   Thumb Code    52  cmd.o(i.CMD_PackCheck)
    CMD_PackRun                              0x08000b91   Thumb Code   196  cmd.o(i.CMD_PackRun)
    CloclkInit                               0x08000cdd   Thumb Code   150  driver.o(i.CloclkInit)
    CommonConfig                             0x08000d81   Thumb Code    12  driver.o(i.CommonConfig)
    DMA1_Channel1_IRQHandler                 0x08000d8d   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_IRQHandler                 0x08000da5   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel2_IRQHandler)
    DMA1_Channel3_IRQHandler                 0x08000dbd   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel3_IRQHandler)
    DMA1_Channel4_IRQHandler                 0x08000dd5   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel4_IRQHandler)
    DMA1_Channel5_IRQHandler                 0x08000ded   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel5_IRQHandler)
    DMA1_Channel6_IRQHandler                 0x08000e05   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel6_IRQHandler)
    DMA1_Channel7_IRQHandler                 0x08000e1d   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel7_IRQHandler)
    DMA2_Channel1_IRQHandler                 0x08000e35   Thumb Code    16  stm32f10x_it.o(i.DMA2_Channel1_IRQHandler)
    DMA2_Channel2_IRQHandler                 0x08000e4d   Thumb Code    16  stm32f10x_it.o(i.DMA2_Channel2_IRQHandler)
    DMA2_Channel3_IRQHandler                 0x08000e65   Thumb Code    16  stm32f10x_it.o(i.DMA2_Channel3_IRQHandler)
    DMA2_Channel4_5_IRQHandler               0x08000e7d   Thumb Code    16  stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler)
    DebugMonitor                             0x08000e95   Thumb Code    16  stm32f10x_it.o(i.DebugMonitor)
    DelayInit                                0x08000ead   Thumb Code    52  driver.o(i.DelayInit)
    DelayMs                                  0x08000ef1   Thumb Code    62  driver.o(i.DelayMs)
    DelayUs                                  0x08000f35   Thumb Code    68  driver.o(i.DelayUs)
    EXTI0_IRQHandler                         0x08000f7d   Thumb Code    16  stm32f10x_it.o(i.EXTI0_IRQHandler)
    EXTI15_10_IRQHandler                     0x08000f95   Thumb Code    48  driver.o(i.EXTI15_10_IRQHandler)
    EXTI1_IRQHandler                         0x08000fd5   Thumb Code    16  stm32f10x_it.o(i.EXTI1_IRQHandler)
    EXTI2_IRQHandler                         0x08000fed   Thumb Code    16  stm32f10x_it.o(i.EXTI2_IRQHandler)
    EXTI3_IRQHandler                         0x08001005   Thumb Code    16  stm32f10x_it.o(i.EXTI3_IRQHandler)
    EXTI4_IRQHandler                         0x0800101d   Thumb Code    16  stm32f10x_it.o(i.EXTI4_IRQHandler)
    EXTI9_5_IRQHandler                       0x08001035   Thumb Code    16  stm32f10x_it.o(i.EXTI9_5_IRQHandler)
    EXTI_GetITStatus                         0x0800104d   Thumb Code    34  stm32f10x_exti.o(i.EXTI_GetITStatus)
    FLASH_IRQHandler                         0x08001075   Thumb Code    16  stm32f10x_it.o(i.FLASH_IRQHandler)
    FLASH_PrefetchBufferCmd                  0x0800108d   Thumb Code    22  stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd)
    FLASH_SetLatency                         0x080010a9   Thumb Code    22  stm32f10x_flash.o(i.FLASH_SetLatency)
    FSMC_IRQHandler                          0x080010c5   Thumb Code    16  stm32f10x_it.o(i.FSMC_IRQHandler)
    GPIO_Init                                0x080010dd   Thumb Code   276  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x080011f1   Thumb Code   106  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    HardFaultException                       0x08001261   Thumb Code    16  stm32f10x_it.o(i.HardFaultException)
    I2C1_ER_IRQHandler                       0x08001279   Thumb Code    16  stm32f10x_it.o(i.I2C1_ER_IRQHandler)
    I2C1_EV_IRQHandler                       0x08001291   Thumb Code    16  stm32f10x_it.o(i.I2C1_EV_IRQHandler)
    I2C2_ER_IRQHandler                       0x080012a9   Thumb Code    16  stm32f10x_it.o(i.I2C2_ER_IRQHandler)
    I2C2_EV_IRQHandler                       0x080012c1   Thumb Code    16  stm32f10x_it.o(i.I2C2_EV_IRQHandler)
    ILI9481Init                              0x080012d9   Thumb Code  1308  driver.o(i.ILI9481Init)
    ILI9481SetDisplayWindow                  0x08001805   Thumb Code   276  driver.o(i.ILI9481SetDisplayWindow)
    ILI9481WriteRamPrepare                   0x08001929   Thumb Code    60  driver.o(i.ILI9481WriteRamPrepare)
    LCD_BackLightInit                        0x08001975   Thumb Code   198  driver.o(i.LCD_BackLightInit)
    LCD_Clear                                0x08001a49   Thumb Code    38  lcd.o(i.LCD_Clear)
    LCD_DrawPoint                            0x08001a71   Thumb Code    84  lcd.o(i.LCD_DrawPoint)
    LCD_DrawProgress                         0x08001ad5   Thumb Code   330  lcd.o(i.LCD_DrawProgress)
    LCD_Init                                 0x08001c25   Thumb Code    36  lcd.o(i.LCD_Init)
    LCD_InitGpio                             0x08001c59   Thumb Code   226  driver.o(i.LCD_InitGpio)
    LCD_PutChar                              0x08001d51   Thumb Code   194  lcd.o(i.LCD_PutChar)
    LCD_PutHanzi                             0x08001e1d   Thumb Code   194  lcd.o(i.LCD_PutHanzi)
    LCD_PutStr                               0x08001ee9   Thumb Code   294  lcd.o(i.LCD_PutStr)
    LCD_PutStrCenter                         0x08002019   Thumb Code   100  lcd.o(i.LCD_PutStrCenter)
    LCD_PutStrLeftTop                        0x08002085   Thumb Code    54  lcd.o(i.LCD_PutStrLeftTop)
    LCD_PutStrRightCenter                    0x080020bd   Thumb Code    98  lcd.o(i.LCD_PutStrRightCenter)
    LCD_SearchFont                           0x08002129   Thumb Code   432  lcd.o(i.LCD_SearchFont)
    LCD_SelectFont                           0x080022fd   Thumb Code   120  lcd.o(i.LCD_SelectFont)
    LCD_SetBar                               0x08002391   Thumb Code    82  lcd.o(i.LCD_SetBar)
    LCD_SetBright                            0x080023e5   Thumb Code    22  driver.o(i.LCD_SetBright)
    LCD_WriteConst                           0x08002401   Thumb Code    70  driver.o(i.LCD_WriteConst)
    LedInit                                  0x08002459   Thumb Code    52  driver.o(i.LedInit)
    LrcCalc                                  0x08002491   Thumb Code    28  common.o(i.LrcCalc)
    MemManageException                       0x080024ad   Thumb Code    16  stm32f10x_it.o(i.MemManageException)
    NMIException                             0x080024c5   Thumb Code    16  stm32f10x_it.o(i.NMIException)
    NVIC_Init                                0x080024dd   Thumb Code   150  stm32f10x_nvic.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08002581   Thumb Code    10  stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig)
    Num2Str                                  0x08002595   Thumb Code   350  common.o(i.Num2Str)
    PVD_IRQHandler                           0x080026f5   Thumb Code    16  stm32f10x_it.o(i.PVD_IRQHandler)
    PendSVC                                  0x0800270d   Thumb Code    16  stm32f10x_it.o(i.PendSVC)
    RCC_APB1PeriphClockCmd                   0x08002725   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08002745   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_DeInit                               0x08002765   Thumb Code    62  stm32f10x_rcc.o(i.RCC_DeInit)
    RCC_GetClocksFreq                        0x080027b1   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    RCC_GetFlagStatus                        0x08002885   Thumb Code    56  stm32f10x_rcc.o(i.RCC_GetFlagStatus)
    RCC_GetSYSCLKSource                      0x080028c1   Thumb Code    10  stm32f10x_rcc.o(i.RCC_GetSYSCLKSource)
    RCC_HCLKConfig                           0x080028d1   Thumb Code    18  stm32f10x_rcc.o(i.RCC_HCLKConfig)
    RCC_HSEConfig                            0x080028e9   Thumb Code    70  stm32f10x_rcc.o(i.RCC_HSEConfig)
    RCC_IRQHandler                           0x08002935   Thumb Code    16  stm32f10x_it.o(i.RCC_IRQHandler)
    RCC_PCLK1Config                          0x0800294d   Thumb Code    18  stm32f10x_rcc.o(i.RCC_PCLK1Config)
    RCC_PCLK2Config                          0x08002965   Thumb Code    20  stm32f10x_rcc.o(i.RCC_PCLK2Config)
    RCC_PLLCmd                               0x0800297d   Thumb Code     6  stm32f10x_rcc.o(i.RCC_PLLCmd)
    RCC_PLLConfig                            0x08002989   Thumb Code    24  stm32f10x_rcc.o(i.RCC_PLLConfig)
    RCC_SYSCLKConfig                         0x080029a5   Thumb Code    18  stm32f10x_rcc.o(i.RCC_SYSCLKConfig)
    RTCAlarm_IRQHandler                      0x080029bd   Thumb Code    16  stm32f10x_it.o(i.RTCAlarm_IRQHandler)
    RTC_IRQHandler                           0x080029d5   Thumb Code    16  stm32f10x_it.o(i.RTC_IRQHandler)
    SDIO_IRQHandler                          0x080029ed   Thumb Code    16  stm32f10x_it.o(i.SDIO_IRQHandler)
    SPI1_IRQHandler                          0x08002a05   Thumb Code    16  stm32f10x_it.o(i.SPI1_IRQHandler)
    SPI2_IRQHandler                          0x08002a1d   Thumb Code    16  stm32f10x_it.o(i.SPI2_IRQHandler)
    SPI3_IRQHandler                          0x08002a35   Thumb Code    16  stm32f10x_it.o(i.SPI3_IRQHandler)
    SPI_Cmd                                  0x08002a4d   Thumb Code    24  stm32f10x_spi.o(i.SPI_Cmd)
    SPI_FLASH_EraseSector                    0x08002a65   Thumb Code   176  driver.o(i.SPI_FLASH_EraseSector)
    SPI_FLASH_Init                           0x08002b1d   Thumb Code   160  driver.o(i.SPI_FLASH_Init)
    SPI_FLASH_ReadData                       0x08002bc5   Thumb Code   216  driver.o(i.SPI_FLASH_ReadData)
    SPI_FLASH_ReadId                         0x08002ca5   Thumb Code   406  driver.o(i.SPI_FLASH_ReadId)
    SPI_FLASH_WaitBusy                       0x08002e49   Thumb Code    84  driver.o(i.SPI_FLASH_WaitBusy)
    SPI_FLASH_WriteSector                    0x08002ea5   Thumb Code   354  driver.o(i.SPI_FLASH_WriteSector)
    SPI_Init                                 0x08003011   Thumb Code    60  stm32f10x_spi.o(i.SPI_Init)
    SVCHandler                               0x0800304d   Thumb Code    16  stm32f10x_it.o(i.SVCHandler)
    Spi3Init                                 0x08003065   Thumb Code   216  driver.o(i.Spi3Init)
    StrLen                                   0x08003149   Thumb Code    26  common.o(i.StrLen)
    SysTickHandler                           0x08003165   Thumb Code    16  stm32f10x_it.o(i.SysTickHandler)
    SystemInit                               0x0800317d   Thumb Code    28  main.o(i.SystemInit)
    TAMPER_IRQHandler                        0x080031a5   Thumb Code    16  stm32f10x_it.o(i.TAMPER_IRQHandler)
    TASK_Init                                0x080031bd   Thumb Code    32  task.o(i.TASK_Init)
    TASK_Periodicity                         0x080031ed   Thumb Code    26  task.o(i.TASK_Periodicity)
    TASK_Run                                 0x0800320d   Thumb Code    16  task.o(i.TASK_Run)
    TASK_TimerInit                           0x08003221   Thumb Code   130  driver.o(i.TASK_TimerInit)
    TIM1_BRK_IRQHandler                      0x080032b1   Thumb Code    16  stm32f10x_it.o(i.TIM1_BRK_IRQHandler)
    TIM1_CC_IRQHandler                       0x080032c9   Thumb Code    16  stm32f10x_it.o(i.TIM1_CC_IRQHandler)
    TIM1_TRG_COM_IRQHandler                  0x080032e1   Thumb Code    16  stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler)
    TIM1_UP_IRQHandler                       0x080032f9   Thumb Code    24  driver.o(i.TIM1_UP_IRQHandler)
    TIM2_IRQHandler                          0x0800331d   Thumb Code    16  stm32f10x_it.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x08003335   Thumb Code    16  stm32f10x_it.o(i.TIM3_IRQHandler)
    TIM4_IRQHandler                          0x0800334d   Thumb Code    16  stm32f10x_it.o(i.TIM4_IRQHandler)
    TIM5_IRQHandler                          0x08003365   Thumb Code    36  driver.o(i.TIM5_IRQHandler)
    TIM6_IRQHandler                          0x08003399   Thumb Code    16  stm32f10x_it.o(i.TIM6_IRQHandler)
    TIM7_IRQHandler                          0x080033b1   Thumb Code    16  stm32f10x_it.o(i.TIM7_IRQHandler)
    TIM8_BRK_IRQHandler                      0x080033c9   Thumb Code    16  stm32f10x_it.o(i.TIM8_BRK_IRQHandler)
    TIM8_CC_IRQHandler                       0x080033e1   Thumb Code    16  stm32f10x_it.o(i.TIM8_CC_IRQHandler)
    TIM8_TRG_COM_IRQHandler                  0x080033f9   Thumb Code    16  stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler)
    TIM8_UP_IRQHandler                       0x08003411   Thumb Code    16  stm32f10x_it.o(i.TIM8_UP_IRQHandler)
    TIM_ARRPreloadConfig                     0x08003429   Thumb Code    24  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_ClearFlag                            0x08003441   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearFlag)
    TIM_Cmd                                  0x08003447   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x0800345f   Thumb Code    30  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_ITConfig                             0x0800347d   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_OC4Init                              0x08003491   Thumb Code   106  stm32f10x_tim.o(i.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x08003505   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    TIM_TimeBaseInit                         0x08003521   Thumb Code    50  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    UART4_IRQHandler                         0x0800355d   Thumb Code    16  stm32f10x_it.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x08003575   Thumb Code    16  stm32f10x_it.o(i.UART5_IRQHandler)
    UART_ClearRxdBuffer                      0x0800358d   Thumb Code    16  uart.o(i.UART_ClearRxdBuffer)
    UART_GetRxdData                          0x080035a9   Thumb Code    70  uart.o(i.UART_GetRxdData)
    UART_GetRxdFifoLen                       0x080035fd   Thumb Code    44  uart.o(i.UART_GetRxdFifoLen)
    UART_Init                                0x08003631   Thumb Code    66  uart.o(i.UART_Init)
    UART_RxdIsr                              0x080036a1   Thumb Code    50  uart.o(i.UART_RxdIsr)
    UART_SendData                            0x080036e1   Thumb Code   114  uart.o(i.UART_SendData)
    UART_SendStr                             0x08003769   Thumb Code    36  uart.o(i.UART_SendStr)
    UART_TxdIsr                              0x0800378d   Thumb Code    58  uart.o(i.UART_TxdIsr)
    USART1_IRQHandler                        0x080037dd   Thumb Code    16  stm32f10x_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080037f5   Thumb Code    90  driver.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08003865   Thumb Code    16  stm32f10x_it.o(i.USART3_IRQHandler)
    USART_Cmd                                0x0800387d   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_ITConfig                           0x08003895   Thumb Code    64  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x080038d5   Thumb Code   150  stm32f10x_usart.o(i.USART_Init)
    USBWakeUp_IRQHandler                     0x08003971   Thumb Code    16  stm32f10x_it.o(i.USBWakeUp_IRQHandler)
    USB_HP_CAN_TX_IRQHandler                 0x08003989   Thumb Code    16  stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler)
    USB_LP_CAN_RX0_IRQHandler                0x080039a1   Thumb Code    16  stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler)
    Uart2Init                                0x080039b9   Thumb Code   174  driver.o(i.Uart2Init)
    UsageFaultException                      0x08003a79   Thumb Code    16  stm32f10x_it.o(i.UsageFaultException)
    WWDG_IRQHandler                          0x08003a91   Thumb Code    16  stm32f10x_it.o(i.WWDG_IRQHandler)
    XFLASH_GetDataFromUart                   0x08003aa9   Thumb Code  1464  xflash.o(i.XFLASH_GetDataFromUart)
    XFLASH_UartRxdIsr                        0x080040b5   Thumb Code   228  xflash.o(i.XFLASH_UartRxdIsr)
    XFLASH_WriteData                         0x080041bd   Thumb Code   342  xflash.o(i.XFLASH_WriteData)
    _is_digit                                0x0800431d   Thumb Code    14  __printf_wp.o(i._is_digit)
    main                                     0x0800432b   Thumb Code    22  main.o(i.main)
    ASCII_12X24                              0x08004354   Data        4608  font.o(.constdata)
    ASCII_16X32                              0x08005554   Data        6144  font.o(.constdata)
    FONT_32                                  0x08006d54   Data          12  font.o(.constdata)
    FONT_24                                  0x08006d60   Data          12  font.o(.constdata)
    Region$$Table$$Base                      0x08006d80   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006da0   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  main.o(.data)
    ptrApkTask                               0x20000004   Data           4  apk.o(.data)
    ptrApkTaskPre                            0x20000008   Data           4  apk.o(.data)
    Uart2RxdIsr                              0x20000014   Data           4  driver.o(.data)
    Uart2TxdIsr                              0x20000018   Data           4  driver.o(.data)
    IrExtiIsr                                0x2000001c   Data           4  driver.o(.data)
    TaskTimerIsr                             0x20000020   Data           4  driver.o(.data)
    ExFlashId                                0x20000024   Data           8  driver.o(.data)
    TP_TouchNum                              0x2000002c   Data           2  driver.o(.data)
    Font_ASCII_p                             0x2000003c   Data           4  lcd.o(.data)
    Font_CHINESE_p                           0x20000040   Data           4  lcd.o(.data)
    Font_ASCII_WIDTH                         0x20000044   Data           1  lcd.o(.data)
    Font_ASCII_HEIGHT                        0x20000045   Data           1  lcd.o(.data)
    nbyte0                                   0x20000046   Data           1  lcd.o(.data)
    nbyte1                                   0x20000047   Data           1  lcd.o(.data)
    FontExternalFlag                         0x20000048   Data           2  lcd.o(.data)
    UART_BdRate                              0x20000054   Data           4  uart.o(.data)
    FontEncode                               0x2000006c   Data           2  xfont.o(.data)
    FontDataLenAscii                         0x2000006e   Data           2  xfont.o(.data)
    FontDataLenOther                         0x20000070   Data           2  xfont.o(.data)
    XFONT_FontNum                            0x20000074   Data           4  xfont.o(.data)
    XFONT_FontEncode                         0x20000078   Data           4  xfont.o(.data)
    TaskTimeCnt                              0x20000080   Data           8  task.o(.data)
    TaskTimeCntNext                          0x20000088   Data           8  task.o(.data)
    CMD_Pack                                 0x20000090   Data          54  cmd.o(.bss)
    XFLASH_TempBuffer                        0x20000c92   Data       16384  xflash.o(.bss)
    XFONT_Inf                                0x20006d9c   Data         528  xfont.o(.bss)
    XFONT_CurrHeader                         0x20006fac   Data          52  xfont.o(.bss)
    __libspace_start                         0x20006fe0   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20007040   Data           0  libspace.o(.bss)
    SystemRunMode                            0x2000fffc   Data           4  main.o(.ARM.__AT_0x2000FFFC)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006e30, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00006da0, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000130   Data   RO            8    RESET               stm32f10x_vector.o
    0x08000130   0x00000008   Code   RO         3353  * !!!main             c_w.l(__main.o)
    0x08000138   0x00000034   Code   RO         3613    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0000001a   Code   RO         3615    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x00000002   PAD
    0x08000188   0x0000001c   Code   RO         3617    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x00000000   Code   RO         3348    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x00000006   Code   RO         3347    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001aa   0x00000004   Code   RO         3390    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001ae   0x00000002   Code   RO         3477    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001b0   0x00000000   Code   RO         3489    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3491    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3494    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3496    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3498    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3501    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3503    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3505    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3507    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3509    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3511    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3513    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3515    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3517    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3519    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3521    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3525    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3527    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3529    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001b0   0x00000000   Code   RO         3531    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001b0   0x00000002   Code   RO         3532    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001b2   0x00000002   Code   RO         3564    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001b4   0x00000000   Code   RO         3582    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x080001b4   0x00000000   Code   RO         3585    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001b4   0x00000000   Code   RO         3588    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001b4   0x00000000   Code   RO         3590    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x080001b4   0x00000000   Code   RO         3593    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001b4   0x00000002   Code   RO         3594    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001b6   0x00000000   Code   RO         3379    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001b6   0x00000000   Code   RO         3432    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001b6   0x00000006   Code   RO         3444    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001bc   0x00000000   Code   RO         3434    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001bc   0x00000004   Code   RO         3435    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001c0   0x00000000   Code   RO         3437    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001c0   0x00000008   Code   RO         3438    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001c8   0x00000002   Code   RO         3482    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001ca   0x00000000   Code   RO         3536    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001ca   0x00000004   Code   RO         3537    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001ce   0x00000006   Code   RO         3538    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001d4   0x0000002c   Code   RO            9    .text               stm32f10x_vector.o
    0x08000200   0x00000048   Code   RO         3315    .text               c_w.l(llsdiv.o)
    0x08000248   0x00000028   Code   RO         3319    .text               c_w.l(noretval__2sprintf.o)
    0x08000270   0x0000004e   Code   RO         3323    .text               c_w.l(_printf_pad.o)
    0x080002be   0x00000002   PAD
    0x080002c0   0x00000078   Code   RO         3325    .text               c_w.l(_printf_dec.o)
    0x08000338   0x00000138   Code   RO         3339    .text               c_w.l(__printf_flags_wp.o)
    0x08000470   0x00000006   Code   RO         3351    .text               c_w.l(heapauxi.o)
    0x08000476   0x000000ee   Code   RO         3380    .text               c_w.l(lludivv7m.o)
    0x08000564   0x000000b2   Code   RO         3384    .text               c_w.l(_printf_intcommon.o)
    0x08000616   0x00000002   PAD
    0x08000618   0x00000030   Code   RO         3386    .text               c_w.l(_printf_char_common.o)
    0x08000648   0x0000000a   Code   RO         3388    .text               c_w.l(_sputc.o)
    0x08000652   0x00000002   PAD
    0x08000654   0x00000008   Code   RO         3463    .text               c_w.l(libspace.o)
    0x0800065c   0x0000004a   Code   RO         3466    .text               c_w.l(sys_stackheap_outer.o)
    0x080006a6   0x0000000c   Code   RO         3470    .text               c_w.l(exit.o)
    0x080006b2   0x00000002   PAD
    0x080006b4   0x0000000c   Code   RO         3552    .text               c_w.l(sys_exit.o)
    0x080006c0   0x00000002   Code   RO         3569    .text               c_w.l(use_no_semi.o)
    0x080006c2   0x00000000   Code   RO         3571    .text               c_w.l(indicate_semi.o)
    0x080006c2   0x00000002   PAD
    0x080006c4   0x00000018   Code   RO         2121    i.ADC1_2_IRQHandler  stm32f10x_it.o
    0x080006dc   0x00000018   Code   RO         2122    i.ADC3_IRQHandler   stm32f10x_it.o
    0x080006f4   0x00000008   Code   RO         2548    i.APK_Common        apk.o
    0x080006fc   0x00000010   Code   RO         2549    i.APK_Continuous    apk.o
    0x0800070c   0x00000070   Code   RO         2550    i.APK_Init          apk.o
    0x0800077c   0x00000002   Code   RO         2553    i.APK_Periodicity   apk.o
    0x0800077e   0x00000002   PAD
    0x08000780   0x00000220   Code   RO         2554    i.Apk_Main          apk.o
    0x080009a0   0x00000018   Code   RO         2123    i.BusFaultException  stm32f10x_it.o
    0x080009b8   0x00000018   Code   RO         2124    i.CAN_RX1_IRQHandler  stm32f10x_it.o
    0x080009d0   0x00000018   Code   RO         2125    i.CAN_SCE_IRQHandler  stm32f10x_it.o
    0x080009e8   0x00000034   Code   RO         2854    i.CMD_Init          cmd.o
    0x08000a1c   0x00000140   Code   RO         2855    i.CMD_MainTask      cmd.o
    0x08000b5c   0x00000034   Code   RO         2856    i.CMD_PackCheck     cmd.o
    0x08000b90   0x0000014c   Code   RO         2857    i.CMD_PackRun       cmd.o
    0x08000cdc   0x000000a4   Code   RO         2600    i.CloclkInit        driver.o
    0x08000d80   0x0000000c   Code   RO         2601    i.CommonConfig      driver.o
    0x08000d8c   0x00000018   Code   RO         2126    i.DMA1_Channel1_IRQHandler  stm32f10x_it.o
    0x08000da4   0x00000018   Code   RO         2127    i.DMA1_Channel2_IRQHandler  stm32f10x_it.o
    0x08000dbc   0x00000018   Code   RO         2128    i.DMA1_Channel3_IRQHandler  stm32f10x_it.o
    0x08000dd4   0x00000018   Code   RO         2129    i.DMA1_Channel4_IRQHandler  stm32f10x_it.o
    0x08000dec   0x00000018   Code   RO         2130    i.DMA1_Channel5_IRQHandler  stm32f10x_it.o
    0x08000e04   0x00000018   Code   RO         2131    i.DMA1_Channel6_IRQHandler  stm32f10x_it.o
    0x08000e1c   0x00000018   Code   RO         2132    i.DMA1_Channel7_IRQHandler  stm32f10x_it.o
    0x08000e34   0x00000018   Code   RO         2133    i.DMA2_Channel1_IRQHandler  stm32f10x_it.o
    0x08000e4c   0x00000018   Code   RO         2134    i.DMA2_Channel2_IRQHandler  stm32f10x_it.o
    0x08000e64   0x00000018   Code   RO         2135    i.DMA2_Channel3_IRQHandler  stm32f10x_it.o
    0x08000e7c   0x00000018   Code   RO         2136    i.DMA2_Channel4_5_IRQHandler  stm32f10x_it.o
    0x08000e94   0x00000018   Code   RO         2137    i.DebugMonitor      stm32f10x_it.o
    0x08000eac   0x00000044   Code   RO         2604    i.DelayInit         driver.o
    0x08000ef0   0x00000044   Code   RO         2605    i.DelayMs           driver.o
    0x08000f34   0x00000048   Code   RO         2606    i.DelayUs           driver.o
    0x08000f7c   0x00000018   Code   RO         2138    i.EXTI0_IRQHandler  stm32f10x_it.o
    0x08000f94   0x00000040   Code   RO         2607    i.EXTI15_10_IRQHandler  driver.o
    0x08000fd4   0x00000018   Code   RO         2140    i.EXTI1_IRQHandler  stm32f10x_it.o
    0x08000fec   0x00000018   Code   RO         2141    i.EXTI2_IRQHandler  stm32f10x_it.o
    0x08001004   0x00000018   Code   RO         2142    i.EXTI3_IRQHandler  stm32f10x_it.o
    0x0800101c   0x00000018   Code   RO         2143    i.EXTI4_IRQHandler  stm32f10x_it.o
    0x08001034   0x00000018   Code   RO         2144    i.EXTI9_5_IRQHandler  stm32f10x_it.o
    0x0800104c   0x00000028   Code   RO          340    i.EXTI_GetITStatus  stm32f10x_exti.o
    0x08001074   0x00000018   Code   RO         2145    i.FLASH_IRQHandler  stm32f10x_it.o
    0x0800108c   0x0000001c   Code   RO         1839    i.FLASH_PrefetchBufferCmd  stm32f10x_flash.o
    0x080010a8   0x0000001c   Code   RO         1844    i.FLASH_SetLatency  stm32f10x_flash.o
    0x080010c4   0x00000018   Code   RO         2146    i.FSMC_IRQHandler   stm32f10x_it.o
    0x080010dc   0x00000114   Code   RO          397    i.GPIO_Init         stm32f10x_gpio.o
    0x080011f0   0x00000070   Code   RO          399    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x08001260   0x00000018   Code   RO         2147    i.HardFaultException  stm32f10x_it.o
    0x08001278   0x00000018   Code   RO         2148    i.I2C1_ER_IRQHandler  stm32f10x_it.o
    0x08001290   0x00000018   Code   RO         2149    i.I2C1_EV_IRQHandler  stm32f10x_it.o
    0x080012a8   0x00000018   Code   RO         2150    i.I2C2_ER_IRQHandler  stm32f10x_it.o
    0x080012c0   0x00000018   Code   RO         2151    i.I2C2_EV_IRQHandler  stm32f10x_it.o
    0x080012d8   0x0000052c   Code   RO         2608    i.ILI9481Init       driver.o
    0x08001804   0x00000124   Code   RO         2610    i.ILI9481SetDisplayWindow  driver.o
    0x08001928   0x0000004c   Code   RO         2611    i.ILI9481WriteRamPrepare  driver.o
    0x08001974   0x000000d4   Code   RO         2613    i.LCD_BackLightInit  driver.o
    0x08001a48   0x00000026   Code   RO         2969    i.LCD_Clear         lcd.o
    0x08001a6e   0x00000002   PAD
    0x08001a70   0x00000064   Code   RO         2976    i.LCD_DrawPoint     lcd.o
    0x08001ad4   0x00000150   Code   RO         2977    i.LCD_DrawProgress  lcd.o
    0x08001c24   0x00000034   Code   RO         2979    i.LCD_Init          lcd.o
    0x08001c58   0x000000f8   Code   RO         2614    i.LCD_InitGpio      driver.o
    0x08001d50   0x000000cc   Code   RO         2980    i.LCD_PutChar       lcd.o
    0x08001e1c   0x000000cc   Code   RO         2981    i.LCD_PutHanzi      lcd.o
    0x08001ee8   0x00000130   Code   RO         2982    i.LCD_PutStr        lcd.o
    0x08002018   0x0000006c   Code   RO         2983    i.LCD_PutStrCenter  lcd.o
    0x08002084   0x00000036   Code   RO         2984    i.LCD_PutStrLeftTop  lcd.o
    0x080020ba   0x00000002   PAD
    0x080020bc   0x0000006c   Code   RO         2985    i.LCD_PutStrRightCenter  lcd.o
    0x08002128   0x000001d4   Code   RO         2987    i.LCD_SearchFont    lcd.o
    0x080022fc   0x00000094   Code   RO         2988    i.LCD_SelectFont    lcd.o
    0x08002390   0x00000052   Code   RO         2989    i.LCD_SetBar        lcd.o
    0x080023e2   0x00000002   PAD
    0x080023e4   0x0000001c   Code   RO         2615    i.LCD_SetBright     driver.o
    0x08002400   0x00000058   Code   RO         2618    i.LCD_WriteConst    driver.o
    0x08002458   0x00000038   Code   RO         2619    i.LedInit           driver.o
    0x08002490   0x0000001c   Code   RO         2892    i.LrcCalc           common.o
    0x080024ac   0x00000018   Code   RO         2152    i.MemManageException  stm32f10x_it.o
    0x080024c4   0x00000018   Code   RO         2153    i.NMIException      stm32f10x_it.o
    0x080024dc   0x000000a4   Code   RO          519    i.NVIC_Init         stm32f10x_nvic.o
    0x08002580   0x00000014   Code   RO          520    i.NVIC_PriorityGroupConfig  stm32f10x_nvic.o
    0x08002594   0x0000015e   Code   RO         2893    i.Num2Str           common.o
    0x080026f2   0x00000002   PAD
    0x080026f4   0x00000018   Code   RO         2154    i.PVD_IRQHandler    stm32f10x_it.o
    0x0800270c   0x00000018   Code   RO         2155    i.PendSVC           stm32f10x_it.o
    0x08002724   0x00000020   Code   RO          757    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08002744   0x00000020   Code   RO          759    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08002764   0x0000004c   Code   RO          766    i.RCC_DeInit        stm32f10x_rcc.o
    0x080027b0   0x000000d4   Code   RO          767    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08002884   0x0000003c   Code   RO          768    i.RCC_GetFlagStatus  stm32f10x_rcc.o
    0x080028c0   0x00000010   Code   RO          770    i.RCC_GetSYSCLKSource  stm32f10x_rcc.o
    0x080028d0   0x00000018   Code   RO          771    i.RCC_HCLKConfig    stm32f10x_rcc.o
    0x080028e8   0x0000004c   Code   RO          772    i.RCC_HSEConfig     stm32f10x_rcc.o
    0x08002934   0x00000018   Code   RO         2156    i.RCC_IRQHandler    stm32f10x_it.o
    0x0800294c   0x00000018   Code   RO          778    i.RCC_PCLK1Config   stm32f10x_rcc.o
    0x08002964   0x00000018   Code   RO          779    i.RCC_PCLK2Config   stm32f10x_rcc.o
    0x0800297c   0x0000000c   Code   RO          780    i.RCC_PLLCmd        stm32f10x_rcc.o
    0x08002988   0x0000001c   Code   RO          781    i.RCC_PLLConfig     stm32f10x_rcc.o
    0x080029a4   0x00000018   Code   RO          784    i.RCC_SYSCLKConfig  stm32f10x_rcc.o
    0x080029bc   0x00000018   Code   RO         2157    i.RTCAlarm_IRQHandler  stm32f10x_it.o
    0x080029d4   0x00000018   Code   RO         2158    i.RTC_IRQHandler    stm32f10x_it.o
    0x080029ec   0x00000018   Code   RO         2159    i.SDIO_IRQHandler   stm32f10x_it.o
    0x08002a04   0x00000018   Code   RO         2160    i.SPI1_IRQHandler   stm32f10x_it.o
    0x08002a1c   0x00000018   Code   RO         2161    i.SPI2_IRQHandler   stm32f10x_it.o
    0x08002a34   0x00000018   Code   RO         2162    i.SPI3_IRQHandler   stm32f10x_it.o
    0x08002a4c   0x00000018   Code   RO          961    i.SPI_Cmd           stm32f10x_spi.o
    0x08002a64   0x000000b8   Code   RO         2623    i.SPI_FLASH_EraseSector  driver.o
    0x08002b1c   0x000000a8   Code   RO         2624    i.SPI_FLASH_Init    driver.o
    0x08002bc4   0x000000e0   Code   RO         2625    i.SPI_FLASH_ReadData  driver.o
    0x08002ca4   0x000001a4   Code   RO         2626    i.SPI_FLASH_ReadId  driver.o
    0x08002e48   0x0000005c   Code   RO         2834    i.SPI_FLASH_WaitBusy  driver.o
    0x08002ea4   0x0000016c   Code   RO         2627    i.SPI_FLASH_WriteSector  driver.o
    0x08003010   0x0000003c   Code   RO          974    i.SPI_Init          stm32f10x_spi.o
    0x0800304c   0x00000018   Code   RO         2163    i.SVCHandler        stm32f10x_it.o
    0x08003064   0x000000e4   Code   RO         2628    i.Spi3Init          driver.o
    0x08003148   0x0000001a   Code   RO         2896    i.StrLen            common.o
    0x08003162   0x00000002   PAD
    0x08003164   0x00000018   Code   RO         2164    i.SysTickHandler    stm32f10x_it.o
    0x0800317c   0x00000028   Code   RO         2022    i.SystemInit        main.o
    0x080031a4   0x00000018   Code   RO         2165    i.TAMPER_IRQHandler  stm32f10x_it.o
    0x080031bc   0x00000030   Code   RO         3283    i.TASK_Init         task.o
    0x080031ec   0x00000020   Code   RO         3284    i.TASK_Periodicity  task.o
    0x0800320c   0x00000014   Code   RO         3285    i.TASK_Run          task.o
    0x08003220   0x00000090   Code   RO         2629    i.TASK_TimerInit    driver.o
    0x080032b0   0x00000018   Code   RO         2166    i.TIM1_BRK_IRQHandler  stm32f10x_it.o
    0x080032c8   0x00000018   Code   RO         2167    i.TIM1_CC_IRQHandler  stm32f10x_it.o
    0x080032e0   0x00000018   Code   RO         2168    i.TIM1_TRG_COM_IRQHandler  stm32f10x_it.o
    0x080032f8   0x00000024   Code   RO         2630    i.TIM1_UP_IRQHandler  driver.o
    0x0800331c   0x00000018   Code   RO         2170    i.TIM2_IRQHandler   stm32f10x_it.o
    0x08003334   0x00000018   Code   RO         2171    i.TIM3_IRQHandler   stm32f10x_it.o
    0x0800334c   0x00000018   Code   RO         2172    i.TIM4_IRQHandler   stm32f10x_it.o
    0x08003364   0x00000034   Code   RO         2631    i.TIM5_IRQHandler   driver.o
    0x08003398   0x00000018   Code   RO         2174    i.TIM6_IRQHandler   stm32f10x_it.o
    0x080033b0   0x00000018   Code   RO         2175    i.TIM7_IRQHandler   stm32f10x_it.o
    0x080033c8   0x00000018   Code   RO         2176    i.TIM8_BRK_IRQHandler  stm32f10x_it.o
    0x080033e0   0x00000018   Code   RO         2177    i.TIM8_CC_IRQHandler  stm32f10x_it.o
    0x080033f8   0x00000018   Code   RO         2178    i.TIM8_TRG_COM_IRQHandler  stm32f10x_it.o
    0x08003410   0x00000018   Code   RO         2179    i.TIM8_UP_IRQHandler  stm32f10x_it.o
    0x08003428   0x00000018   Code   RO         1107    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08003440   0x00000006   Code   RO         1113    i.TIM_ClearFlag     stm32f10x_tim.o
    0x08003446   0x00000018   Code   RO         1119    i.TIM_Cmd           stm32f10x_tim.o
    0x0800345e   0x0000001e   Code   RO         1121    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x0800347c   0x00000012   Code   RO         1144    i.TIM_ITConfig      stm32f10x_tim.o
    0x0800348e   0x00000002   PAD
    0x08003490   0x00000074   Code   RO         1163    i.TIM_OC4Init       stm32f10x_tim.o
    0x08003504   0x0000001a   Code   RO         1165    i.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x0800351e   0x00000002   PAD
    0x08003520   0x0000003c   Code   RO         1190    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x0800355c   0x00000018   Code   RO         2180    i.UART4_IRQHandler  stm32f10x_it.o
    0x08003574   0x00000018   Code   RO         2181    i.UART5_IRQHandler  stm32f10x_it.o
    0x0800358c   0x0000001c   Code   RO         3159    i.UART_ClearRxdBuffer  uart.o
    0x080035a8   0x00000054   Code   RO         3160    i.UART_GetRxdData   uart.o
    0x080035fc   0x00000034   Code   RO         3161    i.UART_GetRxdFifoLen  uart.o
    0x08003630   0x00000070   Code   RO         3162    i.UART_Init         uart.o
    0x080036a0   0x00000040   Code   RO         3163    i.UART_RxdIsr       uart.o
    0x080036e0   0x00000088   Code   RO         3164    i.UART_SendData     uart.o
    0x08003768   0x00000024   Code   RO         3165    i.UART_SendStr      uart.o
    0x0800378c   0x00000050   Code   RO         3166    i.UART_TxdIsr       uart.o
    0x080037dc   0x00000018   Code   RO         2182    i.USART1_IRQHandler  stm32f10x_it.o
    0x080037f4   0x00000070   Code   RO         2634    i.USART2_IRQHandler  driver.o
    0x08003864   0x00000018   Code   RO         2184    i.USART3_IRQHandler  stm32f10x_it.o
    0x0800387c   0x00000018   Code   RO         1658    i.USART_Cmd         stm32f10x_usart.o
    0x08003894   0x00000040   Code   RO         1664    i.USART_ITConfig    stm32f10x_usart.o
    0x080038d4   0x0000009c   Code   RO         1665    i.USART_Init        stm32f10x_usart.o
    0x08003970   0x00000018   Code   RO         2185    i.USBWakeUp_IRQHandler  stm32f10x_it.o
    0x08003988   0x00000018   Code   RO         2186    i.USB_HP_CAN_TX_IRQHandler  stm32f10x_it.o
    0x080039a0   0x00000018   Code   RO         2187    i.USB_LP_CAN_RX0_IRQHandler  stm32f10x_it.o
    0x080039b8   0x000000c0   Code   RO         2635    i.Uart2Init         driver.o
    0x08003a78   0x00000018   Code   RO         2188    i.UsageFaultException  stm32f10x_it.o
    0x08003a90   0x00000018   Code   RO         2189    i.WWDG_IRQHandler   stm32f10x_it.o
    0x08003aa8   0x0000060c   Code   RO         3220    i.XFLASH_GetDataFromUart  xflash.o
    0x080040b4   0x00000108   Code   RO         3221    i.XFLASH_UartRxdIsr  xflash.o
    0x080041bc   0x00000160   Code   RO         3222    i.XFLASH_WriteData  xflash.o
    0x0800431c   0x0000000e   Code   RO         3337    i._is_digit         c_w.l(__printf_wp.o)
    0x0800432a   0x00000016   Code   RO         2023    i.main              main.o
    0x08004340   0x00000014   Data   RO          787    .constdata          stm32f10x_rcc.o
    0x08004354   0x00002a18   Data   RO         2933    .constdata          font.o
    0x08006d6c   0x00000011   Data   RO         3340    .constdata          c_w.l(__printf_flags_wp.o)
    0x08006d7d   0x00000003   PAD
    0x08006d80   0x00000020   Data   RO         3611    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00010000, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000004   Data   RW         2025    .data               main.o
    0x20000004   0x00000008   Data   RW         2555    .data               apk.o
    0x2000000c   0x00000022   Data   RW         2638    .data               driver.o
    0x2000002e   0x00000002   PAD
    0x20000030   0x0000000a   Data   RW         2859    .data               cmd.o
    0x2000003a   0x00000002   PAD
    0x2000003c   0x00000014   Data   RW         2991    .data               lcd.o
    0x20000050   0x00000012   Data   RW         3168    .data               uart.o
    0x20000062   0x00000009   Data   RW         3224    .data               xflash.o
    0x2000006b   0x00000001   PAD
    0x2000006c   0x00000010   Data   RW         3255    .data               xfont.o
    0x2000007c   0x00000004   PAD
    0x20000080   0x00000010   Data   RW         3286    .data               task.o
    0x20000090   0x00000036   Zero   RW         2858    .bss                cmd.o
    0x200000c6   0x00000bcc   Zero   RW         3167    .bss                uart.o
    0x20000c92   0x00006108   Zero   RW         3223    .bss                xflash.o
    0x20006d9a   0x00000002   PAD
    0x20006d9c   0x00000244   Zero   RW         3254    .bss                xfont.o
    0x20006fe0   0x00000060   Zero   RW         3464    .bss                c_w.l(libspace.o)
    0x20007040   0x00001000   Zero   RW            7    HEAP                stm32f10x_vector.o
    0x20008040   0x00001000   Zero   RW            6    STACK               stm32f10x_vector.o
    0x20009040   0x00006fbc   PAD
    0x2000fffc   0x00000004   Zero   RW         2024    .ARM.__AT_0x2000FFFC  main.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       682        180          0          8          0       2913   apk.o
       756        174          0         10         54       3127   cmd.o
       404          0          0          0          0       2517   common.o
         0          0          0          0          0         32   cortexm3_macro.o
      4988        334          0         34          0      18572   driver.o
         0          0      10776          0          0       1154   font.o
      2206        150          0         20          0      12070   lcd.o
        62         12          0          4          4       1728   main.o
         0          0          0          0          0      23980   stm32f10x_adc.o
        40          6          0          0          0        583   stm32f10x_exti.o
        56         12          0          0          0       1025   stm32f10x_flash.o
       388          6          0          0          0       8794   stm32f10x_gpio.o
      1560        520          0          0          0      29102   stm32f10x_it.o
       184         24          0          0          0       9363   stm32f10x_nvic.o
       640         94         20          0          0       9534   stm32f10x_rcc.o
        84          0          0          0          0       8549   stm32f10x_spi.o
       304         20          0          0          0      29109   stm32f10x_tim.o
       244          6          0          0          0       9658   stm32f10x_usart.o
        44         26        304          0       8192        348   stm32f10x_vector.o
       100         26          0         16          0       1616   task.o
       592        138          0         18       3020       5430   uart.o
      2164        256          0          9      24840       4896   xflash.o
         0          0          0         16        580       1719   xfont.o

    ----------------------------------------------------------------------
     15514       <USER>      <GROUP>        144      65296     185819   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        16          0          0          9      28606          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       312          4         17          0          0         92   __printf_flags_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0         68   _sputc.o
        12          0          0          0          0         72   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        72          0          0          0          0         76   llsdiv.o
       238          0          0          0          0        100   lludivv7m.o
        40          6          0          0          0         84   noretval__2sprintf.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
      1398         <USER>         <GROUP>          0         96       1448   Library Totals
        12          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1386         48         17          0         96       1448   c_w.l

    ----------------------------------------------------------------------
      1398         <USER>         <GROUP>          0         96       1448   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     16912       2032      11152        144      65392     178267   Grand Totals
     16912       2032      11152        144      65392     178267   ELF Image Totals
     16912       2032      11152        144          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                28064 (  27.41kB)
    Total RW  Size (RW Data + ZI Data)             65536 (  64.00kB)
    Total ROM Size (Code + RO Data + RW Data)      28208 (  27.55kB)

==============================================================================

