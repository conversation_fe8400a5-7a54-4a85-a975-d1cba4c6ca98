Component: ARM Compiler 5.05 update 2 (build 169) Tool: armlink [4d0f33]

==============================================================================

Section Cross References

    stm32f10x_vector.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(RESET) refers to stm32f10x_vector.o(STACK) for __initial_sp
    stm32f10x_vector.o(RESET) refers to stm32f10x_vector.o(.text) for Reset_Handler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.NMIException) for NMIException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.HardFaultException) for HardFaultException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.MemManageException) for MemManageException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.BusFaultException) for BusFaultException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.UsageFaultException) for UsageFaultException
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SVCHandler) for SVCHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DebugMonitor) for DebugMonitor
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.PendSVC) for PendSVC
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SysTickHandler) for SysTickHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.WWDG_IRQHandler) for WWDG_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.PVD_IRQHandler) for PVD_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TAMPER_IRQHandler) for TAMPER_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.RTC_IRQHandler) for RTC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.FLASH_IRQHandler) for FLASH_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.RCC_IRQHandler) for RCC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel3_IRQHandler) for DMA1_Channel3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel4_IRQHandler) for DMA1_Channel4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel6_IRQHandler) for DMA1_Channel6_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA1_Channel7_IRQHandler) for DMA1_Channel7_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.ADC1_2_IRQHandler) for ADC1_2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler) for USB_HP_CAN_TX_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler) for USB_LP_CAN_RX0_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.CAN_RX1_IRQHandler) for CAN_RX1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.CAN_SCE_IRQHandler) for CAN_SCE_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.EXTI9_5_IRQHandler) for EXTI9_5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM1_BRK_IRQHandler) for TIM1_BRK_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.TIM1_UP_IRQHandler) for TIM1_UP_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler) for TIM1_TRG_COM_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM1_CC_IRQHandler) for TIM1_CC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C1_EV_IRQHandler) for I2C1_EV_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C1_ER_IRQHandler) for I2C1_ER_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C2_EV_IRQHandler) for I2C2_EV_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.I2C2_ER_IRQHandler) for I2C2_ER_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SPI1_IRQHandler) for SPI1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SPI2_IRQHandler) for SPI2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.USART2_IRQHandler) for USART2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.EXTI15_10_IRQHandler) for EXTI15_10_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.RTCAlarm_IRQHandler) for RTCAlarm_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.USBWakeUp_IRQHandler) for USBWakeUp_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_BRK_IRQHandler) for TIM8_BRK_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_UP_IRQHandler) for TIM8_UP_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler) for TIM8_TRG_COM_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM8_CC_IRQHandler) for TIM8_CC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.ADC3_IRQHandler) for ADC3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.FSMC_IRQHandler) for FSMC_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    stm32f10x_vector.o(RESET) refers to driver.o(i.TIM5_IRQHandler) for TIM5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.SPI3_IRQHandler) for SPI3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM6_IRQHandler) for TIM6_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel1_IRQHandler) for DMA2_Channel1_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel2_IRQHandler) for DMA2_Channel2_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel3_IRQHandler) for DMA2_Channel3_IRQHandler
    stm32f10x_vector.o(RESET) refers to stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler) for DMA2_Channel4_5_IRQHandler
    stm32f10x_vector.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    stm32f10x_vector.o(.text) refers to main.o(i.SystemInit) for SystemInit
    stm32f10x_vector.o(.text) refers to __main.o(!!!main) for __main
    stm32f10x_vector.o(.text) refers to stm32f10x_vector.o(HEAP) for Heap_Mem
    stm32f10x_vector.o(.text) refers to stm32f10x_vector.o(STACK) for Stack_Mem
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_nvic.o(i.NVIC_BASEPRICONFIG) refers to cortexm3_macro.o(.text) for __BASEPRICONFIG
    stm32f10x_nvic.o(i.NVIC_GetBASEPRI) refers to cortexm3_macro.o(.text) for __GetBASEPRI
    stm32f10x_nvic.o(i.NVIC_RESETFAULTMASK) refers to cortexm3_macro.o(.text) for __RESETFAULTMASK
    stm32f10x_nvic.o(i.NVIC_RESETPRIMASK) refers to cortexm3_macro.o(.text) for __RESETPRIMASK
    stm32f10x_nvic.o(i.NVIC_SETFAULTMASK) refers to cortexm3_macro.o(.text) for __SETFAULTMASK
    stm32f10x_nvic.o(i.NVIC_SETPRIMASK) refers to cortexm3_macro.o(.text) for __SETPRIMASK
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode) refers to cortexm3_macro.o(.text) for __WFI
    stm32f10x_pwr.o(i.PWR_EnterSTOPMode) refers to cortexm3_macro.o(.text) for __WFI
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.constdata) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(.data) for StartUpCounter
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.delay) for delay
    main.o(i.SystemInit) refers to driver.o(i.DelayInit) for DelayInit
    main.o(i.SystemInit) refers to driver.o(i.DelayMs) for DelayMs
    main.o(i.SystemInit) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    main.o(i.SystemInit) refers to main.o(.data) for SystemCoreClock
    main.o(i.main) refers to driver.o(i.CloclkInit) for CloclkInit
    main.o(i.main) refers to driver.o(i.CommonConfig) for CommonConfig
    main.o(i.main) refers to driver.o(i.LedInit) for LedInit
    main.o(i.main) refers to driver.o(i.DelayMs) for DelayMs
    stm32f10x_it.o(i.ADC1_2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.ADC3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.BusFaultException) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.CAN_RX1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.CAN_SCE_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel4_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel6_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA1_Channel7_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.DebugMonitor) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI15_10_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI4_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.EXTI9_5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.FLASH_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.FSMC_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.HardFaultException) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C1_ER_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C1_EV_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C2_ER_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.I2C2_EV_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.MemManageException) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.NMIException) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.PVD_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.PendSVC) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.RCC_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.RTCAlarm_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.RTC_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SDIO_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SPI1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SPI2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SPI3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SVCHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.SysTickHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TAMPER_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_BRK_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_CC_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM1_UP_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM4_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM6_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM7_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_BRK_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_CC_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.TIM8_UP_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.UART4_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.UART5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USART1_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USART2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USART3_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USBWakeUp_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.UsageFaultException) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    stm32f10x_it.o(i.WWDG_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    driver.o(i.AdcInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.AdcInit) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    driver.o(i.AdcInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig) for ADC_InjectedSequencerLengthConfig
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_InjectedChannelConfig) for ADC_InjectedChannelConfig
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd) for ADC_AutoInjectedConvCmd
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    driver.o(i.AdcInit) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    driver.o(i.AdcInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_DeInit) for RCC_DeInit
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_HSEConfig) for RCC_HSEConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PLLCmd) for RCC_PLLCmd
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    driver.o(i.CloclkInit) refers to stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd) for FLASH_PrefetchBufferCmd
    driver.o(i.CloclkInit) refers to stm32f10x_flash.o(i.FLASH_SetLatency) for FLASH_SetLatency
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_HCLKConfig) for RCC_HCLKConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PCLK2Config) for RCC_PCLK2Config
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PCLK1Config) for RCC_PCLK1Config
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_PLLConfig) for RCC_PLLConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_SYSCLKConfig) for RCC_SYSCLKConfig
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_GetSYSCLKSource) for RCC_GetSYSCLKSource
    driver.o(i.CloclkInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.CloclkInit) refers to driver.o(i.DelayInit) for DelayInit
    driver.o(i.CloclkInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.CommonConfig) refers to stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    driver.o(i.DacInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.DacInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.DacInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.DacInit) refers to stm32f10x_dac.o(i.DAC_Init) for DAC_Init
    driver.o(i.DacInit) refers to stm32f10x_dac.o(i.DAC_Cmd) for DAC_Cmd
    driver.o(i.DelayInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.DelayInit) refers to driver.o(.data) for fac_us
    driver.o(i.DelayMs) refers to driver.o(.data) for fac_ms
    driver.o(i.DelayUs) refers to driver.o(.data) for fac_us
    driver.o(i.EXTI15_10_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    driver.o(i.EXTI15_10_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    driver.o(i.EXTI15_10_IRQHandler) refers to driver.o(.data) for IrExtiIsr
    driver.o(i.ILI9481Init) refers to driver.o(i.DelayMs) for DelayMs
    driver.o(i.IrInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.IrInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.IrInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.IrInit) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    driver.o(i.IrInit) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    driver.o(i.IrInit) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.IrInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.IrInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.IrInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.IrInit) refers to driver.o(.data) for IrExtiIsr
    driver.o(i.IrInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    driver.o(i.LCD_BackLightInit) refers to driver.o(i.LCD_SetBright) for LCD_SetBright
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.LCD_BackLightInit) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    driver.o(i.LCD_BackLightInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.LCD_InitGpio) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.LCD_InitGpio) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.LCD_InitGpio) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    driver.o(i.LCD_WriteArrayFromXflash) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    driver.o(i.LCD_WriteArrayFromXflash) refers to xflash.o(.bss) for XFLASH_TempBuffer
    driver.o(i.LedInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.PwmInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.PwmInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.PwmInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_BDTRConfig) for TIM_BDTRConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    driver.o(i.PwmInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.PwmInit) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.PwmInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.SPI_FLASH_EraseSector) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.Spi3Init) for Spi3Init
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.DelayUs) for DelayUs
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_Init) refers to driver.o(i.SPI_FLASH_ReadId) for SPI_FLASH_ReadId
    driver.o(i.SPI_FLASH_ReadData) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_ReadId) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.SPI_FLASH_ReadId) refers to driver.o(.data) for ExFlashId
    driver.o(i.SPI_FLASH_WriteSector) refers to driver.o(i.SPI_FLASH_WaitBusy) for SPI_FLASH_WaitBusy
    driver.o(i.Spi3Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.Spi3Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.Spi3Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    driver.o(i.Spi3Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    driver.o(i.Spi3Init) refers to stm32f10x_spi.o(i.SPI_Init) for SPI_Init
    driver.o(i.Spi3Init) refers to stm32f10x_spi.o(i.SPI_Cmd) for SPI_Cmd
    driver.o(i.TASK_TimerInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    driver.o(i.TASK_TimerInit) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    driver.o(i.TASK_TimerInit) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.TASK_TimerInit) refers to main.o(.data) for SystemCoreClock
    driver.o(i.TASK_TimerInit) refers to driver.o(.data) for TaskTimerIsr
    driver.o(i.TIM1_UP_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    driver.o(i.TIM5_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    driver.o(i.TIM5_IRQHandler) refers to driver.o(.data) for TaskTimerIsr
    driver.o(i.TP_Init) refers to driver.o(.data) for TP_TouchNum
    driver.o(i.TP_TouchScan) refers to driver.o(.data) for TP_TouchNum
    driver.o(i.USART2_IRQHandler) refers to main.o(.ARM.__AT_0x2000FFFC) for SystemRunMode
    driver.o(i.USART2_IRQHandler) refers to driver.o(.data) for Uart2TxdIsr
    driver.o(i.Uart2Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    driver.o(i.Uart2Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    driver.o(i.Uart2Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    driver.o(i.Uart2Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    driver.o(i.Uart2Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    driver.o(i.Uart2Init) refers to stm32f10x_nvic.o(i.NVIC_Init) for NVIC_Init
    driver.o(i.Uart2Init) refers to driver.o(.data) for Uart2RxdIsr
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd) for IWDG_WriteAccessCmd
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_SetPrescaler) for IWDG_SetPrescaler
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_SetReload) for IWDG_SetReload
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_ReloadCounter) for IWDG_ReloadCounter
    driver.o(i.WatchDogInit) refers to stm32f10x_iwdg.o(i.IWDG_Enable) for IWDG_Enable
    cmd.o(i.CMD_Init) refers to uart.o(i.UART_ClearRxdBuffer) for UART_ClearRxdBuffer
    cmd.o(i.CMD_Init) refers to cmd.o(.data) for timeout_cnt
    cmd.o(i.CMD_Init) refers to cmd.o(.bss) for CMD_Pack
    cmd.o(i.CMD_MainTask) refers to uart.o(i.UART_GetRxdData) for UART_GetRxdData
    cmd.o(i.CMD_MainTask) refers to uart.o(i.UART_GetRxdFifoLen) for UART_GetRxdFifoLen
    cmd.o(i.CMD_MainTask) refers to cmd.o(i.CMD_PackCheck) for CMD_PackCheck
    cmd.o(i.CMD_MainTask) refers to cmd.o(i.CMD_PackRun) for CMD_PackRun
    cmd.o(i.CMD_MainTask) refers to cmd.o(.data) for PackTimeout
    cmd.o(i.CMD_MainTask) refers to cmd.o(.bss) for CMD_Pack
    cmd.o(i.CMD_PackRun) refers to uart.o(i.UART_SendStr) for UART_SendStr
    cmd.o(i.CMD_PackRun) refers to xflash.o(i.XFLASH_GetDataFromUart) for XFLASH_GetDataFromUart
    cmd.o(i.CMD_PackRun) refers to cmd.o(.bss) for CMD_Pack
    common.o(i.Str2Double) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    common.o(i.Str2Double) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    common.o(i.Str2Double) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    common.o(i.Str2Double) refers to pow.o(i.pow) for pow
    ir.o(i.IR_Decode) refers to ir.o(.data) for IR_start
    ir.o(i.IR_Init) refers to driver.o(i.IrInit) for IrInit
    ir.o(i.IR_Init) refers to ir.o(i.IR_Decode) for IR_Decode
    ir.o(i.IR_Init) refers to driver.o(.data) for IrExtiIsr
    ir.o(i.IR_Init) refers to ir.o(.data) for IR_LedTimeout
    lcd.o(i.LCD_Clear) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_Clear) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_Clear) refers to driver.o(i.LCD_WriteConst) for LCD_WriteConst
    lcd.o(i.LCD_DrawBmpFromRawData) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_DrawBmpFromRawData) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_DrawBmpFromRawData) refers to driver.o(i.LCD_WriteArray) for LCD_WriteArray
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to driver.o(i.LCD_WriteArrayFromXflash) for LCD_WriteArrayFromXflash
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to xflash.o(.bss) for XFLASH_TempBuffer
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to lcd.o(.data) for PageId
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to touch.o(.data) for TouchRectInfoNum
    lcd.o(i.LCD_DrawBmpFromXflashPackData) refers to touch.o(.bss) for TouchRectInfo
    lcd.o(i.LCD_DrawChildPic) refers to lcd.o(i.LCD_DrawBmpFromXflashPackData) for LCD_DrawBmpFromXflashPackData
    lcd.o(i.LCD_DrawChildPic) refers to lcd.o(.data) for PageId
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_DrawPage) refers to lcd.o(i.LCD_DrawBmpFromXflashPackData) for LCD_DrawBmpFromXflashPackData
    lcd.o(i.LCD_DrawPageCenter) refers to lcd.o(i.LCD_DrawBmpFromXflashPackData) for LCD_DrawBmpFromXflashPackData
    lcd.o(i.LCD_DrawPoint) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_DrawPoint) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_DrawProgress) refers to lcd.o(i.LCD_SetBar) for LCD_SetBar
    lcd.o(i.LCD_Init) refers to driver.o(i.LCD_InitGpio) for LCD_InitGpio
    lcd.o(i.LCD_Init) refers to driver.o(i.ILI9481Init) for ILI9481Init
    lcd.o(i.LCD_Init) refers to driver.o(i.LCD_BackLightInit) for LCD_BackLightInit
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_SelectFont) for LCD_SelectFont
    lcd.o(i.LCD_Init) refers to lcd.o(.data) for PageOffsetX
    lcd.o(i.LCD_Init) refers to font.o(.constdata) for FONT_32
    lcd.o(i.LCD_PutChar) refers to lcd.o(i.LCD_SearchFont) for LCD_SearchFont
    lcd.o(i.LCD_PutChar) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_PutChar) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_PutHanzi) refers to lcd.o(i.LCD_SearchFont) for LCD_SearchFont
    lcd.o(i.LCD_PutHanzi) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_PutHanzi) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_PutStr) refers to lcd.o(i.LCD_PutHanzi) for LCD_PutHanzi
    lcd.o(i.LCD_PutStr) refers to lcd.o(i.LCD_PutChar) for LCD_PutChar
    lcd.o(i.LCD_PutStr) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_PutStrCenter) refers to common.o(i.StrLen) for StrLen
    lcd.o(i.LCD_PutStrCenter) refers to lcd.o(i.LCD_PutStr) for LCD_PutStr
    lcd.o(i.LCD_PutStrCenter) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_PutStrLeftTop) refers to lcd.o(i.LCD_PutStr) for LCD_PutStr
    lcd.o(i.LCD_PutStrRightCenter) refers to common.o(i.StrLen) for StrLen
    lcd.o(i.LCD_PutStrRightCenter) refers to lcd.o(i.LCD_PutStr) for LCD_PutStr
    lcd.o(i.LCD_PutStrRightCenter) refers to lcd.o(.data) for Font_ASCII_WIDTH
    lcd.o(i.LCD_SearchFont) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    lcd.o(i.LCD_SearchFont) refers to lcd.o(.data) for FontExternalFlag
    lcd.o(i.LCD_SearchFont) refers to xfont.o(.bss) for XFONT_CurrHeader
    lcd.o(i.LCD_SearchFont) refers to xfont.o(.data) for FontDataLenAscii
    lcd.o(i.LCD_SelectFont) refers to lcd.o(.data) for Font_ASCII_p
    lcd.o(i.LCD_SetBar) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    lcd.o(i.LCD_SetBar) refers to driver.o(i.ILI9481WriteRamPrepare) for ILI9481WriteRamPrepare
    lcd.o(i.LCD_SetBar) refers to driver.o(i.LCD_WriteConst) for LCD_WriteConst
    lcd.o(i.LCD_SetCursor) refers to driver.o(i.ILI9481SetDisplayWindow) for ILI9481SetDisplayWindow
    touch.o(i.TOUCH_DispPos) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    touch.o(i.TOUCH_DispPos) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    touch.o(i.TOUCH_DispPos) refers to _printf_dec.o(.text) for _printf_int_dec
    touch.o(i.TOUCH_DispPos) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    touch.o(i.TOUCH_DispPos) refers to lcd.o(i.LCD_PutStrLeftTop) for LCD_PutStrLeftTop
    touch.o(i.TOUCH_DispPos) refers to noretval__2sprintf.o(.text) for __2sprintf
    touch.o(i.TOUCH_DispPos) refers to driver.o(i.TP_TouchScan) for TP_TouchScan
    touch.o(i.TOUCH_DispPos) refers to common.o(i.Num2Str) for Num2Str
    touch.o(i.TOUCH_DispPos) refers to driver.o(i.DelayMs) for DelayMs
    touch.o(i.TOUCH_DispPos) refers to touch.o(.bss) for TouchState
    touch.o(i.TOUCH_DispPos) refers to driver.o(.data) for TP_TouchNum
    touch.o(i.TOUCH_DispPos) refers to driver.o(.bss) for TP_TouchPoint
    touch.o(i.TOUCH_DrawLine) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    touch.o(i.TOUCH_DrawLine) refers to driver.o(i.TP_TouchScan) for TP_TouchScan
    touch.o(i.TOUCH_DrawLine) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    touch.o(i.TOUCH_DrawLine) refers to driver.o(i.DelayMs) for DelayMs
    touch.o(i.TOUCH_DrawLine) refers to driver.o(.data) for TP_TouchNum
    touch.o(i.TOUCH_DrawLine) refers to driver.o(.bss) for TP_TouchPoint
    touch.o(i.TOUCH_GetIdByPoint) refers to touch.o(.bss) for TouchRectInfo
    touch.o(i.TOUCH_GetIdByPoint) refers to touch.o(.data) for TouchRectInfoNum
    touch.o(i.TOUCH_GetState) refers to driver.o(i.TP_TouchScan) for TP_TouchScan
    touch.o(i.TOUCH_GetState) refers to touch.o(.data) for num_last
    touch.o(i.TOUCH_GetState) refers to driver.o(.data) for TP_TouchNum
    touch.o(i.TOUCH_GetState) refers to touch.o(.bss) for TouchState
    touch.o(i.TOUCH_GetState) refers to driver.o(.bss) for TP_TouchPoint
    uart.o(i.UART_ClearRxdBuffer) refers to uart.o(.data) for RxdFifoDataFront
    uart.o(i.UART_ClearRxdBuffer) refers to uart.o(.bss) for RxdFifoData
    uart.o(i.UART_GetRxdData) refers to uart.o(.bss) for RxdFifoData
    uart.o(i.UART_GetRxdData) refers to uart.o(.data) for RxdFifoDataFront
    uart.o(i.UART_GetRxdFifoLen) refers to uart.o(.data) for RxdFifoDataRear
    uart.o(i.UART_Init) refers to driver.o(i.Uart2Init) for Uart2Init
    uart.o(i.UART_Init) refers to uart.o(.data) for UART_InitFlag
    uart.o(i.UART_Init) refers to uart.o(i.UART_RxdIsr) for UART_RxdIsr
    uart.o(i.UART_Init) refers to driver.o(.data) for Uart2RxdIsr
    uart.o(i.UART_Init) refers to uart.o(i.UART_TxdIsr) for UART_TxdIsr
    uart.o(i.UART_RxdIsr) refers to uart.o(i.UART_GetRxdFifoLen) for UART_GetRxdFifoLen
    uart.o(i.UART_RxdIsr) refers to uart.o(.bss) for RxdFifoData
    uart.o(i.UART_RxdIsr) refers to uart.o(.data) for RxdFifoDataRear
    uart.o(i.UART_SendData) refers to uart.o(i.UART_TxdIsr) for UART_TxdIsr
    uart.o(i.UART_SendData) refers to uart.o(.data) for UART_InitFlag
    uart.o(i.UART_SendData) refers to uart.o(.bss) for TxdFifoData
    uart.o(i.UART_SendStr) refers to uart.o(i.UART_SendData) for UART_SendData
    uart.o(i.UART_TxdIsr) refers to uart.o(.data) for TxdFifoDataRear
    uart.o(i.UART_TxdIsr) refers to uart.o(.bss) for TxdFifoData
    xflash.o(i.XFLASH_GetDataFromUart) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    xflash.o(i.XFLASH_GetDataFromUart) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    xflash.o(i.XFLASH_GetDataFromUart) refers to _printf_dec.o(.text) for _printf_int_dec
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_SelectFont) for LCD_SelectFont
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_PutStrLeftTop) for LCD_PutStrLeftTop
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.DelayMs) for DelayMs
    xflash.o(i.XFLASH_GetDataFromUart) refers to noretval__2sprintf.o(.text) for __2sprintf
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.DelayUs) for DelayUs
    xflash.o(i.XFLASH_GetDataFromUart) refers to common.o(i.LrcCalc) for LrcCalc
    xflash.o(i.XFLASH_GetDataFromUart) refers to uart.o(i.UART_SendData) for UART_SendData
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(i.XFLASH_WriteData) for XFLASH_WriteData
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    xflash.o(i.XFLASH_GetDataFromUart) refers to common.o(i.Num2Str) for Num2Str
    xflash.o(i.XFLASH_GetDataFromUart) refers to lcd.o(i.LCD_DrawProgress) for LCD_DrawProgress
    xflash.o(i.XFLASH_GetDataFromUart) refers to font.o(.constdata) for FONT_32
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(.data) for UartRxdFlag
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(.bss) for UartRxdDataBuffer1
    xflash.o(i.XFLASH_GetDataFromUart) refers to driver.o(.data) for Uart2RxdIsr
    xflash.o(i.XFLASH_GetDataFromUart) refers to xflash.o(i.XFLASH_UartRxdIsr) for XFLASH_UartRxdIsr
    xflash.o(i.XFLASH_GetDataFromUart) refers to uart.o(.data) for UART_BdRate
    xflash.o(i.XFLASH_UartRxdIsr) refers to xflash.o(.data) for UartRxdFlag
    xflash.o(i.XFLASH_UartRxdIsr) refers to xflash.o(.bss) for UartRxdDataBuffer1
    xflash.o(i.XFLASH_WriteData) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    xflash.o(i.XFLASH_WriteData) refers to driver.o(i.SPI_FLASH_EraseSector) for SPI_FLASH_EraseSector
    xflash.o(i.XFLASH_WriteData) refers to driver.o(i.SPI_FLASH_WriteSector) for SPI_FLASH_WriteSector
    xflash.o(i.XFLASH_WriteData) refers to xflash.o(.bss) for XFLASH_TempBuffer
    xfont.o(i.XFONT_GetFontInf) refers to driver.o(i.SPI_FLASH_ReadData) for SPI_FLASH_ReadData
    xfont.o(i.XFONT_GetFontInf) refers to xfont.o(.bss) for XFONT_Inf
    xfont.o(i.XFONT_GetFontInf) refers to xfont.o(.data) for XFONT_FontNum
    xfont.o(i.XFONT_Init) refers to xfont.o(i.XFONT_GetFontInf) for XFONT_GetFontInf
    xfont.o(i.XFONT_Init) refers to xfont.o(i.XFONT_SeleFont) for XFONT_SeleFont
    xfont.o(i.XFONT_SeleFont) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    xfont.o(i.XFONT_SeleFont) refers to lcd.o(.data) for FontExternalFlag
    xfont.o(i.XFONT_SeleFont) refers to xfont.o(.bss) for XFONT_Inf
    xfont.o(i.XFONT_SeleFont) refers to xfont.o(.data) for FontDataLenAscii
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.__pow$lsc) for __pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.__pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.__pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.__pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.__pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.__pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.__pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.__pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.__pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.__pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.__pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dsqrt_noumaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_noumaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to stm32f10x_vector.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing cortexm3_macro.o(.text), (122 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (96 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (4 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (16 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (16 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (184 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (196 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (112 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_BASEPRICONFIG), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_ClearIRQChannelPendingBit), (24 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_ClearSystemHandlerPendingBit), (32 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_DeInit), (68 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GenerateCoreReset), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GenerateSystemReset), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetBASEPRI), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetCPUID), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetCurrentActiveHandler), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetCurrentPendingIRQChannel), (16 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetFaultAddress), (32 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetFaultHandlerSources), (68 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetIRQChannelActiveBitStatus), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetIRQChannelPendingBitStatus), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetSystemHandlerActiveBitStatus), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_GetSystemHandlerPendingBitStatus), (48 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_Init), (164 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_RESETFAULTMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_RESETPRIMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SCBDeInit), (84 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SETFAULTMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SETPRIMASK), (8 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SetIRQChannelPendingBit), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SetSystemHandlerPendingBit), (28 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_StructInit), (12 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SystemHandlerConfig), (44 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SystemHandlerPriorityConfig), (112 bytes).
    Removing stm32f10x_nvic.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetClocksFreq), (212 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (76 bytes).
    Removing stm32f10x_rcc.o(.constdata), (20 bytes).
    Removing stm32f10x_rcc.o(.data), (8 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (210 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (34 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (48 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (92 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (56 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_Cmd), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (224 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (110 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (120 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (156 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (116 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (108 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (14 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseInit), (60 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_Cmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (76 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (64 bytes).
    Removing stm32f10x_usart.o(i.USART_Init), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (160 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (80 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (68 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (42 bytes).
    Removing stm32f10x_flash.o(i.delay), (26 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_it.o(i.EXTI15_10_IRQHandler), (20 bytes).
    Removing stm32f10x_it.o(i.TIM1_UP_IRQHandler), (20 bytes).
    Removing stm32f10x_it.o(i.TIM5_IRQHandler), (24 bytes).
    Removing stm32f10x_it.o(i.USART2_IRQHandler), (20 bytes).
    Removing driver.o(i.AdcInit), (536 bytes).
    Removing driver.o(i.DacInit), (104 bytes).
    Removing driver.o(i.DelayHalfUs), (74 bytes).
    Removing driver.o(i.DelayUs), (72 bytes).
    Removing driver.o(i.ILI9481Init), (1324 bytes).
    Removing driver.o(i.ILI9481ReadRamPrepare), (76 bytes).
    Removing driver.o(i.ILI9481SetDisplayWindow), (292 bytes).
    Removing driver.o(i.ILI9481WriteRamPrepare), (76 bytes).
    Removing driver.o(i.IrInit), (204 bytes).
    Removing driver.o(i.LCD_BackLightInit), (212 bytes).
    Removing driver.o(i.LCD_InitGpio), (248 bytes).
    Removing driver.o(i.LCD_SetBright), (28 bytes).
    Removing driver.o(i.LCD_WriteArray), (120 bytes).
    Removing driver.o(i.LCD_WriteArrayFromXflash), (164 bytes).
    Removing driver.o(i.LCD_WriteConst), (88 bytes).
    Removing driver.o(i.PwmInit), (560 bytes).
    Removing driver.o(i.SPI_FLASH_EraseSector), (184 bytes).
    Removing driver.o(i.SPI_FLASH_Init), (168 bytes).
    Removing driver.o(i.SPI_FLASH_ReadData), (224 bytes).
    Removing driver.o(i.SPI_FLASH_ReadId), (420 bytes).
    Removing driver.o(i.SPI_FLASH_WriteSector), (364 bytes).
    Removing driver.o(i.Spi3Init), (228 bytes).
    Removing driver.o(i.TASK_TimerInit), (144 bytes).
    Removing driver.o(i.TP_Init), (12 bytes).
    Removing driver.o(i.TP_TouchScan), (12 bytes).
    Removing driver.o(i.Uart2Init), (192 bytes).
    Removing driver.o(i.WatchDogInit), (54 bytes).
    Removing driver.o(.bss), (20 bytes).
    Removing cmd.o(i.CMD_Init), (52 bytes).
    Removing cmd.o(i.CMD_MainTask), (320 bytes).
    Removing cmd.o(i.CMD_PackCheck), (52 bytes).
    Removing cmd.o(i.CMD_PackRun), (120 bytes).
    Removing cmd.o(.bss), (54 bytes).
    Removing cmd.o(.data), (10 bytes).
    Removing common.o(i.IntPower), (28 bytes).
    Removing common.o(i.LrcCalc), (28 bytes).
    Removing common.o(i.Num2Str), (350 bytes).
    Removing common.o(i.Str2Double), (236 bytes).
    Removing common.o(i.Str2Num), (204 bytes).
    Removing common.o(i.StrLen), (26 bytes).
    Removing font.o(.constdata), (10776 bytes).
    Removing ir.o(i.IR_Decode), (896 bytes).
    Removing ir.o(i.IR_Init), (40 bytes).
    Removing ir.o(.data), (16 bytes).
    Removing lcd.o(i.LCD_Clear), (38 bytes).
    Removing lcd.o(i.LCD_DrawBmpFromRawData), (124 bytes).
    Removing lcd.o(i.LCD_DrawBmpFromXflashPackData), (528 bytes).
    Removing lcd.o(i.LCD_DrawChildPic), (40 bytes).
    Removing lcd.o(i.LCD_DrawLine), (508 bytes).
    Removing lcd.o(i.LCD_DrawPage), (26 bytes).
    Removing lcd.o(i.LCD_DrawPageCenter), (24 bytes).
    Removing lcd.o(i.LCD_DrawPoint), (100 bytes).
    Removing lcd.o(i.LCD_DrawProgress), (328 bytes).
    Removing lcd.o(i.LCD_GetBar), (6 bytes).
    Removing lcd.o(i.LCD_Init), (52 bytes).
    Removing lcd.o(i.LCD_PutChar), (204 bytes).
    Removing lcd.o(i.LCD_PutHanzi), (204 bytes).
    Removing lcd.o(i.LCD_PutStr), (304 bytes).
    Removing lcd.o(i.LCD_PutStrCenter), (108 bytes).
    Removing lcd.o(i.LCD_PutStrLeftTop), (54 bytes).
    Removing lcd.o(i.LCD_PutStrRightCenter), (108 bytes).
    Removing lcd.o(i.LCD_ReadPoint), (6 bytes).
    Removing lcd.o(i.LCD_SearchFont), (468 bytes).
    Removing lcd.o(i.LCD_SelectFont), (148 bytes).
    Removing lcd.o(i.LCD_SetBar), (82 bytes).
    Removing lcd.o(i.LCD_SetCursor), (20 bytes).
    Removing lcd.o(.data), (20 bytes).
    Removing touch.o(i.TOUCH_DispPos), (488 bytes).
    Removing touch.o(i.TOUCH_DrawLine), (136 bytes).
    Removing touch.o(i.TOUCH_GetIdByPoint), (188 bytes).
    Removing touch.o(i.TOUCH_GetState), (208 bytes).
    Removing touch.o(.bss), (516 bytes).
    Removing touch.o(.data), (8 bytes).
    Removing uart.o(i.UART_ClearRxdBuffer), (28 bytes).
    Removing uart.o(i.UART_GetRxdData), (84 bytes).
    Removing uart.o(i.UART_GetRxdFifoLen), (52 bytes).
    Removing uart.o(i.UART_Init), (112 bytes).
    Removing uart.o(i.UART_RxdIsr), (64 bytes).
    Removing uart.o(i.UART_SendData), (136 bytes).
    Removing uart.o(i.UART_SendStr), (36 bytes).
    Removing uart.o(i.UART_TxdIsr), (80 bytes).
    Removing uart.o(.bss), (3020 bytes).
    Removing uart.o(.data), (18 bytes).
    Removing xflash.o(i.XFLASH_GetDataFromUart), (1476 bytes).
    Removing xflash.o(i.XFLASH_UartRxdIsr), (264 bytes).
    Removing xflash.o(i.XFLASH_WriteData), (352 bytes).
    Removing xflash.o(.bss), (24840 bytes).
    Removing xflash.o(.data), (9 bytes).
    Removing xfont.o(i.XFONT_GetFontInf), (72 bytes).
    Removing xfont.o(i.XFONT_Init), (14 bytes).
    Removing xfont.o(i.XFONT_SeleFont), (216 bytes).
    Removing xfont.o(.bss), (580 bytes).
    Removing xfont.o(.data), (16 bytes).
    Removing driver.o(i.SPI_FLASH_WaitBusy), (92 bytes).

400 unused section(s) (total 66749 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_noumaal.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\qiankunTeamLib\api\cmd.c              0x00000000   Number         0  cmd.o ABSOLUTE
    ..\qiankunTeamLib\api\common.c           0x00000000   Number         0  common.o ABSOLUTE
    ..\qiankunTeamLib\api\font.c             0x00000000   Number         0  font.o ABSOLUTE
    ..\qiankunTeamLib\api\ir.c               0x00000000   Number         0  ir.o ABSOLUTE
    ..\qiankunTeamLib\api\lcd.c              0x00000000   Number         0  lcd.o ABSOLUTE
    ..\qiankunTeamLib\api\touch.c            0x00000000   Number         0  touch.o ABSOLUTE
    ..\qiankunTeamLib\api\uart.c             0x00000000   Number         0  uart.o ABSOLUTE
    ..\qiankunTeamLib\api\xflash.c           0x00000000   Number         0  xflash.o ABSOLUTE
    ..\qiankunTeamLib\api\xfont.c            0x00000000   Number         0  xfont.o ABSOLUTE
    ..\qiankunTeamLib\driver\driver.c        0x00000000   Number         0  driver.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_adc.c  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_dac.c  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_exti.c 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_iwdg.c 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_nvic.c 0x00000000   Number         0  stm32f10x_nvic.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_pwr.c  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_rcc.c  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_spi.c  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_tim.c  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Lib\FWLib_STM32F10x\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Startup\cortexm3_macro.s                 0x00000000   Number         0  cortexm3_macro.o ABSOLUTE
    Startup\stm32f10x_vector.s               0x00000000   Number         0  stm32f10x_vector.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      304  stm32f10x_vector.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001a6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001a8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x080001aa   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x080001ac   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001ac   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001ac   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001b2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001b2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001b6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001be   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001c0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001c0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001c4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001cc   Section       44  stm32f10x_vector.o(.text)
    .text                                    0x080001f8   Section        0  heapauxi.o(.text)
    .text                                    0x08000200   Section        8  libspace.o(.text)
    .text                                    0x08000208   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000252   Section        0  exit.o(.text)
    .text                                    0x08000260   Section        0  sys_exit.o(.text)
    .text                                    0x0800026c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800026e   Section        0  indicate_semi.o(.text)
    i.ADC1_2_IRQHandler                      0x08000270   Section        0  stm32f10x_it.o(i.ADC1_2_IRQHandler)
    i.ADC3_IRQHandler                        0x08000284   Section        0  stm32f10x_it.o(i.ADC3_IRQHandler)
    i.BusFaultException                      0x08000298   Section        0  stm32f10x_it.o(i.BusFaultException)
    i.CAN_RX1_IRQHandler                     0x080002ac   Section        0  stm32f10x_it.o(i.CAN_RX1_IRQHandler)
    i.CAN_SCE_IRQHandler                     0x080002c0   Section        0  stm32f10x_it.o(i.CAN_SCE_IRQHandler)
    i.CloclkInit                             0x080002d4   Section        0  driver.o(i.CloclkInit)
    i.CommonConfig                           0x08000378   Section        0  driver.o(i.CommonConfig)
    i.DMA1_Channel1_IRQHandler               0x08000384   Section        0  stm32f10x_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA1_Channel2_IRQHandler               0x08000398   Section        0  stm32f10x_it.o(i.DMA1_Channel2_IRQHandler)
    i.DMA1_Channel3_IRQHandler               0x080003ac   Section        0  stm32f10x_it.o(i.DMA1_Channel3_IRQHandler)
    i.DMA1_Channel4_IRQHandler               0x080003c0   Section        0  stm32f10x_it.o(i.DMA1_Channel4_IRQHandler)
    i.DMA1_Channel5_IRQHandler               0x080003d4   Section        0  stm32f10x_it.o(i.DMA1_Channel5_IRQHandler)
    i.DMA1_Channel6_IRQHandler               0x080003e8   Section        0  stm32f10x_it.o(i.DMA1_Channel6_IRQHandler)
    i.DMA1_Channel7_IRQHandler               0x080003fc   Section        0  stm32f10x_it.o(i.DMA1_Channel7_IRQHandler)
    i.DMA2_Channel1_IRQHandler               0x08000410   Section        0  stm32f10x_it.o(i.DMA2_Channel1_IRQHandler)
    i.DMA2_Channel2_IRQHandler               0x08000428   Section        0  stm32f10x_it.o(i.DMA2_Channel2_IRQHandler)
    i.DMA2_Channel3_IRQHandler               0x08000440   Section        0  stm32f10x_it.o(i.DMA2_Channel3_IRQHandler)
    i.DMA2_Channel4_5_IRQHandler             0x08000458   Section        0  stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler)
    i.DebugMonitor                           0x08000470   Section        0  stm32f10x_it.o(i.DebugMonitor)
    i.DelayInit                              0x08000484   Section        0  driver.o(i.DelayInit)
    i.DelayMs                                0x080004c8   Section        0  driver.o(i.DelayMs)
    i.EXTI0_IRQHandler                       0x0800050c   Section        0  stm32f10x_it.o(i.EXTI0_IRQHandler)
    i.EXTI15_10_IRQHandler                   0x08000520   Section        0  driver.o(i.EXTI15_10_IRQHandler)
    i.EXTI1_IRQHandler                       0x0800055c   Section        0  stm32f10x_it.o(i.EXTI1_IRQHandler)
    i.EXTI2_IRQHandler                       0x08000570   Section        0  stm32f10x_it.o(i.EXTI2_IRQHandler)
    i.EXTI3_IRQHandler                       0x08000584   Section        0  stm32f10x_it.o(i.EXTI3_IRQHandler)
    i.EXTI4_IRQHandler                       0x08000598   Section        0  stm32f10x_it.o(i.EXTI4_IRQHandler)
    i.EXTI9_5_IRQHandler                     0x080005ac   Section        0  stm32f10x_it.o(i.EXTI9_5_IRQHandler)
    i.EXTI_GetITStatus                       0x080005c0   Section        0  stm32f10x_exti.o(i.EXTI_GetITStatus)
    i.FLASH_IRQHandler                       0x080005e8   Section        0  stm32f10x_it.o(i.FLASH_IRQHandler)
    i.FLASH_PrefetchBufferCmd                0x080005fc   Section        0  stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd)
    i.FLASH_SetLatency                       0x08000618   Section        0  stm32f10x_flash.o(i.FLASH_SetLatency)
    i.FSMC_IRQHandler                        0x08000634   Section        0  stm32f10x_it.o(i.FSMC_IRQHandler)
    i.GPIO_Init                              0x0800064c   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.HardFaultException                     0x08000760   Section        0  stm32f10x_it.o(i.HardFaultException)
    i.I2C1_ER_IRQHandler                     0x08000774   Section        0  stm32f10x_it.o(i.I2C1_ER_IRQHandler)
    i.I2C1_EV_IRQHandler                     0x08000788   Section        0  stm32f10x_it.o(i.I2C1_EV_IRQHandler)
    i.I2C2_ER_IRQHandler                     0x0800079c   Section        0  stm32f10x_it.o(i.I2C2_ER_IRQHandler)
    i.I2C2_EV_IRQHandler                     0x080007b0   Section        0  stm32f10x_it.o(i.I2C2_EV_IRQHandler)
    i.LedInit                                0x080007c4   Section        0  driver.o(i.LedInit)
    i.MemManageException                     0x080007fc   Section        0  stm32f10x_it.o(i.MemManageException)
    i.NMIException                           0x08000810   Section        0  stm32f10x_it.o(i.NMIException)
    i.NVIC_PriorityGroupConfig               0x08000824   Section        0  stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig)
    i.PVD_IRQHandler                         0x08000838   Section        0  stm32f10x_it.o(i.PVD_IRQHandler)
    i.PendSVC                                0x0800084c   Section        0  stm32f10x_it.o(i.PendSVC)
    i.RCC_APB2PeriphClockCmd                 0x08000860   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_DeInit                             0x08000880   Section        0  stm32f10x_rcc.o(i.RCC_DeInit)
    i.RCC_GetFlagStatus                      0x080008cc   Section        0  stm32f10x_rcc.o(i.RCC_GetFlagStatus)
    i.RCC_GetSYSCLKSource                    0x08000908   Section        0  stm32f10x_rcc.o(i.RCC_GetSYSCLKSource)
    i.RCC_HCLKConfig                         0x08000918   Section        0  stm32f10x_rcc.o(i.RCC_HCLKConfig)
    i.RCC_HSEConfig                          0x08000930   Section        0  stm32f10x_rcc.o(i.RCC_HSEConfig)
    i.RCC_IRQHandler                         0x0800097c   Section        0  stm32f10x_it.o(i.RCC_IRQHandler)
    i.RCC_PCLK1Config                        0x08000990   Section        0  stm32f10x_rcc.o(i.RCC_PCLK1Config)
    i.RCC_PCLK2Config                        0x080009a8   Section        0  stm32f10x_rcc.o(i.RCC_PCLK2Config)
    i.RCC_PLLCmd                             0x080009c0   Section        0  stm32f10x_rcc.o(i.RCC_PLLCmd)
    i.RCC_PLLConfig                          0x080009cc   Section        0  stm32f10x_rcc.o(i.RCC_PLLConfig)
    i.RCC_SYSCLKConfig                       0x080009e8   Section        0  stm32f10x_rcc.o(i.RCC_SYSCLKConfig)
    i.RTCAlarm_IRQHandler                    0x08000a00   Section        0  stm32f10x_it.o(i.RTCAlarm_IRQHandler)
    i.RTC_IRQHandler                         0x08000a14   Section        0  stm32f10x_it.o(i.RTC_IRQHandler)
    i.SDIO_IRQHandler                        0x08000a28   Section        0  stm32f10x_it.o(i.SDIO_IRQHandler)
    i.SPI1_IRQHandler                        0x08000a40   Section        0  stm32f10x_it.o(i.SPI1_IRQHandler)
    i.SPI2_IRQHandler                        0x08000a54   Section        0  stm32f10x_it.o(i.SPI2_IRQHandler)
    i.SPI3_IRQHandler                        0x08000a68   Section        0  stm32f10x_it.o(i.SPI3_IRQHandler)
    i.SVCHandler                             0x08000a80   Section        0  stm32f10x_it.o(i.SVCHandler)
    i.SysTickHandler                         0x08000a94   Section        0  stm32f10x_it.o(i.SysTickHandler)
    i.SystemInit                             0x08000aa8   Section        0  main.o(i.SystemInit)
    i.TAMPER_IRQHandler                      0x08000ad0   Section        0  stm32f10x_it.o(i.TAMPER_IRQHandler)
    i.TIM1_BRK_IRQHandler                    0x08000ae4   Section        0  stm32f10x_it.o(i.TIM1_BRK_IRQHandler)
    i.TIM1_CC_IRQHandler                     0x08000af8   Section        0  stm32f10x_it.o(i.TIM1_CC_IRQHandler)
    i.TIM1_TRG_COM_IRQHandler                0x08000b0c   Section        0  stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler)
    i.TIM1_UP_IRQHandler                     0x08000b20   Section        0  driver.o(i.TIM1_UP_IRQHandler)
    i.TIM2_IRQHandler                        0x08000b40   Section        0  stm32f10x_it.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x08000b54   Section        0  stm32f10x_it.o(i.TIM3_IRQHandler)
    i.TIM4_IRQHandler                        0x08000b68   Section        0  stm32f10x_it.o(i.TIM4_IRQHandler)
    i.TIM5_IRQHandler                        0x08000b7c   Section        0  driver.o(i.TIM5_IRQHandler)
    i.TIM6_IRQHandler                        0x08000bb0   Section        0  stm32f10x_it.o(i.TIM6_IRQHandler)
    i.TIM7_IRQHandler                        0x08000bc8   Section        0  stm32f10x_it.o(i.TIM7_IRQHandler)
    i.TIM8_BRK_IRQHandler                    0x08000be0   Section        0  stm32f10x_it.o(i.TIM8_BRK_IRQHandler)
    i.TIM8_CC_IRQHandler                     0x08000bf4   Section        0  stm32f10x_it.o(i.TIM8_CC_IRQHandler)
    i.TIM8_TRG_COM_IRQHandler                0x08000c08   Section        0  stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler)
    i.TIM8_UP_IRQHandler                     0x08000c1c   Section        0  stm32f10x_it.o(i.TIM8_UP_IRQHandler)
    i.UART4_IRQHandler                       0x08000c30   Section        0  stm32f10x_it.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x08000c48   Section        0  stm32f10x_it.o(i.UART5_IRQHandler)
    i.USART1_IRQHandler                      0x08000c60   Section        0  stm32f10x_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08000c74   Section        0  driver.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08000ce0   Section        0  stm32f10x_it.o(i.USART3_IRQHandler)
    i.USBWakeUp_IRQHandler                   0x08000cf4   Section        0  stm32f10x_it.o(i.USBWakeUp_IRQHandler)
    i.USB_HP_CAN_TX_IRQHandler               0x08000d08   Section        0  stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler)
    i.USB_LP_CAN_RX0_IRQHandler              0x08000d1c   Section        0  stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler)
    i.UsageFaultException                    0x08000d30   Section        0  stm32f10x_it.o(i.UsageFaultException)
    i.WWDG_IRQHandler                        0x08000d44   Section        0  stm32f10x_it.o(i.WWDG_IRQHandler)
    i.main                                   0x08000d58   Section        0  main.o(i.main)
    .data                                    0x20000000   Section        4  main.o(.data)
    .data                                    0x20000004   Section       34  driver.o(.data)
    fac_us                                   0x20000004   Data           1  driver.o(.data)
    fac_ms                                   0x20000008   Data           4  driver.o(.data)
    .bss                                     0x20000028   Section       96  libspace.o(.bss)
    HEAP                                     0x20000088   Section     4096  stm32f10x_vector.o(HEAP)
    Heap_Mem                                 0x20000088   Data        4096  stm32f10x_vector.o(HEAP)
    STACK                                    0x20001088   Section     4096  stm32f10x_vector.o(STACK)
    Stack_Mem                                0x20001088   Data        4096  stm32f10x_vector.o(STACK)
    __initial_sp                             0x20002088   Data           0  stm32f10x_vector.o(STACK)
    .ARM.__AT_0x2000FFFC                     0x2000fffc   Section        4  main.o(.ARM.__AT_0x2000FFFC)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors                                0x08000000   Data           4  stm32f10x_vector.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001a9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x080001ad   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001ad   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001ad   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001bf   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001c5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001cd   Thumb Code     8  stm32f10x_vector.o(.text)
    __user_initial_stackheap                 0x080001d5   Thumb Code     0  stm32f10x_vector.o(.text)
    __use_two_region_memory                  0x080001f9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080001fb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080001fd   Thumb Code     2  heapauxi.o(.text)
    __user_libspace                          0x08000201   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000201   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000201   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000209   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000253   Thumb Code    12  exit.o(.text)
    _sys_exit                                0x08000261   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0800026d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800026d   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0800026f   Thumb Code     0  indicate_semi.o(.text)
    ADC1_2_IRQHandler                        0x08000271   Thumb Code    16  stm32f10x_it.o(i.ADC1_2_IRQHandler)
    ADC3_IRQHandler                          0x08000285   Thumb Code    16  stm32f10x_it.o(i.ADC3_IRQHandler)
    BusFaultException                        0x08000299   Thumb Code    16  stm32f10x_it.o(i.BusFaultException)
    CAN_RX1_IRQHandler                       0x080002ad   Thumb Code    16  stm32f10x_it.o(i.CAN_RX1_IRQHandler)
    CAN_SCE_IRQHandler                       0x080002c1   Thumb Code    16  stm32f10x_it.o(i.CAN_SCE_IRQHandler)
    CloclkInit                               0x080002d5   Thumb Code   150  driver.o(i.CloclkInit)
    CommonConfig                             0x08000379   Thumb Code    12  driver.o(i.CommonConfig)
    DMA1_Channel1_IRQHandler                 0x08000385   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_IRQHandler                 0x08000399   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel2_IRQHandler)
    DMA1_Channel3_IRQHandler                 0x080003ad   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel3_IRQHandler)
    DMA1_Channel4_IRQHandler                 0x080003c1   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel4_IRQHandler)
    DMA1_Channel5_IRQHandler                 0x080003d5   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel5_IRQHandler)
    DMA1_Channel6_IRQHandler                 0x080003e9   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel6_IRQHandler)
    DMA1_Channel7_IRQHandler                 0x080003fd   Thumb Code    16  stm32f10x_it.o(i.DMA1_Channel7_IRQHandler)
    DMA2_Channel1_IRQHandler                 0x08000411   Thumb Code    18  stm32f10x_it.o(i.DMA2_Channel1_IRQHandler)
    DMA2_Channel2_IRQHandler                 0x08000429   Thumb Code    18  stm32f10x_it.o(i.DMA2_Channel2_IRQHandler)
    DMA2_Channel3_IRQHandler                 0x08000441   Thumb Code    18  stm32f10x_it.o(i.DMA2_Channel3_IRQHandler)
    DMA2_Channel4_5_IRQHandler               0x08000459   Thumb Code    18  stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler)
    DebugMonitor                             0x08000471   Thumb Code    16  stm32f10x_it.o(i.DebugMonitor)
    DelayInit                                0x08000485   Thumb Code    52  driver.o(i.DelayInit)
    DelayMs                                  0x080004c9   Thumb Code    62  driver.o(i.DelayMs)
    EXTI0_IRQHandler                         0x0800050d   Thumb Code    16  stm32f10x_it.o(i.EXTI0_IRQHandler)
    EXTI15_10_IRQHandler                     0x08000521   Thumb Code    48  driver.o(i.EXTI15_10_IRQHandler)
    EXTI1_IRQHandler                         0x0800055d   Thumb Code    16  stm32f10x_it.o(i.EXTI1_IRQHandler)
    EXTI2_IRQHandler                         0x08000571   Thumb Code    16  stm32f10x_it.o(i.EXTI2_IRQHandler)
    EXTI3_IRQHandler                         0x08000585   Thumb Code    16  stm32f10x_it.o(i.EXTI3_IRQHandler)
    EXTI4_IRQHandler                         0x08000599   Thumb Code    16  stm32f10x_it.o(i.EXTI4_IRQHandler)
    EXTI9_5_IRQHandler                       0x080005ad   Thumb Code    16  stm32f10x_it.o(i.EXTI9_5_IRQHandler)
    EXTI_GetITStatus                         0x080005c1   Thumb Code    34  stm32f10x_exti.o(i.EXTI_GetITStatus)
    FLASH_IRQHandler                         0x080005e9   Thumb Code    16  stm32f10x_it.o(i.FLASH_IRQHandler)
    FLASH_PrefetchBufferCmd                  0x080005fd   Thumb Code    22  stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd)
    FLASH_SetLatency                         0x08000619   Thumb Code    22  stm32f10x_flash.o(i.FLASH_SetLatency)
    FSMC_IRQHandler                          0x08000635   Thumb Code    18  stm32f10x_it.o(i.FSMC_IRQHandler)
    GPIO_Init                                0x0800064d   Thumb Code   276  stm32f10x_gpio.o(i.GPIO_Init)
    HardFaultException                       0x08000761   Thumb Code    16  stm32f10x_it.o(i.HardFaultException)
    I2C1_ER_IRQHandler                       0x08000775   Thumb Code    16  stm32f10x_it.o(i.I2C1_ER_IRQHandler)
    I2C1_EV_IRQHandler                       0x08000789   Thumb Code    16  stm32f10x_it.o(i.I2C1_EV_IRQHandler)
    I2C2_ER_IRQHandler                       0x0800079d   Thumb Code    16  stm32f10x_it.o(i.I2C2_ER_IRQHandler)
    I2C2_EV_IRQHandler                       0x080007b1   Thumb Code    16  stm32f10x_it.o(i.I2C2_EV_IRQHandler)
    LedInit                                  0x080007c5   Thumb Code    52  driver.o(i.LedInit)
    MemManageException                       0x080007fd   Thumb Code    16  stm32f10x_it.o(i.MemManageException)
    NMIException                             0x08000811   Thumb Code    16  stm32f10x_it.o(i.NMIException)
    NVIC_PriorityGroupConfig                 0x08000825   Thumb Code    10  stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig)
    PVD_IRQHandler                           0x08000839   Thumb Code    16  stm32f10x_it.o(i.PVD_IRQHandler)
    PendSVC                                  0x0800084d   Thumb Code    16  stm32f10x_it.o(i.PendSVC)
    RCC_APB2PeriphClockCmd                   0x08000861   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_DeInit                               0x08000881   Thumb Code    62  stm32f10x_rcc.o(i.RCC_DeInit)
    RCC_GetFlagStatus                        0x080008cd   Thumb Code    56  stm32f10x_rcc.o(i.RCC_GetFlagStatus)
    RCC_GetSYSCLKSource                      0x08000909   Thumb Code    10  stm32f10x_rcc.o(i.RCC_GetSYSCLKSource)
    RCC_HCLKConfig                           0x08000919   Thumb Code    18  stm32f10x_rcc.o(i.RCC_HCLKConfig)
    RCC_HSEConfig                            0x08000931   Thumb Code    70  stm32f10x_rcc.o(i.RCC_HSEConfig)
    RCC_IRQHandler                           0x0800097d   Thumb Code    16  stm32f10x_it.o(i.RCC_IRQHandler)
    RCC_PCLK1Config                          0x08000991   Thumb Code    18  stm32f10x_rcc.o(i.RCC_PCLK1Config)
    RCC_PCLK2Config                          0x080009a9   Thumb Code    20  stm32f10x_rcc.o(i.RCC_PCLK2Config)
    RCC_PLLCmd                               0x080009c1   Thumb Code     6  stm32f10x_rcc.o(i.RCC_PLLCmd)
    RCC_PLLConfig                            0x080009cd   Thumb Code    24  stm32f10x_rcc.o(i.RCC_PLLConfig)
    RCC_SYSCLKConfig                         0x080009e9   Thumb Code    18  stm32f10x_rcc.o(i.RCC_SYSCLKConfig)
    RTCAlarm_IRQHandler                      0x08000a01   Thumb Code    16  stm32f10x_it.o(i.RTCAlarm_IRQHandler)
    RTC_IRQHandler                           0x08000a15   Thumb Code    16  stm32f10x_it.o(i.RTC_IRQHandler)
    SDIO_IRQHandler                          0x08000a29   Thumb Code    18  stm32f10x_it.o(i.SDIO_IRQHandler)
    SPI1_IRQHandler                          0x08000a41   Thumb Code    16  stm32f10x_it.o(i.SPI1_IRQHandler)
    SPI2_IRQHandler                          0x08000a55   Thumb Code    16  stm32f10x_it.o(i.SPI2_IRQHandler)
    SPI3_IRQHandler                          0x08000a69   Thumb Code    18  stm32f10x_it.o(i.SPI3_IRQHandler)
    SVCHandler                               0x08000a81   Thumb Code    16  stm32f10x_it.o(i.SVCHandler)
    SysTickHandler                           0x08000a95   Thumb Code    16  stm32f10x_it.o(i.SysTickHandler)
    SystemInit                               0x08000aa9   Thumb Code    28  main.o(i.SystemInit)
    TAMPER_IRQHandler                        0x08000ad1   Thumb Code    16  stm32f10x_it.o(i.TAMPER_IRQHandler)
    TIM1_BRK_IRQHandler                      0x08000ae5   Thumb Code    16  stm32f10x_it.o(i.TIM1_BRK_IRQHandler)
    TIM1_CC_IRQHandler                       0x08000af9   Thumb Code    16  stm32f10x_it.o(i.TIM1_CC_IRQHandler)
    TIM1_TRG_COM_IRQHandler                  0x08000b0d   Thumb Code    16  stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler)
    TIM1_UP_IRQHandler                       0x08000b21   Thumb Code    24  driver.o(i.TIM1_UP_IRQHandler)
    TIM2_IRQHandler                          0x08000b41   Thumb Code    16  stm32f10x_it.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x08000b55   Thumb Code    16  stm32f10x_it.o(i.TIM3_IRQHandler)
    TIM4_IRQHandler                          0x08000b69   Thumb Code    16  stm32f10x_it.o(i.TIM4_IRQHandler)
    TIM5_IRQHandler                          0x08000b7d   Thumb Code    38  driver.o(i.TIM5_IRQHandler)
    TIM6_IRQHandler                          0x08000bb1   Thumb Code    18  stm32f10x_it.o(i.TIM6_IRQHandler)
    TIM7_IRQHandler                          0x08000bc9   Thumb Code    18  stm32f10x_it.o(i.TIM7_IRQHandler)
    TIM8_BRK_IRQHandler                      0x08000be1   Thumb Code    16  stm32f10x_it.o(i.TIM8_BRK_IRQHandler)
    TIM8_CC_IRQHandler                       0x08000bf5   Thumb Code    16  stm32f10x_it.o(i.TIM8_CC_IRQHandler)
    TIM8_TRG_COM_IRQHandler                  0x08000c09   Thumb Code    16  stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler)
    TIM8_UP_IRQHandler                       0x08000c1d   Thumb Code    16  stm32f10x_it.o(i.TIM8_UP_IRQHandler)
    UART4_IRQHandler                         0x08000c31   Thumb Code    18  stm32f10x_it.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x08000c49   Thumb Code    18  stm32f10x_it.o(i.UART5_IRQHandler)
    USART1_IRQHandler                        0x08000c61   Thumb Code    16  stm32f10x_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08000c75   Thumb Code    90  driver.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08000ce1   Thumb Code    16  stm32f10x_it.o(i.USART3_IRQHandler)
    USBWakeUp_IRQHandler                     0x08000cf5   Thumb Code    16  stm32f10x_it.o(i.USBWakeUp_IRQHandler)
    USB_HP_CAN_TX_IRQHandler                 0x08000d09   Thumb Code    16  stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler)
    USB_LP_CAN_RX0_IRQHandler                0x08000d1d   Thumb Code    16  stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler)
    UsageFaultException                      0x08000d31   Thumb Code    16  stm32f10x_it.o(i.UsageFaultException)
    WWDG_IRQHandler                          0x08000d45   Thumb Code    16  stm32f10x_it.o(i.WWDG_IRQHandler)
    main                                     0x08000d59   Thumb Code    50  main.o(i.main)
    Region$$Table$$Base                      0x08000d90   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08000db0   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  main.o(.data)
    Uart2RxdIsr                              0x2000000c   Data           4  driver.o(.data)
    Uart2TxdIsr                              0x20000010   Data           4  driver.o(.data)
    IrExtiIsr                                0x20000014   Data           4  driver.o(.data)
    TaskTimerIsr                             0x20000018   Data           4  driver.o(.data)
    ExFlashId                                0x2000001c   Data           8  driver.o(.data)
    TP_TouchNum                              0x20000024   Data           2  driver.o(.data)
    __libspace_start                         0x20000028   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000088   Data           0  libspace.o(.bss)
    SystemRunMode                            0x2000fffc   Data           4  main.o(.ARM.__AT_0x2000FFFC)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00000dd8, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00000db0, Max: 0x00020000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000130   Data   RO            8    RESET               stm32f10x_vector.o
    0x08000130   0x00000008   Code   RO         3252  * !!!main             c_w.l(__main.o)
    0x08000138   0x00000034   Code   RO         3500    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0000001a   Code   RO         3502    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x00000002   PAD
    0x08000188   0x0000001c   Code   RO         3504    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x00000002   Code   RO         3368    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a6   0x00000000   Code   RO         3376    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3378    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3381    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3383    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3385    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3388    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3390    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3392    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3394    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3396    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3398    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3400    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3402    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3404    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3406    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3408    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3412    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3414    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3416    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3418    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001a6   0x00000002   Code   RO         3419    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001a8   0x00000002   Code   RO         3441    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001aa   0x00000000   Code   RO         3455    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO         3458    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO         3461    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO         3463    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO         3466    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001aa   0x00000002   Code   RO         3467    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001ac   0x00000000   Code   RO         3278    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001ac   0x00000000   Code   RO         3329    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001ac   0x00000006   Code   RO         3341    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001b2   0x00000000   Code   RO         3331    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001b2   0x00000004   Code   RO         3332    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001b6   0x00000000   Code   RO         3334    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001b6   0x00000008   Code   RO         3335    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001be   0x00000002   Code   RO         3373    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001c0   0x00000000   Code   RO         3423    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001c0   0x00000004   Code   RO         3424    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001c4   0x00000006   Code   RO         3425    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001ca   0x00000002   PAD
    0x080001cc   0x0000002c   Code   RO            9    .text               stm32f10x_vector.o
    0x080001f8   0x00000006   Code   RO         3250    .text               c_w.l(heapauxi.o)
    0x080001fe   0x00000002   PAD
    0x08000200   0x00000008   Code   RO         3356    .text               c_w.l(libspace.o)
    0x08000208   0x0000004a   Code   RO         3359    .text               c_w.l(sys_stackheap_outer.o)
    0x08000252   0x0000000c   Code   RO         3361    .text               c_w.l(exit.o)
    0x0800025e   0x00000002   PAD
    0x08000260   0x0000000c   Code   RO         3433    .text               c_w.l(sys_exit.o)
    0x0800026c   0x00000002   Code   RO         3444    .text               c_w.l(use_no_semi.o)
    0x0800026e   0x00000000   Code   RO         3446    .text               c_w.l(indicate_semi.o)
    0x0800026e   0x00000002   PAD
    0x08000270   0x00000014   Code   RO         2098    i.ADC1_2_IRQHandler  stm32f10x_it.o
    0x08000284   0x00000014   Code   RO         2099    i.ADC3_IRQHandler   stm32f10x_it.o
    0x08000298   0x00000014   Code   RO         2100    i.BusFaultException  stm32f10x_it.o
    0x080002ac   0x00000014   Code   RO         2101    i.CAN_RX1_IRQHandler  stm32f10x_it.o
    0x080002c0   0x00000014   Code   RO         2102    i.CAN_SCE_IRQHandler  stm32f10x_it.o
    0x080002d4   0x000000a4   Code   RO         2526    i.CloclkInit        driver.o
    0x08000378   0x0000000c   Code   RO         2527    i.CommonConfig      driver.o
    0x08000384   0x00000014   Code   RO         2103    i.DMA1_Channel1_IRQHandler  stm32f10x_it.o
    0x08000398   0x00000014   Code   RO         2104    i.DMA1_Channel2_IRQHandler  stm32f10x_it.o
    0x080003ac   0x00000014   Code   RO         2105    i.DMA1_Channel3_IRQHandler  stm32f10x_it.o
    0x080003c0   0x00000014   Code   RO         2106    i.DMA1_Channel4_IRQHandler  stm32f10x_it.o
    0x080003d4   0x00000014   Code   RO         2107    i.DMA1_Channel5_IRQHandler  stm32f10x_it.o
    0x080003e8   0x00000014   Code   RO         2108    i.DMA1_Channel6_IRQHandler  stm32f10x_it.o
    0x080003fc   0x00000014   Code   RO         2109    i.DMA1_Channel7_IRQHandler  stm32f10x_it.o
    0x08000410   0x00000018   Code   RO         2110    i.DMA2_Channel1_IRQHandler  stm32f10x_it.o
    0x08000428   0x00000018   Code   RO         2111    i.DMA2_Channel2_IRQHandler  stm32f10x_it.o
    0x08000440   0x00000018   Code   RO         2112    i.DMA2_Channel3_IRQHandler  stm32f10x_it.o
    0x08000458   0x00000018   Code   RO         2113    i.DMA2_Channel4_5_IRQHandler  stm32f10x_it.o
    0x08000470   0x00000014   Code   RO         2114    i.DebugMonitor      stm32f10x_it.o
    0x08000484   0x00000044   Code   RO         2530    i.DelayInit         driver.o
    0x080004c8   0x00000044   Code   RO         2531    i.DelayMs           driver.o
    0x0800050c   0x00000014   Code   RO         2115    i.EXTI0_IRQHandler  stm32f10x_it.o
    0x08000520   0x0000003c   Code   RO         2533    i.EXTI15_10_IRQHandler  driver.o
    0x0800055c   0x00000014   Code   RO         2117    i.EXTI1_IRQHandler  stm32f10x_it.o
    0x08000570   0x00000014   Code   RO         2118    i.EXTI2_IRQHandler  stm32f10x_it.o
    0x08000584   0x00000014   Code   RO         2119    i.EXTI3_IRQHandler  stm32f10x_it.o
    0x08000598   0x00000014   Code   RO         2120    i.EXTI4_IRQHandler  stm32f10x_it.o
    0x080005ac   0x00000014   Code   RO         2121    i.EXTI9_5_IRQHandler  stm32f10x_it.o
    0x080005c0   0x00000028   Code   RO          340    i.EXTI_GetITStatus  stm32f10x_exti.o
    0x080005e8   0x00000014   Code   RO         2122    i.FLASH_IRQHandler  stm32f10x_it.o
    0x080005fc   0x0000001c   Code   RO         1839    i.FLASH_PrefetchBufferCmd  stm32f10x_flash.o
    0x08000618   0x0000001c   Code   RO         1844    i.FLASH_SetLatency  stm32f10x_flash.o
    0x08000634   0x00000018   Code   RO         2123    i.FSMC_IRQHandler   stm32f10x_it.o
    0x0800064c   0x00000114   Code   RO          397    i.GPIO_Init         stm32f10x_gpio.o
    0x08000760   0x00000014   Code   RO         2124    i.HardFaultException  stm32f10x_it.o
    0x08000774   0x00000014   Code   RO         2125    i.I2C1_ER_IRQHandler  stm32f10x_it.o
    0x08000788   0x00000014   Code   RO         2126    i.I2C1_EV_IRQHandler  stm32f10x_it.o
    0x0800079c   0x00000014   Code   RO         2127    i.I2C2_ER_IRQHandler  stm32f10x_it.o
    0x080007b0   0x00000014   Code   RO         2128    i.I2C2_EV_IRQHandler  stm32f10x_it.o
    0x080007c4   0x00000038   Code   RO         2545    i.LedInit           driver.o
    0x080007fc   0x00000014   Code   RO         2129    i.MemManageException  stm32f10x_it.o
    0x08000810   0x00000014   Code   RO         2130    i.NMIException      stm32f10x_it.o
    0x08000824   0x00000014   Code   RO          520    i.NVIC_PriorityGroupConfig  stm32f10x_nvic.o
    0x08000838   0x00000014   Code   RO         2131    i.PVD_IRQHandler    stm32f10x_it.o
    0x0800084c   0x00000014   Code   RO         2132    i.PendSVC           stm32f10x_it.o
    0x08000860   0x00000020   Code   RO          759    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000880   0x0000004c   Code   RO          766    i.RCC_DeInit        stm32f10x_rcc.o
    0x080008cc   0x0000003c   Code   RO          768    i.RCC_GetFlagStatus  stm32f10x_rcc.o
    0x08000908   0x00000010   Code   RO          770    i.RCC_GetSYSCLKSource  stm32f10x_rcc.o
    0x08000918   0x00000018   Code   RO          771    i.RCC_HCLKConfig    stm32f10x_rcc.o
    0x08000930   0x0000004c   Code   RO          772    i.RCC_HSEConfig     stm32f10x_rcc.o
    0x0800097c   0x00000014   Code   RO         2133    i.RCC_IRQHandler    stm32f10x_it.o
    0x08000990   0x00000018   Code   RO          778    i.RCC_PCLK1Config   stm32f10x_rcc.o
    0x080009a8   0x00000018   Code   RO          779    i.RCC_PCLK2Config   stm32f10x_rcc.o
    0x080009c0   0x0000000c   Code   RO          780    i.RCC_PLLCmd        stm32f10x_rcc.o
    0x080009cc   0x0000001c   Code   RO          781    i.RCC_PLLConfig     stm32f10x_rcc.o
    0x080009e8   0x00000018   Code   RO          784    i.RCC_SYSCLKConfig  stm32f10x_rcc.o
    0x08000a00   0x00000014   Code   RO         2134    i.RTCAlarm_IRQHandler  stm32f10x_it.o
    0x08000a14   0x00000014   Code   RO         2135    i.RTC_IRQHandler    stm32f10x_it.o
    0x08000a28   0x00000018   Code   RO         2136    i.SDIO_IRQHandler   stm32f10x_it.o
    0x08000a40   0x00000014   Code   RO         2137    i.SPI1_IRQHandler   stm32f10x_it.o
    0x08000a54   0x00000014   Code   RO         2138    i.SPI2_IRQHandler   stm32f10x_it.o
    0x08000a68   0x00000018   Code   RO         2139    i.SPI3_IRQHandler   stm32f10x_it.o
    0x08000a80   0x00000014   Code   RO         2140    i.SVCHandler        stm32f10x_it.o
    0x08000a94   0x00000014   Code   RO         2141    i.SysTickHandler    stm32f10x_it.o
    0x08000aa8   0x00000028   Code   RO         2022    i.SystemInit        main.o
    0x08000ad0   0x00000014   Code   RO         2142    i.TAMPER_IRQHandler  stm32f10x_it.o
    0x08000ae4   0x00000014   Code   RO         2143    i.TIM1_BRK_IRQHandler  stm32f10x_it.o
    0x08000af8   0x00000014   Code   RO         2144    i.TIM1_CC_IRQHandler  stm32f10x_it.o
    0x08000b0c   0x00000014   Code   RO         2145    i.TIM1_TRG_COM_IRQHandler  stm32f10x_it.o
    0x08000b20   0x00000020   Code   RO         2554    i.TIM1_UP_IRQHandler  driver.o
    0x08000b40   0x00000014   Code   RO         2147    i.TIM2_IRQHandler   stm32f10x_it.o
    0x08000b54   0x00000014   Code   RO         2148    i.TIM3_IRQHandler   stm32f10x_it.o
    0x08000b68   0x00000014   Code   RO         2149    i.TIM4_IRQHandler   stm32f10x_it.o
    0x08000b7c   0x00000034   Code   RO         2555    i.TIM5_IRQHandler   driver.o
    0x08000bb0   0x00000018   Code   RO         2151    i.TIM6_IRQHandler   stm32f10x_it.o
    0x08000bc8   0x00000018   Code   RO         2152    i.TIM7_IRQHandler   stm32f10x_it.o
    0x08000be0   0x00000014   Code   RO         2153    i.TIM8_BRK_IRQHandler  stm32f10x_it.o
    0x08000bf4   0x00000014   Code   RO         2154    i.TIM8_CC_IRQHandler  stm32f10x_it.o
    0x08000c08   0x00000014   Code   RO         2155    i.TIM8_TRG_COM_IRQHandler  stm32f10x_it.o
    0x08000c1c   0x00000014   Code   RO         2156    i.TIM8_UP_IRQHandler  stm32f10x_it.o
    0x08000c30   0x00000018   Code   RO         2157    i.UART4_IRQHandler  stm32f10x_it.o
    0x08000c48   0x00000018   Code   RO         2158    i.UART5_IRQHandler  stm32f10x_it.o
    0x08000c60   0x00000014   Code   RO         2159    i.USART1_IRQHandler  stm32f10x_it.o
    0x08000c74   0x0000006c   Code   RO         2558    i.USART2_IRQHandler  driver.o
    0x08000ce0   0x00000014   Code   RO         2161    i.USART3_IRQHandler  stm32f10x_it.o
    0x08000cf4   0x00000014   Code   RO         2162    i.USBWakeUp_IRQHandler  stm32f10x_it.o
    0x08000d08   0x00000014   Code   RO         2163    i.USB_HP_CAN_TX_IRQHandler  stm32f10x_it.o
    0x08000d1c   0x00000014   Code   RO         2164    i.USB_LP_CAN_RX0_IRQHandler  stm32f10x_it.o
    0x08000d30   0x00000014   Code   RO         2165    i.UsageFaultException  stm32f10x_it.o
    0x08000d44   0x00000014   Code   RO         2166    i.WWDG_IRQHandler   stm32f10x_it.o
    0x08000d58   0x00000038   Code   RO         2023    i.main              main.o
    0x08000d90   0x00000020   Data   RO         3498    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00010000, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000004   Data   RW         2025    .data               main.o
    0x20000004   0x00000022   Data   RW         2562    .data               driver.o
    0x20000026   0x00000002   PAD
    0x20000028   0x00000060   Zero   RW         3357    .bss                c_w.l(libspace.o)
    0x20000088   0x00001000   Zero   RW            7    HEAP                stm32f10x_vector.o
    0x20001088   0x00001000   Zero   RW            6    STACK               stm32f10x_vector.o
    0x20002088   0x0000df74   PAD
    0x2000fffc   0x00000004   Zero   RW         2024    .ARM.__AT_0x2000FFFC  main.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0         32   cortexm3_macro.o
       620         92          0         34          0       6695   driver.o
        96         18          0          4          4       1120   main.o
         0          0          0          0          0      16308   stm32f10x_adc.o
        40          6          0          0          0        587   stm32f10x_exti.o
        56         12          0          0          0       1037   stm32f10x_flash.o
       276          0          0          0          0       8108   stm32f10x_gpio.o
      1344        282          0          0          0      29526   stm32f10x_it.o
        20         10          0          0          0       8239   stm32f10x_nvic.o
       396         68          0          0          0       5841   stm32f10x_rcc.o
         0          0          0          0          0      22056   stm32f10x_tim.o
        44         26        304          0       8192        352   stm32f10x_vector.o

    ----------------------------------------------------------------------
      2892        <USER>        <GROUP>         40      65400      99901   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         0          0          0          2      57204          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        12          0          0          0          0         72   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       276         <USER>          <GROUP>          0         96        576   Library Totals
        10          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       266         16          0          0         96        576   c_w.l

    ----------------------------------------------------------------------
       276         <USER>          <GROUP>          0         96        576   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      3168        530        336         40      65496      95741   Grand Totals
      3168        530        336         40      65496      95741   ELF Image Totals
      3168        530        336         40          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 3504 (   3.42kB)
    Total RW  Size (RW Data + ZI Data)             65536 (  64.00kB)
    Total ROM Size (Code + RO Data + RW Data)       3544 (   3.46kB)

==============================================================================

