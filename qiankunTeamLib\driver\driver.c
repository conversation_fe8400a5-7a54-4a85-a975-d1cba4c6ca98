//------------------------------------------------------------------
//-------------------- (C) COPYRIGHT <EMAIL>  -----------------
//
//    File Name		driver.c
//    Description	包含所有与硬件相关的驱动函数和变量
//    Date			2018-10-09
//
//-------------------- (C) COPYRIGHT <EMAIL>  -----------------
//------------------------------------------------------------------
#include "driver.h"
#include "touch.h"
#include "xflash.h"
#include "lcd.h"

// 触摸屏I2C读写命令	
#define TP_CMD_WR 				0X28    	//写命令
#define TP_CMD_RD 				0X29		//读命令
// TP部分寄存器定义 
#define TP_DEVIDE_MODE 			0x00   		//模式控制寄存器
#define TP_REG_NUM_FINGER       0x03		//触摸状态寄存器
#define TP_TP1_REG 				0X03	  	//第一个触摸点数据地址
#define TP_TP2_REG 				0X09		//第二个触摸点数据地址
#define TP_TP3_REG 				0X0F		//第三个触摸点数据地址
#define TP_TP4_REG 				0X15		//第四个触摸点数据地址
#define TP_TP5_REG 				0X1B		//第五个触摸点数据地址  
#define	TP_ID_G_LIB_VERSION		0xA1		//版本		
#define TP_ID_G_MODE 			0xA4   		//中断模式控制寄存器
#define TP_ID_G_THGROUP			0x80   		//触摸有效值设置寄存器
#define TP_ID_G_PERIODACTIVE	0x88   		//激活状态周期设置寄存器  
#define TP_CHIP_VENDOR_ID       0xA3        //芯片ID(0x36)
#define TP_ID_G_FT6236ID		0xA8		//0x11

//--------------------- 全局变量 ------------------------

// 系统运行模式
u32 SystemRunMode __attribute__((at(SYSTEM_RUN_MODE_ADDR))) = 0;// 特别小心：在keil配置Flash时IRAM1的空间大小一定要设置成0xFFF0
// 系统使用的时钟频率
u32 SystemCoreClock = 8000000;

static unsigned char  fac_us = 0;// us延时系数
static unsigned int fac_ms = 0;// ms延时系数
static u8 DelayTimeFlag = 0;
void (*Uart2RxdIsr)(void);// 串口2接收中断函数指针
void (*Uart2TxdIsr)(void);// 串口2发送完成中断函数指针
void (*IrExtiIsr)(void);// 红外遥控中断线中断函数指针
void (*PwmIsr)(void);// PWM中断函数指针
void (*TaskTimerIsr)(void);// 任务定时器中断函数指针
u32 XFlashId[2];
u16 TP_TouchNum;// 当前触摸的点数
POINT TP_TouchPoint[TP_NUM_MAX];
u8 SPI_FLASH_InitFlag;
u16 DbValue = 0;
u32 CpuId[3];// 当前CPU ID
static	float Cal_kx=0,Cal_bx=0,Cal_ky=0,Cal_by=0;
static u8 SPI_InitFlag = 0;

//--------------------- 本地函数 ------------------------
void DelayInit(void);
void SPI_FLASH_ReadId(void);

//------------------------------------------------------------------
//    Name			GetCpuId	
//    Arg			void
//    Return		void
//    Description	获取CPU ID
//    Author		<EMAIL>
//    Date			2018-11-06
//------------------------------------------------------------------
void GetCpuId(void)
{
	u32 m,n,addr;
	m = ~(0x1ffff7e8 - 2018);
	n = 2018;
	addr = ~m + n;
	CpuId[0]=*(vu32*)(addr);
	CpuId[1]=*(vu32*)(addr + 4);
	CpuId[2]=*(vu32*)(addr + 8);
}

//------------------------------------------------------------------
//    Name			CloclkInit	
//    Arg			void
//    Return		void
//    Description	设置时钟
//    Author		<EMAIL>
//    Date			2018-10-09
//------------------------------------------------------------------
void CloclkInit(void)
{
	ErrorStatus HSEStartUpStatus;
	NVIC_InitTypeDef NVIC_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;

	RCC_DeInit();
	// 使用有源晶振，不开启HSE
	RCC_HSEConfig(RCC_HSE_Bypass);
	HSEStartUpStatus = SUCCESS;
	RCC_PLLCmd(DISABLE);
	while(RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == SET);
	if(HSEStartUpStatus == SUCCESS)
	{
		/* Enable Prefetch Buffer */
		FLASH_PrefetchBufferCmd(FLASH_PrefetchBuffer_Enable);
		/* Flash 2 wait state */
		FLASH_SetLatency(FLASH_Latency_2);		
		/* HCLK = SystemCoreClock */
		RCC_HCLKConfig(RCC_SYSCLK_Div1); 		
		/* PCLK2 = HCLK */
		RCC_PCLK2Config(RCC_HCLK_Div1); 		
		/* PCLK1 = HCLK/2 */
		RCC_PCLK1Config(RCC_HCLK_Div2);		
		/* PLLCLK = 8MHz * 9 = 72 MHz */
		RCC_PLLConfig(RCC_PLLSource_HSE_Div2, RCC_PLLMul_9);		
		/* Enable PLL */ 
		RCC_PLLCmd(ENABLE);		
		/* Wait till PLL is ready */
		while(RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET)
		{
		}		
		/* Select PLL as system clock source */
		RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);	
		/* Wait till PLL is used as system clock source */
		while(RCC_GetSYSCLKSource() != 0x08)
		{
		}
		SystemCoreClock = 8000000 * 9;		
	}
	else 
	{
		SystemCoreClock = 8000000;
	}
	// 使能端口时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB | RCC_APB2Periph_GPIOC | RCC_APB2Periph_GPIOD,ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO,ENABLE);
	DelayInit();
}

//------------------------------------------------------------------
//    Name			CommonConfig	
//    Arg			void
//    Return		void
//    Description	通用的设置
//    Author		<EMAIL>
//    Date			2018-10-09
//------------------------------------------------------------------
void CommonConfig(void)
{
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_4);
	// 能用PB3，PB4，PA15做普通IO，PA13&14用于SWD调试
	GPIO_PinRemapConfig(GPIO_Remap_SWJ_JTAGDisable, ENABLE);
	// 获取CPU ID
	GetCpuId();
	// 某些变量初始化
	SPI_InitFlag = 0;
	SPI_FLASH_InitFlag = 0;
}

//------------------------------------------------------------------
//    Name			DelayInit	
//    Arg			void
//    Return		void
//    Description	系统Tick延时初始化
//    Author		<EMAIL>
//    Date			2018-10-09
//------------------------------------------------------------------
void DelayInit(void)
{
	// bit2置0，选择SYSTICK的时钟源为HCLK时钟的1/8
	SysTick->CTRL &= 0Xfffb;
	// us延时系数，即每微秒SysTick递减多少
	fac_us = SystemCoreClock / 1000000 / 8;		   
	// ms延时系数，即每毫秒SysTick递减多少
	fac_ms = (unsigned int)fac_us * 1000;
	
	DelayTimeFlag = 0;
}

//------------------------------------------------------------------
//    Name			DelayMs	
//    Arg			nms:需要延时的ms数
//    Return		void
//    Description	ms级延时
//    Author		<EMAIL>
//    Date			2018-10-09
//------------------------------------------------------------------
void DelayMs(u32 nms)
{	 		  	  
	u32 temp;	
	// 等待时间到达
	while(nms--)
	{
		// 时间加载(SysTick->LOAD为24bit)
		SysTick->LOAD = (u32)fac_ms;
		// 清空计数器
		SysTick->VAL = 0x00;
		// 开始倒数            
		SysTick->CTRL = 0x01;
		// SysTick->VAL倒数到0后，SysTick->CTRL最高位自动置1，读取该位将自动清0 
		do
		{
			temp = SysTick->CTRL;
		}
		while(temp & 0x01 && !(temp & (1 << 16)));
		ClearWatchDog();
	}
	// 关闭计数器
	SysTick->CTRL = 0x00;
	// 清空计数器
	SysTick->VAL = 0X00;       	  	    
}  
 
//------------------------------------------------------------------
//    Name			DelayUs	
//    Arg			nus:需要延时的us数
//    Return		void
//    Description	us级的延时
//    Author		<EMAIL>
//    Date			2018-10-09
//------------------------------------------------------------------    								   
void DelayUs(u32 nus)
{		
	u32 temp;
	
	temp = nus * fac_us;
	if(temp <= 6)
	{
		return;// 延时时间太短
	}
	// 时间加载(SysTick->LOAD为24bit)	    	 
	SysTick->LOAD = temp - 6;
	// 清空计数器	  		 
	SysTick->VAL = 0;
	// 开始倒数
	SysTick->CTRL = 0x01;
	//等待时间到达
	do
	{
		temp = SysTick->CTRL;
	}
	while(temp & 0x01 && !(temp & (1 << 16))); 
	// 关闭计数器
	SysTick->CTRL = 0x00;
	// 清空计数器
	SysTick->VAL = 0X00;       	 
}

//------------------------------------------------------------------
//    Name			DelayHalfUs	
//    Arg			void
//    Return		void
//    Description	延时大概半us
//    Author		<EMAIL>
//    Date			2018-10-09
//------------------------------------------------------------------   								   
void DelayHalfUs(void)
{		
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();
	__nop();	
}

//------------------------------------------------------------------
//    Name			SetDelayTimeUs	
//    Arg			nus:设定需要延时的us数,72M主频时nus最大值为1.86s
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-22
//------------------------------------------------------------------
void SetDelayTimeUs(u32 nus)
{	 		  	  
	u32 temp;	
	
	DelayTimeFlag = 0;
	// 时间加载(SysTick->LOAD为24bit)
	SysTick->LOAD = nus * fac_us;
	// 清空计数器
	SysTick->VAL = 0x00;
	// 开始倒数            
	SysTick->CTRL = 0x01;
}

//------------------------------------------------------------------
//    Name			GetDelayTimeFlag	
//    Arg			void
//    Return		0: 设定的时间未到, 1: 设定的时间已到
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-22
//------------------------------------------------------------------
u16 GetDelayTimeFlag(void)
{
	u32 temp;
	
	if(DelayTimeFlag)
	{
		return 1;
	}
	// SysTick->VAL倒数到0后，SysTick->CTRL最高位自动置1，读取该位将自动清0 
	temp = SysTick->CTRL;
	if(temp & 0x01 && !(temp & (1 << 16)))
	{
		return 0;
	}
	else
	{
		DelayTimeFlag = 1;
		return 1;
	}   	  	    
}  

//------------------------------------------------------------------
//    Name			WatchDogInit	
//    Arg			ms_timeout:超时的ms数
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-09
//------------------------------------------------------------------
void WatchDogInit(u32 ms_timeout)
{
	if(ms_timeout == 0)// 关闭看门狗
	{
		// 默认就是关闭状态
	}
	else// 配置看门狗并启用
	{
		ms_timeout /= 4;
		if(ms_timeout < 1)
		{
			ms_timeout = 1;
		}
		if(ms_timeout > 4096) 
		{
			ms_timeout = 4096;// 限幅保护
		}
		IWDG_WriteAccessCmd(IWDG_WriteAccess_Enable);
		IWDG_SetPrescaler(IWDG_Prescaler_128);
		IWDG_SetReload(ms_timeout - 1);
		IWDG_ReloadCounter();
		IWDG_Enable();// 使能
	}
}

//------------------------------------------------------------------
//    Name			LedInit	
//    Arg			void
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-09
//------------------------------------------------------------------
void LedInit(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	// 初始化LED
	LED1_OFF();
	LED2_OFF();
	LED3_OFF();
	LED4_OFF();
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6 | GPIO_Pin_7 | GPIO_Pin_8 | GPIO_Pin_9; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
}

//------------------------------------------------------------------
//    Name			Uart2Init	
//    Arg			br:波特率
//    Return		0
//    Description	串口2初始化
//    Author		<EMAIL>
//    Date			2018-10-10
//------------------------------------------------------------------
u16 Uart2Init(u32 br)
{
	NVIC_InitTypeDef NVIC_InitStructure;
	USART_InitTypeDef USART_InitStructure;
	GPIO_InitTypeDef GPIO_InitStructure;
	
	// 全局变量初始化
	Uart2RxdIsr = 0;
	Uart2TxdIsr = 0;

	//配置PA2,PA3作为USART2输出端口
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2 ; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3; 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	// 使能UART2时钟
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
	// 初始化UART2模块
	USART_InitStructure.USART_BaudRate            = br / 2;
	USART_InitStructure.USART_WordLength          = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits            = USART_StopBits_1;
	USART_InitStructure.USART_Parity              = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode                = USART_Mode_Rx | USART_Mode_Tx;
	USART_Init(USART2, &USART_InitStructure);
	USART_Cmd(USART2, ENABLE); 
	// 中断设置
	USART_ITConfig(USART2, USART_IT_RXNE, ENABLE);
	USART_ITConfig(USART2, USART_IT_TC, ENABLE);
	NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQChannel;		// 设定中断源
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;	// 中断占优先级为1
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;			// 副优先级为0
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;				// 使能中断
	NVIC_Init(&NVIC_InitStructure);							   	// 根据参数初始化中断寄存器
	
	return 0;
}

//------------------------------------------------------------------
//    Name			USART2_IRQHandler	
//    Arg			void
//    Return		void
//    Description	UART2中断函数
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void USART2_IRQHandler(void)
{
	u32 sr;
	u8 temp;
#ifdef SYSTEM_RUN_MODE_BOOT
	if(SystemRunMode == 0)
#endif
	{
		sr = USART2->SR;
		if(sr & USART_FLAG_TC)
		{
			USART2->SR = ~USART_FLAG_TC;
			if(Uart2TxdIsr)
			{
				Uart2TxdIsr();
			}			
		}
		if(sr & USART_FLAG_RXNE)
		{
			USART2->SR = ~USART_FLAG_RXNE;
			if(Uart2RxdIsr)
			{
				Uart2RxdIsr();
			}			
		}
		temp = USART2->DR;
		USART2->SR = ~USART_FLAG_ORE;
	}
#ifdef SYSTEM_RUN_MODE_BOOT
	else
	{
		// 用户模式
		((void (*)(void))(*(u32*)(APK_CODE_ADDR + 0xd8)))();
	}
#endif
}

//------------------------------------------------------------------
//    Name			ILI9481Init
//    Arg			void
//    Return		0
//    Description	ILI9481初始化
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
u16 ILI9481Init(void)
{
	SET_DB_OUT();
	LCD_CSN_L();
	LCD_WriteCmd(0x11);
	DelayMs(120); 
	LCD_WriteCmd(0xD0); 
	LCD_WriteArg(0x07); 
	LCD_WriteArg(0x41); 
	LCD_WriteArg(0x1F);
	LCD_WriteCmd(0xD1); 
	LCD_WriteArg(0x00); 
	LCD_WriteArg(0x20); 
	LCD_WriteArg(0x0D);
	LCD_WriteCmd(0xD2); 
	LCD_WriteArg(0x03); 
	LCD_WriteArg(0x00);
	LCD_WriteCmd(0xC0); 
	LCD_WriteArg(0x10); 
	LCD_WriteArg(0x3B); 
	LCD_WriteArg(0x00); 
	LCD_WriteArg(0x02); 
	LCD_WriteArg(0x11); 
	LCD_WriteCmd(0xC5); 
	LCD_WriteArg(0x02);
	LCD_WriteCmd(0xC8); 
	LCD_WriteArg(0x00); 
	LCD_WriteArg(0x01); 
	LCD_WriteArg(0x20); 
	LCD_WriteArg(0x01); 
	LCD_WriteArg(0x10); 
	LCD_WriteArg(0x0F); 
	LCD_WriteArg(0x74); 
	LCD_WriteArg(0x67); 
	LCD_WriteArg(0x77); 
	LCD_WriteArg(0x50); 
	LCD_WriteArg(0x0F); 
	LCD_WriteArg(0x10);
	LCD_WriteCmd(0xF8); 
	LCD_WriteArg(0x01);
	LCD_WriteCmd(0xFE); 
	LCD_WriteArg(0x00); 
	LCD_WriteArg(0x02);
	LCD_WriteCmd(0x2a);
	LCD_WriteArg(0x00);
	LCD_WriteArg(0x00);
	LCD_WriteArg(0x01);
	LCD_WriteArg(0x3F);
	LCD_WriteCmd(0x2b);
	LCD_WriteArg(0x00);
	LCD_WriteArg(0x00);
	LCD_WriteArg(0x01);
	LCD_WriteArg(0xdf);
	LCD_WriteCmd(0x36); 
	LCD_WriteArg(0x28 | 0x01 | 0x02);// B7,B6,B5 --> 扫描方向;B1,B0 --> 水平竖直镜像
	LCD_WriteCmd(0x3a); 
	LCD_WriteArg(0x55);
	DelayMs(120);
	LCD_WriteCmd(0x29);
	LCD_CSN_H();
	SET_DB_IN();
	return 0;
}

//------------------------------------------------------------------
//    Name			ILI9481WriteRamPrepare	
//    Arg			void
//    Return		void
//    Description	准备开始写入RAM数据
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void ILI9481WriteRamPrepare(void)
{
	SET_DB_OUT();
	LCD_CSN_L();
	LCD_WriteCmd(0x2C);
	LCD_CSN_H();
	SET_DB_IN();
}

//------------------------------------------------------------------
//    Name			ILI9481ReadRamPrepare		
//    Arg			void
//    Return		void
//    Description	开始读取RAM
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void ILI9481ReadRamPrepare(void)
{
	SET_DB_OUT();
	LCD_CSN_L();
	LCD_WriteCmd(0x2E);
	LCD_CSN_H();
	SET_DB_IN();
}

//------------------------------------------------------------------
//    Name			ILI9481SetDisplayWindow	
//    Arg			窗口的左上角坐标和右下角坐标
//    Return		void
//    Description	设置显示窗口
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void ILI9481SetDisplayWindow(u16 x0, u16 y0, u16 x1, u16 y1)
{
	SET_DB_OUT();
	LCD_CSN_L();
	LCD_WriteCmd(0x2A);   
	LCD_WriteArg(x0 >> 8);
	LCD_WriteArg(x0 & 0xff); 
	LCD_WriteArg(x1 >> 8);
	LCD_WriteArg(x1 & 0xff); 
	LCD_WriteCmd(0x2B);   
	LCD_WriteArg(y0 >> 8);
	LCD_WriteArg(y0 & 0xff); 
	LCD_WriteArg(y1 >> 8);
	LCD_WriteArg(y1 & 0xff);
	LCD_CSN_H();
	SET_DB_IN();
}

//------------------------------------------------------------------
//    Name			LCD_InitGpio	
//    Arg			void
//    Return		void
//    Description	初始化液晶屏相关GPIO
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void LCD_InitGpio(void)
{
	GPIO_InitTypeDef  GPIO_InitStructure;
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA|RCC_APB2Periph_GPIOB|RCC_APB2Periph_GPIOC|RCC_APB2Periph_GPIOD |RCC_APB2Periph_AFIO ,ENABLE);//使能PORTA~D时钟
	// 引脚默认状态
	LCD_CSN_H();
	LCD_RS_H();
	LCD_WRN_H();
	LCD_RDN_H();
	LCD_RESETN_H();
	WRITE_DB(0);
	// PC -> DB
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_All;				 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOC, &GPIO_InitStructure); 
	// PA15 -> CB0(CSN)
	GPIO_PinRemapConfig(GPIO_Remap_SWJ_JTAGDisable , ENABLE);// 不使用JTAG，可以使用SWD
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_15;				 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure); 
	// PD2 -> CB1(RS)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;				 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOD, &GPIO_InitStructure);
	// PB2 -> CB2(WRN)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;				 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	// PB12 -> CB3(RDN)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;				 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB, &GPIO_InitStructure); 	
	// PA12 -> CB4(RESETN)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;				 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure); 
}

//------------------------------------------------------------------
//    Name			LCD_BackLightInit	
//    Arg			void
//    Return		void
//    Description	初始化液晶屏背光
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void LCD_BackLightInit(void)
{
	TIM_OCInitTypeDef TIM_OCInitStructure;
	GPIO_InitTypeDef GPIO_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
	
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1,ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO,ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);// PA
	// LCD背光控制
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_11; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	
	TIM_OCInitStructure.TIM_OCMode       = TIM_OCMode_PWM1;             //在向上计数时，一旦TIMx_CNT<TIMx_CCR1时通道1为有效电平
    TIM_OCInitStructure.TIM_OutputState  = TIM_OutputState_Enable;      //比较输出使能
    TIM_OCInitStructure.TIM_OutputNState = TIM_OutputNState_Enable;     //PWM互补输出使能
    TIM_OCInitStructure.TIM_Pulse        = 0;  			             	//占空比 = TIM_Pulse/TIM_Period;
	TIM_OCInitStructure.TIM_OCIdleState  = TIM_OCIdleState_Reset;         //输出空闲状态
    TIM_OCInitStructure.TIM_OCNIdleState = TIM_OCNIdleState_Reset;      //PWM互补输出空闲状态
	
	TIM_OCInitStructure.TIM_OCPolarity   = TIM_OCPolarity_High;         //有效电平为高电平
    TIM_OCInitStructure.TIM_OCNPolarity  = TIM_OCNPolarity_High;        //互补通道电平与普通通道电平相反
	TIM_OC4Init(TIM1, &TIM_OCInitStructure);   
	TIM_OC4PreloadConfig(TIM1, TIM_OCPreload_Enable);
	LCD_SetBright(0);
	
	// TIM1设置
	TIM_TimeBaseStructure.TIM_Period = SystemCoreClock / 10000;	//频率的计算：	72M/(Prescaler+1)/(Period+1)	 
	TIM_TimeBaseStructure.TIM_Prescaler = 0; //预分频为0，实际计算时为0+1（都需加上1）
	TIM_TimeBaseStructure.TIM_ClockDivision=0;//时钟分频为0，则仍为倍频后的时钟频率
	TIM_TimeBaseStructure.TIM_CounterMode=TIM_CounterMode_Up;
	TIM_TimeBaseStructure.TIM_RepetitionCounter = 0;                    //重复溢出中断
	TIM_TimeBaseInit(TIM1,&TIM_TimeBaseStructure);
	TIM_ARRPreloadConfig(TIM1,ENABLE); 
	TIM_ClearFlag(TIM1, TIM_FLAG_Update);
	TIM_Cmd(TIM1,ENABLE);
	TIM_CtrlPWMOutputs(TIM1,ENABLE);
}

//------------------------------------------------------------------
//    Name			LCD_SetBright	
//    Arg			light_percent:亮度百分比
//    Return		void
//    Description	设置背光亮度
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void LCD_SetBright(u8 light_percent)
{
	TIM1->CCR4 = light_percent * (TIM1->ARR + 1) / 100;	
}


//------------------------------------------------------------------
//    Name			LCD_WriteArray	
//    Arg			addr:数组起始地址
//					n:写入的数据个数
//    Return		
//    Description	向LCD连续写入n个16位数
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void LCD_WriteArray(u16 *addr,u32 n)
{
	u16 wr_pin;
	u16 *addr_end;
	u16 *ptr1;
	u16 *ptr2;
	u16 *ptr3;
	LCD_CSN_L();
	LCD_RS_H();
	SET_DB_OUT();
	wr_pin = GPIO_Pin_2;
	ptr1 = (u16 *)&(GPIOB->BRR);
	ptr2 = (u16 *)&(GPIOB->BSRR);
	ptr3 = (u16 *)&(GPIOC->ODR);
	addr_end = addr + n;
	while(addr < addr_end)
	{
		*ptr1 = wr_pin;
		*ptr3 = *addr++;
		*ptr2 = wr_pin;
	}
	LCD_CSN_H();
	SET_DB_IN();
}

//------------------------------------------------------------------
//    Name			LCD_WriteConst	
//    Arg			value:待写入的数
//					n:写入数据的个数
//    Return		void
//    Description	向LCD连续写入n个16位常数
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void LCD_WriteConst(u16 value,u32 n)
{
	u16 wr_pin;
	u16 *addr_end;
	u16 *ptr1;
	u16 *ptr2;
	u16 *ptr3;
	LCD_CSN_L();
	LCD_RS_H();
	wr_pin = GPIO_Pin_2;
	ptr1 = (u16 *)&(GPIOB->BRR);
	ptr2 = (u16 *)&(GPIOB->BSRR);
	WRITE_DB(value);
	SET_DB_OUT();
	for(;n > 0;)
	{
		*ptr1 = wr_pin;
		n--;
//		__nop();
		*ptr2 = wr_pin;
	}
	LCD_CSN_H();
	SET_DB_IN();
}

//------------------------------------------------------------------
//    Name			LCD_WriteArrayFromXflash	
//    Arg			flash_data_addr:XFLASH的数据地址
//					n:写入的16位数据个数
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-15
//------------------------------------------------------------------
void LCD_WriteArrayFromXflash(u32 flash_data_addr,u32 n)
{
	u16 wr_pin;
	u16 *ptr1;
	u16 *ptr2;
	u16 *ptr3;
	u16 *addr;
	u32 n0;
	LCD_CSN_L();
	LCD_RS_H();
	wr_pin = GPIO_Pin_2;
	ptr1 = (u16 *)&(GPIOB->BRR);
	ptr2 = (u16 *)&(GPIOB->BSRR);
	ptr3 = (u16 *)&(GPIOC->ODR);
	n0 = 0;
	while(n > 0)
	{
		XFLASH_ReadData(flash_data_addr,XFLASH_TempBuffer,512);
		flash_data_addr += 512;
		addr = (u16*)XFLASH_TempBuffer;
		n0 = n;
		if(n0 > 256)
		{
			n0 = 256;
		}
		n -= n0;
		SET_DB_OUT();
		while(n0--)
		{
			*ptr1 = wr_pin;
			
			// 1. 小端格式
			*ptr3 = *addr++;
			// 2. 大端格式
//			*ptr3 = ((*addr << 8) & 0xff00) | ((*addr >> 8) & 0x00ff);
//			addr++;
			
			*ptr2 = wr_pin;
		}
		SET_DB_IN();
	}
	LCD_CSN_H();
}

//------------------------------------------------------------------
//    Name			IrInit	
//    Arg			void
//    Return		void
//    Description	红外遥控器的初始化
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void IrInit(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	EXTI_InitTypeDef EXTI_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;

	IrExtiIsr = 0;
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM6,ENABLE);
	//PB11作为红外信号输入
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_11;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_10MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_Init(GPIOB, &GPIO_InitStructure); 
	GPIO_EXTILineConfig(GPIO_PortSourceGPIOB,GPIO_PinSource11);
	//外部中断线的配置
	EXTI_InitStructure.EXTI_Line = EXTI_Line11;
	EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;
	EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising;
	EXTI_InitStructure.EXTI_LineCmd = ENABLE;
	EXTI_Init(&EXTI_InitStructure);
	//EXTI_Line11的中断配置
	NVIC_InitStructure.NVIC_IRQChannel = EXTI15_10_IRQChannel;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure); 
	//解码红外用到的定时器																		  
	TIM_TimeBaseStructure.TIM_Period = 200*10-1;	//红外超时长度定义200ms	 
	TIM_TimeBaseStructure.TIM_Prescaler = SystemCoreClock/10000-1; //预分频为0，实际计算时为0+1（都需加上1）
	TIM_TimeBaseStructure.TIM_ClockDivision = 0;			//时钟分频为0，则仍为倍频后的时钟频率
	TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInit(IR_TIM,&TIM_TimeBaseStructure);
	TIM_ARRPreloadConfig(IR_TIM,ENABLE);
	TIM_Cmd(IR_TIM,ENABLE);
}

//------------------------------------------------------------------
//    Name			EXTI15_10_IRQHandler	
//    Arg			void
//    Return		void
//    Description	红外遥控PB11的中断函数
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void EXTI15_10_IRQHandler(void)
{
#ifdef SYSTEM_RUN_MODE_BOOT
	if(SystemRunMode == 0)
#endif
	{
		if(EXTI_GetITStatus(EXTI_Line11))
		{
			EXTI->PR = EXTI_Line11;
			if(IrExtiIsr)
			{
				IrExtiIsr();
			}
		}
	}
#ifdef SYSTEM_RUN_MODE_BOOT
	else
	{
		// 用户模式
		((void (*)(void))(*(u32*)(APK_CODE_ADDR + 0xe0)))();
	}
#endif
}

//------------------------------------------------------------------
//    Name			AdcInit	
//    Arg			void
//    Return		void
//    Description	ADC初始化
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void AdcInit(void)
{
	ADC_InitTypeDef ADC_InitStructure;
	GPIO_InitTypeDef GPIO_InitStructure;
	// ADC3是备用的，暂时不用
	//使能ADC1 ADC2 ADC3
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1 | RCC_APB2Periph_ADC2 | RCC_APB2Periph_ADC3,ENABLE);
	//使能GPIO
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB,ENABLE);
	// 配置ADC CLK
	if(SystemCoreClock <= 28000000)
	{
		RCC_ADCCLKConfig(RCC_PCLK2_Div2);
	}
	else if(SystemCoreClock <= 56000000)
	{
		RCC_ADCCLKConfig(RCC_PCLK2_Div4);
	}
	else if(SystemCoreClock <= 84000000)
	{
		RCC_ADCCLKConfig(RCC_PCLK2_Div6);
	}
	else
	{
		RCC_ADCCLKConfig(RCC_PCLK2_Div8);
	}
	//配置ADC的输入通道
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_6 | GPIO_Pin_7;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	//ADC配置
	ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;									//独立工作模式
	ADC_InitStructure.ADC_ScanConvMode = ENABLE;										//扫描方式
	ADC_InitStructure.ADC_ContinuousConvMode = ENABLE;									//连续转换
	ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_None;					//外部触发禁止
	ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;								//数据右对齐
	ADC_InitStructure.ADC_NbrOfChannel = 3;												//用于转换的通道数
	ADC_Init(ADC1, &ADC_InitStructure);
	ADC_Init(ADC2, &ADC_InitStructure);
	ADC_Init(ADC3, &ADC_InitStructure);
	
	ADC_InjectedSequencerLengthConfig(ADC1, 3);
	ADC_InjectedSequencerLengthConfig(ADC2, 3);
	ADC_InjectedSequencerLengthConfig(ADC3, 3);
	ADC_InjectedChannelConfig(ADC1, ADC_Channel_0, 1,ADC_SampleTime_28Cycles5);
	ADC_InjectedChannelConfig(ADC1, ADC_Channel_1, 2,ADC_SampleTime_28Cycles5);	
	ADC_InjectedChannelConfig(ADC1, ADC_Channel_6, 3,ADC_SampleTime_28Cycles5);
	ADC_InjectedChannelConfig(ADC2, ADC_Channel_7, 1,ADC_SampleTime_28Cycles5);	
	ADC_InjectedChannelConfig(ADC2, ADC_Channel_8, 2,ADC_SampleTime_28Cycles5);	
	ADC_InjectedChannelConfig(ADC2, ADC_Channel_9, 3,ADC_SampleTime_28Cycles5);	
	ADC_InjectedChannelConfig(ADC3, ADC_Channel_0, 1,ADC_SampleTime_28Cycles5);	
	ADC_InjectedChannelConfig(ADC3, ADC_Channel_1, 2,ADC_SampleTime_28Cycles5);	
	ADC_InjectedChannelConfig(ADC3, ADC_Channel_6, 3,ADC_SampleTime_28Cycles5);	
	
  	ADC_AutoInjectedConvCmd(ADC1, ENABLE);
	ADC_AutoInjectedConvCmd(ADC2, ENABLE);
	ADC_AutoInjectedConvCmd(ADC3, ENABLE);	
	/* Enable ADC */
	ADC_Cmd(ADC1, ENABLE);
	ADC_Cmd(ADC2, ENABLE);
	ADC_Cmd(ADC3, ENABLE);

	/* Enable ADC1 reset calibaration register */   
	ADC_ResetCalibration(ADC1);
	ADC_ResetCalibration(ADC2);
	ADC_ResetCalibration(ADC3);
	/* Check the end of ADC1 reset calibration register */
	while(ADC_GetResetCalibrationStatus(ADC1));
	while(ADC_GetResetCalibrationStatus(ADC2));
	while(ADC_GetResetCalibrationStatus(ADC3));
	/* Start ADC1 calibaration */
	ADC_StartCalibration(ADC1);
	ADC_StartCalibration(ADC2);
	ADC_StartCalibration(ADC3);
	/* Check the end of ADC1 calibration */
	while(ADC_GetCalibrationStatus(ADC1));
	while(ADC_GetCalibrationStatus(ADC2));
	while(ADC_GetCalibrationStatus(ADC3));
	ADC_SoftwareStartConvCmd(ADC1,ENABLE);
	ADC_SoftwareStartConvCmd(ADC2,ENABLE);
	ADC_SoftwareStartConvCmd(ADC3,ENABLE);	
}

//------------------------------------------------------------------
//    Name			DacInit	
//    Arg			void
//    Return		void
//    Description	DAC初始化
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void DacInit(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	DAC_InitTypeDef DAC_InitStructure;

	// DAC初始化
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE );	  //使能PORTA通道时钟
   	RCC_APB1PeriphClockCmd(RCC_APB1Periph_DAC, ENABLE );	  //使能DAC通道时钟 
	// DAC1
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;	 // 端口配置
 	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN; 		 //模拟输入
 	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
 	GPIO_Init(GPIOA, &GPIO_InitStructure);
	// DAC2  默认DAC2连接到电阻触摸屏的IRQ
//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5;	 // 端口配置
// 	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN; 		 //模拟输入
// 	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
// 	GPIO_Init(GPIOA, &GPIO_InitStructure);
	
	DAC_InitStructure.DAC_Trigger = DAC_Trigger_Software; //触发
	DAC_InitStructure.DAC_WaveGeneration = DAC_WaveGeneration_None;
	DAC_InitStructure.DAC_LFSRUnmask_TriangleAmplitude =DAC_LFSRUnmask_Bits11_0;
	DAC_InitStructure.DAC_OutputBuffer = DAC_OutputBuffer_Enable;
	DAC_Init(DAC_Channel_1, &DAC_InitStructure);
	DAC_Init(DAC_Channel_2, &DAC_InitStructure);
	// 使能DAC
	DAC_Cmd(DAC_Channel_1, ENABLE);
//	DAC_Cmd(DAC_Channel_2, ENABLE);// 默认DAC2连接到电阻触摸屏的IRQ
}

//------------------------------------------------------------------
//    Name			PwmInit	
//    Arg			void
//    Return		void
//    Description	PWM初始化
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void PwmInit(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
	TIM_BDTRInitTypeDef TIM_BDTRInitStruct;
	TIM_OCInitTypeDef TIM_OCInitStructure;

	PwmIsr = 0;
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1,ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM4,ENABLE);
	// TIM1_CH1~4
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_10; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	GPIOA->BRR = GPIO_InitStructure.GPIO_Pin;
	// TIM1_CHN1~3
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_13 | GPIO_Pin_14 | GPIO_Pin_15; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	GPIOB->BRR = GPIO_InitStructure.GPIO_Pin;
	// TIM4_CH1~4
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6 | GPIO_Pin_7 | GPIO_Pin_8 | GPIO_Pin_9; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	GPIOB->BRR = GPIO_InitStructure.GPIO_Pin;
	/* Channel_1   TIM_OCMode_PWM1模式 */  
    TIM_OCInitStructure.TIM_OCMode       = TIM_OCMode_PWM1;             //在向上计数时，一旦TIMx_CNT<TIMx_CCR1时通道1为有效电平
    TIM_OCInitStructure.TIM_OutputState  = TIM_OutputState_Enable;      //比较输出使能
    TIM_OCInitStructure.TIM_OutputNState = TIM_OutputNState_Enable;     //PWM互补输出使能
    TIM_OCInitStructure.TIM_Pulse        = 0;  			             //占空比 = TIM_Pulse/TIM_Period;
	TIM_OCInitStructure.TIM_OCIdleState  = TIM_OCIdleState_Reset;         //输出空闲状态
    TIM_OCInitStructure.TIM_OCNIdleState = TIM_OCNIdleState_Reset;      //PWM互补输出空闲状态
	// PWM1,PWM2
    TIM_OCInitStructure.TIM_OCPolarity   = TIM_OCPolarity_High;         //有效电平为高电平
    TIM_OCInitStructure.TIM_OCNPolarity  = TIM_OCNPolarity_High;        //互补通道电平与普通通道电平相反
    TIM_OC1Init(TIM1, &TIM_OCInitStructure);   
	TIM_OC1PreloadConfig(TIM1, TIM_OCPreload_Enable);
	// PWM3,PWM4
	TIM_OCInitStructure.TIM_OCPolarity   = TIM_OCPolarity_High;         //有效电平为高电平
    TIM_OCInitStructure.TIM_OCNPolarity  = TIM_OCNPolarity_High;        //互补通道电平与普通通道电平相反
	TIM_OC2Init(TIM1, &TIM_OCInitStructure);   
	TIM_OC2PreloadConfig(TIM1, TIM_OCPreload_Enable);
	// PWM5,PWM6
	TIM_OCInitStructure.TIM_OCPolarity   = TIM_OCPolarity_High;         //有效电平为高电平
    TIM_OCInitStructure.TIM_OCNPolarity  = TIM_OCNPolarity_High;        //互补通道电平与普通通道电平相反
	TIM_OC3Init(TIM1, &TIM_OCInitStructure);   
	TIM_OC3PreloadConfig(TIM1, TIM_OCPreload_Enable);
	// PWM7,PWM8,PWM9,PWM10
	TIM_OCInitStructure.TIM_OCPolarity   = TIM_OCPolarity_High;         //有效电平为高电平
	TIM_OC1Init(TIM4, &TIM_OCInitStructure);   
	TIM_OC1PreloadConfig(TIM4, TIM_OCPreload_Enable);
	TIM_OC2Init(TIM4, &TIM_OCInitStructure);   
	TIM_OC2PreloadConfig(TIM4, TIM_OCPreload_Enable);
	TIM_OC3Init(TIM4, &TIM_OCInitStructure);   
	TIM_OC3PreloadConfig(TIM4, TIM_OCPreload_Enable);
	TIM_OC4Init(TIM4, &TIM_OCInitStructure);   
	TIM_OC4PreloadConfig(TIM4, TIM_OCPreload_Enable);
	// 死区时间控制
	TIM_BDTRInitStruct.TIM_OSSRState = TIM_OSSRState_Disable;
	TIM_BDTRInitStruct.TIM_OSSIState = TIM_OSSIState_Disable;
	TIM_BDTRInitStruct.TIM_LOCKLevel = TIM_LOCKLevel_OFF;
	TIM_BDTRInitStruct.TIM_DeadTime = 100;
	TIM_BDTRInitStruct.TIM_Break = TIM_Break_Disable;
	TIM_BDTRInitStruct.TIM_BreakPolarity = TIM_BreakPolarity_High;
	TIM_BDTRInitStruct.TIM_AutomaticOutput = TIM_AutomaticOutput_Enable;
	TIM_BDTRConfig(TIM1,&TIM_BDTRInitStruct);
	// TIM1设置
	TIM_TimeBaseStructure.TIM_Period = PWM_ARR_DEFAULT;	//频率的计算：	72M/(Prescaler+1)/(Period+1)	 
	TIM_TimeBaseStructure.TIM_Prescaler = 0; //预分频为0，实际计算时为0+1（都需加上1）
	TIM_TimeBaseStructure.TIM_ClockDivision=0;//时钟分频为0，则仍为倍频后的时钟频率
	TIM_TimeBaseStructure.TIM_CounterMode=TIM_CounterMode_Up;
	TIM_TimeBaseStructure.TIM_RepetitionCounter = 0;                    //重复溢出中断
	TIM_ARRPreloadConfig(TIM1,ENABLE);
	TIM_TimeBaseInit(TIM1, &TIM_TimeBaseStructure);  
	TIM_ClearFlag(TIM1, TIM_FLAG_Update);
	TIM_CtrlPWMOutputs(TIM1,ENABLE);
	TIM_ITConfig(TIM1,TIM_IT_Update,ENABLE);
	TIM_Cmd(TIM1,ENABLE);
	// TIM4设置
	TIM_TimeBaseStructure.TIM_Period = PWM_ARR_DEFAULT;	//频率的计算：	72M/(Prescaler+1)/(Period+1)	 
	TIM_TimeBaseStructure.TIM_Prescaler = 0; //预分频为0，实际计算时为0+1（都需加上1）
	TIM_TimeBaseStructure.TIM_ClockDivision=0;//时钟分频为0，则仍为倍频后的时钟频率
	TIM_TimeBaseStructure.TIM_CounterMode=TIM_CounterMode_Up;
	TIM_ARRPreloadConfig(TIM4,ENABLE);
	TIM_TimeBaseInit(TIM4, &TIM_TimeBaseStructure);  
	TIM_Cmd(TIM4,ENABLE);
	// 定义中断优先级
	NVIC_InitStructure.NVIC_IRQChannel = TIM1_UP_IRQChannel;		//设定中断源为 
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;	//中断占优先级为2
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;			//副优先级为0
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;				//使能中断
	NVIC_Init(&NVIC_InitStructure);
}

//------------------------------------------------------------------
//    Name			TIM1_UP_IRQHandler	
//    Arg			void
//    Return		void
//    Description	TIM1中断函数
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void TIM1_UP_IRQHandler(void)
{
#ifdef SYSTEM_RUN_MODE_BOOT
	if(SystemRunMode == 0)
#endif
	{
		if(PwmIsr)
		{
			PwmIsr();
		}
		TIM1->SR = 0;// 清除中断标志
	}
#ifdef SYSTEM_RUN_MODE_BOOT
	else
	{
		// 用户模式
		((void (*)(void))(*(u32*)(APK_CODE_ADDR + 0xa4)))();
	}
#endif
}

//------------------------------------------------------------------
//    Name			Spi3Init	
//    Arg			void
//    Return		void
//    Description	SPI3模块初始化
//    Author		<EMAIL>
//    Date			2018-10-13
//------------------------------------------------------------------
void Spi3Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	SPI_InitTypeDef  SPI_InitStructure;
	
	if(SPI_InitFlag)
	{
		return;
	}
	//SPI1Periph clock enable 
    RCC_APB1PeriphClockCmd( RCC_APB1Periph_SPI3, ENABLE ) ;
	TP_CS_H();
	SPI_FLASH_CS_H();
	// SCK MOSI
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3 | GPIO_Pin_5; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;  
	GPIO_Init(GPIOB,&GPIO_InitStructure);  
	// CS
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; 	
	GPIO_Init(GPIOD,&GPIO_InitStructure);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE);
	GPIO_PinRemapConfig(GPIO_Remap_PD01, ENABLE);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10; 
	GPIO_Init(GPIOB,&GPIO_InitStructure);
	// MISO
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU; 	
	GPIO_Init(GPIOB,&GPIO_InitStructure);
	// SPI3
	SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex;
	SPI_InitStructure.SPI_Mode = SPI_Mode_Master;
	SPI_InitStructure.SPI_DataSize = SPI_DataSize_8b;
	SPI_InitStructure.SPI_CPOL = SPI_CPOL_Low;
	SPI_InitStructure.SPI_CPHA = SPI_CPHA_1Edge;
	SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;
	SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_256;
	SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB;
	SPI_InitStructure.SPI_CRCPolynomial = 7;
	SPI_Init(SPI3, &SPI_InitStructure);	
	SPI_Cmd(SPI3, ENABLE);	
	SPI_InitFlag = 1;
}

//------------------------------------------------------------------
//    Name			SPI_ReadWriteByte	
//    Arg			TxData:待发送的数据
//    Return		接收到的数据
//    Description	SPI读写一个数据，为了提高代码执行效率，已经改为宏定义
//    Author		<EMAIL>
//    Date			2018-10-13
//------------------------------------------------------------------
//__inline u8 SPI_WriteReadByte(u8 TxData)
//{
//	/* Loop while DR register in not empty */
////	while ((SPI3->SR & SPI_I2S_FLAG_TXE) == RESET);
//	/* Send byte through the SPI2 peripheral */
//	SPI3->DR = TxData;
//	/* Wait to receive a byte */
//	while((SPI3->SR & SPI_I2S_FLAG_RXNE) == RESET);
//	/* Return the byte read from the SPI bus */
//	return SPI3->DR;
//}

//------------------------------------------------------------------
//    Name			SPI_FLASH_WaitBusy	
//    Arg			void
//    Return		0: 成功 , 1: 失败
//    Description	等待FLASH处理结束
//    Author		<EMAIL>
//    Date			2018-10-13
//------------------------------------------------------------------
__inline u8 SPI_FLASH_WaitBusy(void)
{
	u8 sr;

	SPI_FLASH_CS_L();
	SPI_WriteByte(0x05);// SR1
	SetDelayTimeUs(200000);
	do
	{		
		 SPI_WriteReadByte(0xff,sr);
	}while((sr & 0x01) && (GetDelayTimeFlag() == 0));

	SPI_FLASH_CS_H();
	return GetDelayTimeFlag();
}

//------------------------------------------------------------------
//    Name			SPI_FLASH_Init	
//    Arg			void
//    Return		void
//    Description	SPI FLASH初始化
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void SPI_FLASH_Init(void)
{
	SPI_InitTypeDef  SPI_InitStructure;
	GPIO_InitTypeDef GPIO_InitStructure;
	u8 sr;
	u8 i;
	
	SPI_FLASH_InitFlag = 0;
	//SPI3的配置
	Spi3Init();
	// 软件复位存储器，复位后等待至少30us才能正常工作
	DelayUs(10);
	SPI_FLASH_CS_L();
	SPI_WriteByte(0x66);// 使能复位
	SPI_FLASH_CS_H();
	SPI_FLASH_CS_L();
	SPI_WriteByte(0x99);// 复位
	SPI_FLASH_CS_H();
	DelayUs(100);// 等待的时间故意放长
	if(SPI_FLASH_WaitBusy())
	{
		SPI_FLASH_InitFlag = 0;
		return;
	}
	//写保护使能
	SPI_FLASH_CS_L();
	SPI_WriteByte(0x04);// 写保护
	SPI_FLASH_CS_H();
	if(SPI_FLASH_WaitBusy())
	{
		SPI_FLASH_InitFlag = 0;
		return;
	}
	SPI_FLASH_ReadId();
}

//------------------------------------------------------------------
//    Name			SPI FLASH读取ID	
//    Arg			void
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-13
//------------------------------------------------------------------
void SPI_FLASH_ReadId(void)
{
	u8 i,j,k;
	u8 id[8];
	u32 flash_id0,flash_id1;

	XFlashId[0] = 0;
	XFlashId[1] = 0;
	SPI_SetSpeed(SPI_SPEED_HIGH);
	ClearWatchDog();
	if(SPI_FLASH_WaitBusy())
	{
		SPI_FLASH_InitFlag = 0;
		return;
	}
	SPI_FLASH_CS_L();
	SPI_WriteByte(0x4B);// 读取ID
	SPI_WriteByte(0xff);// 空
	SPI_WriteByte(0xff);// 空
	SPI_WriteByte(0xff);// 空
	SPI_WriteByte(0xff);// 空
	for(i = 0;i < 8;i++)
	{
		 SPI_WriteReadByte(0xff,id[i]);
	}
	SPI_FLASH_CS_H();
	if(SPI_FLASH_WaitBusy())
	{
		SPI_FLASH_InitFlag = 0;
		return;
	}
	XFlashId[0] = (id[0] << 24) | (id[1] << 16) | (id[2] << 8) | (id[3]);
	XFlashId[1] = (id[4] << 24) | (id[5] << 16) | (id[6] << 8) | (id[7]);
	SPI_FLASH_InitFlag = 1;
}

//------------------------------------------------------------------
//    Name			SPI_FLASH_WriteSector	
//    Arg			sector_addr:扇区的地址
//					ptr_data:数据指针
//					num:数据的个数，不大于XFLASH_SECTOR_SIZE，不小于1
//    Return		0: 成功
//					1: 失败
//    Description	1. 在写之前要确保本扇区已经擦除过；
//					2. 如果数据个数低于XFLASH_SECTOR_SIZE，那么剩下的
//						空间默认是0xFF；
//					3. 如果数据个数超过XFLASH_SECTOR_SIZE，将只是写入
//						前4096的数据；
//    Author		<EMAIL>
//    Date			2018-10-13
//------------------------------------------------------------------
u16 SPI_FLASH_WriteSector(u32 sector_addr,u8 *ptr_data,u32 num)
{
	u16 i;
	u8 sr;
	u32 addr;
	
	if(SPI_FLASH_InitFlag == 0)
	{
		return 1;
	}
	if(num <= 0)
	{
		return 0; 
	}
	if(num > XFLASH_SECTOR_SIZE)
	{
		num = XFLASH_SECTOR_SIZE;
	}
	addr = sector_addr << 12;
	SPI_SetSpeed(SPI_SPEED_HIGH);
	if(SPI_FLASH_WaitBusy())
	{
		SPI_FLASH_InitFlag = 0;
		return 1;
	}
	while(num > 0)
	{
		SPI_FLASH_CS_L();
		SPI_WriteByte(0x06);// 写使能
		SPI_FLASH_CS_H();
		SPI_FLASH_CS_L();		
		SPI_WriteByte(0x02);// 写一页
		SPI_WriteByte(addr >> 16);
		SPI_WriteByte(addr >> 8);
		SPI_WriteByte(addr);
		i = 256;
		while(i--)
		{
			SPI_WriteByte(*ptr_data++);
			num--;
			if(num <= 0)
			{
				break;
			}
		}
		SPI_FLASH_CS_H();
		if(SPI_FLASH_WaitBusy())
		{
			SPI_FLASH_InitFlag = 0;
			return 1;
		}
		addr += 256;
		ClearWatchDog();
	}
	SPI_FLASH_CS_L();
	SPI_WriteByte(0x04);// 写保护
	SPI_FLASH_CS_H();
	if(SPI_FLASH_WaitBusy())
	{
		SPI_FLASH_InitFlag = 0;
		return 1;
	}
	return 0;
}

//------------------------------------------------------------------
//    Name			SPI_FLASH_EraseSector	
//    Arg			sector_addr:扇区的地址
//    Return		0
//    Description	SPI FLASH擦除一扇区
//    Author		<EMAIL>
//    Date			2018-10-13
//------------------------------------------------------------------
u16 SPI_FLASH_EraseSector(u32 sector_addr)
{
	u32 addr;
	
	if(SPI_FLASH_InitFlag == 0)
	{
		return 1;
	}
	addr = sector_addr << 12;
	if(SPI_FLASH_WaitBusy())
	{
		SPI_FLASH_InitFlag = 0;
		return 1;
	}
	SPI_FLASH_CS_L();
	SPI_WriteByte(0x06);// 写使能
	SPI_FLASH_CS_H();
	SPI_FLASH_CS_L();
	SPI_WriteByte(0x20);// 擦除
	SPI_WriteByte(addr >> 16);
	SPI_WriteByte(addr >> 8);
	SPI_WriteByte(addr);
	SPI_FLASH_CS_H();
	return 0;
}

//------------------------------------------------------------------
//    Name			SPI_FLASH_ReadData	
//    Arg			addr:数据的地址
//					ptr_data:返回的数据指针
//					num:数据的个数
//    Return		0
//    Description	在指定的地址写入指定长度的数据
//    Author		<EMAIL>
//    Date			2018-10-13
//------------------------------------------------------------------
u16 SPI_FLASH_ReadData(u32 addr,u8 *ptr_data,u32 num)
{
	u16 i;
	
	if(num <= 0)
	{
		return 0; 
	}
	if(SPI_FLASH_InitFlag == 0)
	{
		ptr_data[0] = 0;
		return 1;
	}
	SPI_SetSpeed(SPI_SPEED_HIGH);
	SPI_FLASH_WaitBusy();
	SPI_FLASH_CS_L();
//	SPI_WriteByte(0x0B);// 快速读取需要地址传送完毕时加8个时钟等待
	SPI_WriteByte(0x03);// 读取
	SPI_WriteByte(addr >> 16);
	SPI_WriteByte(addr >> 8);
	SPI_WriteByte(addr);
//	SPI_WriteByte(0xff);
	while(num--)
	{
		SPI_WriteReadByte(0xff,*ptr_data++);
	}
	SPI_FLASH_CS_H();
	SPI_FLASH_WaitBusy();
	return 0;
}

//------------------------------------------------------------------
//    Name			I2C_GpioInit	
//    Arg			void
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
void I2C_GpioInit(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB,ENABLE);
	// SCL
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	// SDA
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_OD;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	// 默认为1
	I2C_SCL_H();
	I2C_SDA_H();
}

//------------------------------------------------------------------
//    Name			I2C_Wait	
//    Arg			void
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
void I2C_Wait(void)    
{
	DelayUs(10);
}

//------------------------------------------------------------------
//    Name			I2C_Start	
//    Arg			void
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
void I2C_Start(void)   
{
	I2C_SDA_H();
	I2C_SCL_H();
	I2C_Wait();
	I2C_SDA_L();
	I2C_Wait();
	I2C_SCL_L();
}

//------------------------------------------------------------------
//    Name			I2C_Stop	
//    Arg			void
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
void I2C_Stop(void)    
{
	I2C_SDA_L();
	I2C_Wait();
	I2C_SCL_H();
	I2C_Wait();
	I2C_SDA_H();
	I2C_Wait();
}

//------------------------------------------------------------------
//    Name			I2C_SendAck	
//    Arg			ack:发送的应答值
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
void I2C_SendAck(unsigned char ack) 
{
	
	if(ack)
	{
		I2C_SDA_H();
	}
	else
	{
		I2C_SDA_L();
	}
	I2C_Wait();
	I2C_SCL_H();
	I2C_Wait();
	I2C_SCL_L();
	I2C_Wait();
	I2C_SDA_H();
	I2C_Wait();
}

//------------------------------------------------------------------
//    Name			I2C_SendByte	
//    Arg			bytedata:待发送的数据
//    Return		ack
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
unsigned char I2C_SendByte(unsigned char bytedata) 
{
  
	unsigned char i;
	unsigned char ack;
	I2C_Wait();
	for(i=0;i<8;i++)
	{
		if(bytedata & 0x80)
		{
		  	I2C_SDA_H();
		}
		else
		{
		  	I2C_SDA_L();
		}
		bytedata <<= 1;
		I2C_Wait();
		I2C_SCL_H();
		I2C_Wait();
		I2C_SCL_L();
		I2C_Wait();
	}
	I2C_SDA_H();
	I2C_Wait();
	I2C_SCL_H();
	I2C_Wait();
	ack = I2C_SDA_IS_H;
	I2C_SCL_L();
	I2C_Wait();
	return ack;
}

//------------------------------------------------------------------
//    Name			I2C_ReceiveByte	
//    Arg			void
//    Return		读取的数据
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
unsigned char I2C_ReceiveByte(void)  
{
	unsigned char i;
	unsigned char bytedata = 0;
	I2C_Wait();
	for(i=0;i<8;i++)
	{
		I2C_SCL_H();
		I2C_Wait();
		bytedata <<= 1;
		if(I2C_SDA_IS_H)
		{
			bytedata |= 0x01;
		}
		I2C_SCL_L();
		I2C_Wait();
	}
	return bytedata;
}

//------------------------------------------------------------------
//    Name			I2C_ByteWrite	
//    Arg			device:设备地址
//					address:寄存器地址
//					bytedata:数据
//    Return		0:成功
// 					非0:失败
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
unsigned char  I2C_ByteWrite(unsigned char device,unsigned char address,unsigned char bytedata)
{     
	unsigned char ack;
	if(!I2C_SDA_IS_H) return -1;
	I2C_Start();
	ack = I2C_SendByte(device);
	if(ack)
	{
		I2C_Stop();
		return -2;
	}	 
	ack = I2C_SendByte(address);
	if(ack)
	{
		I2C_Stop();
		return -3;
	}	 
	ack = I2C_SendByte(bytedata);
	I2C_Stop();
	if(ack)
	{
		return -4;
	}
	return 0;	 
}

//------------------------------------------------------------------
//    Name			TP_WriteReg	
//    Arg			reg:寄存器地址
//					buf:数据指针
//					len:数据个数
//    Return		0:成功
// 					非0:失败
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
u8 TP_WriteReg(u16 reg,u8 *buf,u8 len)
{
	u8 i;
	u8 return_val=0;
	I2C_Start();	 
	I2C_SendByte(TP_CMD_WR);
	I2C_SendByte(reg>>8);
	I2C_SendByte(reg&0XFF); 
	for(i=0;i<len;i++)
	{	   
    	return_val=I2C_SendByte(buf[i]);
		if(return_val) break;  
	}
    I2C_Stop();				
	return return_val; 
}

//------------------------------------------------------------------
//    Name			TP_WriteReg	
//    Arg			reg:寄存器地址
//					buf:数据指针
//					len:数据个数
//    Return		void
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
void TP_ReadReg(u16 reg,u8 *buf,u8 len)
{
	s8 i; 
 	I2C_Start();	
 	I2C_SendByte(TP_CMD_WR);
	I2C_SendByte(reg>>8); 
	I2C_SendByte(reg&0XFF);
 	I2C_Start();  	 	   
	I2C_SendByte(TP_CMD_RD);   
	for(i=0;i<len-1;i++)
	{	   
    	buf[i] = I2C_ReceiveByte();
		I2C_SendAck(0);
	} 
	buf[i]=I2C_ReceiveByte();
	I2C_SendAck(1);
    I2C_Stop();
}

//------------------------------------------------------------------
//    Name			TP_Init	
//    Arg			void
//    Return		void
//    Description	触摸屏初始化
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
void TP_Init(void)
{
		GPIO_InitTypeDef GPIO_InitStructure;
	u8 temp,i;
	
#ifdef TP_TYPE_RES
	//SPI3的配置
	Spi3Init();
//	Touch_Screen_Cal();
	Cal_kx=0.2546;
	Cal_bx= -29.5746;
	Cal_ky=0.17303;
	Cal_by=-17.7047;
#else
#ifdef TP_TYPE_CAP
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB,ENABLE);
	
//	RESET
//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;
//	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
//	GPIO_Init(GPIOA, &GPIO_InitStructure);
	// SCL
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	// SDA
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_OD;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	// INT
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	// 复位已经在系统复位时复位过，此处跳过
//	TP_RESET_L();
//	DelayMs(50);
//	TP_RESET_H();
//	DelayMs(100);
	TP_SDA_H();
	TP_SCL_H();
	DelayMs(10);
	temp = 0;
	HAL_I2cWriteReg(FT_DEVIDE_MODE,&temp,1);	//进入正常操作模式 
 	temp = 12;								//触摸有效值，22，越小越灵敏	
 	HAL_I2cWriteReg(FT_ID_G_THGROUP,&temp,1);	//设置触摸有效值
 	temp = 12;								//激活周期，不能小于12，最大14
 	HAL_I2cWriteReg(FT_ID_G_PERIODACTIVE,&temp,1);
#endif
#endif
	for(i = 0;i < TP_NUM_MAX;i++)	        
	{
		TP_TouchPoint[i].x = 0;
		TP_TouchPoint[i].y = 0;
	}
	TP_TouchNum = 0;
}

//------------------------------------------------------------------
//    Name			TP_ResGetAd	
//    Arg			x,y: 返回的x,y坐标
//    Return		
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-12
//------------------------------------------------------------------
void TP_ResGetAd(u16 *x,u16 *y)
{
	u16 data1,data2;
	SPI_WriteByte(0xd0);
	SPI_WriteReadByte(0x00,data1);
	SPI_WriteReadByte(0x90,data2);
	*y = ((data1 << 4) | (data2 >> 4)) & 0x7ff;
	SPI_WriteReadByte(0x00,data1);
	SPI_WriteReadByte(0x00,data2);
	*x = 0x7ff - (((data1 << 4) | (data2 >> 4)) & 0x7ff);
}

//------------------------------------------------------------------
//    Name			TP_TouchScan	
//    Arg			void
//    Return		void
//    Description	触摸屏扫描，用以获取触摸点数及坐标
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
void TP_TouchScan(void)
{
	u8 i=0;
	u8 sta = 0;
	u8 buf[4] = {0}; 
	u16 x[5],y[5],sx,sy;	
	float x1,y1;
	static u8 cnt = 0;
#ifdef TP_TYPE_RES
	if(TP_IRQ_IS_L)
	{
		if(cnt)
		{
			cnt--;
			return;
		}
		SPI_SetSpeed(SPI_SPEED_LOW);
		TP_CS_L();
		i = 5;
		while(i--)
		{
			TP_ResGetAd(x + i,y + i);
		}
		TP_CS_H();
		sx = 0;
		sy = 0;
		for(i = 0;i < 5;i++)
		{
			sx+=x[i];
			sy+=y[i];
		}
		sx /= 5;
		sy /= 5;
		x1 = Cal_kx * sx + Cal_bx;
		y1 = Cal_ky * sy + Cal_by;
		if((x1 <= 0) || (y1 <= 0))
		{
			if(TP_TouchNum)
			{
				TP_TouchNum = 0;
				TP_TouchPoint[0].x = 0;
				TP_TouchPoint[0].y = 0;
			}
		}
		else
		{
			TP_TouchPoint[0].x = x1;
			TP_TouchPoint[0].y = y1;
			TP_TouchNum = 1;
		}
		return;
	}
	else if(TP_TouchNum)
	{
		TP_TouchNum = 0;
		TP_TouchPoint[0].x = 0;
		TP_TouchPoint[0].y = 0;
	}
	cnt = 5;
#else
#ifdef TP_TYPE_CAP	
	HAL_I2cReadReg(0x02,&sta,1);//读取触摸点的状态  	   
 	if(sta & 0x0f)	//判断是否有触摸点按下，0x02寄存器的低4位表示有效触点个数
 	{
		TPR_Structure.TouchNum = sta & 0x0f;
		if(TPR_Structure.TouchNum > 5)
		{
			TPR_Structure.TouchNum = 0;
		}
 		TPR_Structure.TouchSta = ~(0xFF << (sta & 0x0F));	//~(0xFF << (sta & 0x0F))将点的个数转换为触摸点按下有效标志
 		for(i=0;i<5;i++)	                                //分别判断触摸点1-5是否被按下
 		{
 			if(TPR_Structure.TouchSta & (1<<i))			    //读取触摸点坐标
 			{											    //被按下则读取对应触摸点坐标数据
 				HAL_I2cReadReg(HAL_TPX_TBL[i],buf,4);	//读取XY坐标值
 				if((buf[0]&0XC0)!=0X80)
 				{
					TPR_Structure.x[i] = 0;
					TPR_Structure.y[i] = 0;//必须是contact事件，才认为有效	
				}
				else
				{
					TPR_Structure.y[i]=((u16)(buf[0]&0X0F)<<8)+buf[1] - 10;
					TPR_Structure.x[i]=480 - (((u16)(buf[2]&0X0F)<<8)+buf[3]);
				}
				if(TPR_Structure.x[i] < 0)
				{
					TPR_Structure.x[i] = 0;
				}
				else if(TPR_Structure.x[i] > 479)
				{
					TPR_Structure.x[i] = 479;
				}
				if(TPR_Structure.y[i] < 0)
				{
					TPR_Structure.y[i] = 0;
				}
				else if(TPR_Structure.y[i] > 319)
				{
					TPR_Structure.y[i] = 319;
				}
 			}
 		}
 		TPR_Structure.TouchSta |= TP_PRES_DOWN;     //触摸按下标记
 	}
 	else
 	{
		TPR_Structure.TouchNum = 0;
 		if(TPR_Structure.TouchSta &TP_PRES_DOWN) 	//之前是被按下的
 		{
			TPR_Structure.TouchSta &= ~0x80;        //触摸松开标记	
		}
 		else
 		{
 			for(i=0;i<5;i++)	                                //分别判断触摸点1-5是否被按下
			{
				TPR_Structure.x[i] = 0;
				TPR_Structure.y[i] = 0;//必须是contact事件，才认为有效
					
			}
 			TPR_Structure.TouchSta &= 0xe0;	//清楚触摸点有效标记
 		}
 	}
#endif
#endif
}

//------------------------------------------------------------------
//    Name			TASK_TimerInit	
//    Arg			void
//    Return		void
//    Description	任务定时器初始化
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
void TASK_TimerInit(void)
{
	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	//  TASK_TIM的配置
#define TASK_PERIOD		(10)	// 单位是0.1ms
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM5,ENABLE);	
	TIM_Cmd(TASK_TIM,DISABLE);
	
	TIM_TimeBaseStructure.TIM_Period = SystemCoreClock / 10000 - 1;	//频率的计算：	72M/(Prescaler+1)/(Period+1)	 
	TIM_TimeBaseStructure.TIM_Prescaler = TASK_PERIOD - 1; //预分频为0，实际计算时为0+1（都需加上1）
	TIM_TimeBaseStructure.TIM_ClockDivision=0;//时钟分频为0，则仍为倍频后的时钟频率
	TIM_TimeBaseStructure.TIM_CounterMode=TIM_CounterMode_Up;
	TIM_TimeBaseStructure.TIM_RepetitionCounter = 0;                    //重复溢出中断
	TIM_TimeBaseInit(TASK_TIM,&TIM_TimeBaseStructure);
	TIM_ARRPreloadConfig(TASK_TIM,ENABLE);
	TIM_TimeBaseInit(TASK_TIM, &TIM_TimeBaseStructure);  
	TIM_ITConfig(TASK_TIM,TIM_IT_Update,ENABLE);
	// 定义中断优先级
	NVIC_InitStructure.NVIC_IRQChannel = TIM5_IRQChannel;		//设定中断源为 
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;	//中断占优先级为2
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;			//副优先级为0
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;				//使能中断
	NVIC_Init(&NVIC_InitStructure);
	
	TaskTimerIsr = 0;
}

//------------------------------------------------------------------
//    Name			TIM5_IRQHandler	
//    Arg			void
//    Return		void
//    Description	TIM5中断函数
//    Author		<EMAIL>
//    Date			2018-10-19
//------------------------------------------------------------------
void TIM5_IRQHandler(void)
{
#ifdef SYSTEM_RUN_MODE_BOOT
	if(SystemRunMode == 0)
#endif
	{
		TIM5->SR = 0;// 清除中断标志
		if(TaskTimerIsr)
		{
			TaskTimerIsr();
		}	
	}
#ifdef SYSTEM_RUN_MODE_BOOT
	else
	{
		// 用户模式
		((void (*)(void))(*(u32*)(APK_CODE_ADDR + 0x108)))();
	}
#endif
}

//------------------------------------------------------------------
//    Name			OnChipFlashWritePageData	
//    Arg			addr: 数据写入的flash地址
//					ptr_data: 数据的指针
//					len: 32位数据的个数
//    Return		
//    Description	1. 数据长度len可以大于ON_CHIP_FLASH_PAGE_SIZE/4；
//					2. addr必须是整数倍的ON_CHIP_FLASH_PAGE_SIZE；
//    Author		<EMAIL>
//    Date			2018-10-21
//------------------------------------------------------------------
void OnChipFlashWritePageData(u32 addr,u32 *ptr_data,u32 len)
{
	u32 page_num,i;
	u16 *ptr;
	addr |= 0x8000000;
	if(len <= 0)
	{
		// 没有数据直接返回
		return ;
	}
	page_num = len / (ON_CHIP_FLASH_PAGE_SIZE/4);
	if(page_num * (ON_CHIP_FLASH_PAGE_SIZE/4) < len)
	{
		page_num++;
	}
	// 写数据
	while(page_num--)
	{
		FLASH_Unlock();
		FLASH_ClearFlag(FLASH_FLAG_BSY|FLASH_FLAG_EOP|FLASH_FLAG_PGERR);
		FLASH_ErasePage(addr);
		i = len;
		if(i > ON_CHIP_FLASH_PAGE_SIZE/4)
		{
			i = ON_CHIP_FLASH_PAGE_SIZE/4;
		}
		len -= i;
		while(i--)
		{
			ptr = (u16*)ptr_data;
			FLASH_ProgramWord(addr,ptr[0] | (ptr[1] << 16));
			ptr_data++;
			addr += 4;
		}
		FLASH_ClearFlag(FLASH_FLAG_BSY|FLASH_FLAG_EOP|FLASH_FLAG_PGERR);
		FLASH_Lock();
	}
}

//------------------------------------------------------------------
//    Name			OnChipFlashReadData	
//    Arg			addr: 数据读出的flash地址
//					ptr_data: 数据的指针
//					len: 32位数据的个数
//    Return		
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-21
//------------------------------------------------------------------
void OnChipFlashReadData(u32 addr,u32 *ptr_data,u32 len)
{
	u32 *ptr;
	addr |= 0x8000000;
	ptr = (u32*)addr;
	while(len--)
	{
		*ptr_data++ = *ptr++;
	}
}

//------------------------------------------------------------------
//    Name			HardFaultException	
//    Arg			
//    Return		
//    Description	
//    Author		<EMAIL>
//    Date			2018-10-21
//------------------------------------------------------------------
void HardFaultException(void)
{
#ifdef SYSTEM_RUN_MODE_BOOT
	if(SystemRunMode == 0)
#endif
	{
		LCD_SelectFont((void*)&FONT_32);
		LCD_Clear(0);
		LCD_PutStrCenter(0,LCD_V_PIXELS / 4,LCD_H_PIXELS - 1,LCD_V_PIXELS / 2 - 1,"HardFaultException",RED,0);
		LCD_PutStrCenter(0,LCD_V_PIXELS / 2 - 1,LCD_H_PIXELS - 1,LCD_V_PIXELS * 3 / 4 - 1,"Please reset",RED,0);
		LCD_SetBright(100);
		while(1);
	}
#ifdef SYSTEM_RUN_MODE_BOOT
	else
	{
		// 用户模式
		((void (*)(void))(*(u32*)(APK_CODE_ADDR + 0x0c)))();
	}
#endif
}

//------------------------------------------------------------------
//    Name			SetCpuMsp	
//    Arg			addr: 栈顶地址
//    Return		
//    Description	设置栈顶地址
//    Author		<EMAIL>
//    Date			2018-10-31
//------------------------------------------------------------------
__asm void SetCpuMsp(u32 addr) 
{
    MSR MSP, r0
    BX r14
}