<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Obj\STM32F103\apk.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Obj\STM32F103\apk.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060528: Last Updated: Thu Jul 10 14:11:13 2025
<BR><P>
<H3>Maximum Stack Usage =       1924 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
Apk_Main &rArr; DEBUG_SetPara &rArr; CMD_MainTask &rArr;  CMD_MainTask (Cycle)
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[131]">__WFI</a>
 <LI><a href="#[132]">__WFE</a>
 <LI><a href="#[133]">__SEV</a>
 <LI><a href="#[134]">__ISB</a>
 <LI><a href="#[135]">__DSB</a>
 <LI><a href="#[136]">__DMB</a>
 <LI><a href="#[137]">__SVC</a>
 <LI><a href="#[138]">__MRS_CONTROL</a>
 <LI><a href="#[139]">__MSR_CONTROL</a>
 <LI><a href="#[13a]">__MRS_PSP</a>
 <LI><a href="#[13b]">__MSR_PSP</a>
 <LI><a href="#[13c]">__MRS_MSP</a>
 <LI><a href="#[13d]">__MSR_MSP</a>
 <LI><a href="#[e9]">__RESETPRIMASK</a>
 <LI><a href="#[13e]">__SETPRIMASK</a>
 <LI><a href="#[13f]">__READ_PRIMASK</a>
 <LI><a href="#[140]">__SETFAULTMASK</a>
 <LI><a href="#[141]">__RESETFAULTMASK</a>
 <LI><a href="#[142]">__READ_FAULTMASK</a>
 <LI><a href="#[143]">__BASEPRICONFIG</a>
 <LI><a href="#[144]">__GetBASEPRI</a>
 <LI><a href="#[145]">__REV_HalfWord</a>
 <LI><a href="#[146]">__REV_Word</a>
 <LI><a href="#[73]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[76]">CMD_MainTask</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[76]">CMD_MainTask</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1f]">ADC1_2_IRQHandler</a> from stm32f10x_it.o(i.ADC1_2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3c]">ADC3_IRQHandler</a> from stm32f10x_it.o(i.ADC3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[4e]">APK_Ctrl</a> from apk.o(i.APK_Ctrl) referenced from apk.o(i.APK_Init)
 <LI><a href="#[4d]">Apk_Main</a> from apk.o(i.Apk_Main) referenced from apk.o(i.APK_Init)
 <LI><a href="#[7]">BusFaultException</a> from stm32f10x_it.o(i.BusFaultException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[22]">CAN_RX1_IRQHandler</a> from stm32f10x_it.o(i.CAN_RX1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[23]">CAN_SCE_IRQHandler</a> from stm32f10x_it.o(i.CAN_SCE_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[18]">DMA1_Channel1_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[19]">DMA1_Channel2_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel3_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel4_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel5_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel6_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel6_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1e]">DMA1_Channel7_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel7_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[45]">DMA2_Channel1_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[46]">DMA2_Channel2_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[47]">DMA2_Channel3_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[48]">DMA2_Channel4_5_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[a]">DebugMonitor</a> from stm32f10x_it.o(i.DebugMonitor) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[13]">EXTI0_IRQHandler</a> from stm32f10x_it.o(i.EXTI0_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[35]">EXTI15_10_IRQHandler</a> from driver.o(i.EXTI15_10_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[14]">EXTI1_IRQHandler</a> from stm32f10x_it.o(i.EXTI1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[15]">EXTI2_IRQHandler</a> from stm32f10x_it.o(i.EXTI2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[16]">EXTI3_IRQHandler</a> from stm32f10x_it.o(i.EXTI3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[17]">EXTI4_IRQHandler</a> from stm32f10x_it.o(i.EXTI4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[24]">EXTI9_5_IRQHandler</a> from stm32f10x_it.o(i.EXTI9_5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[11]">FLASH_IRQHandler</a> from stm32f10x_it.o(i.FLASH_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3d]">FSMC_IRQHandler</a> from stm32f10x_it.o(i.FSMC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[5]">HardFaultException</a> from driver.o(i.HardFaultException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2d]">I2C1_ER_IRQHandler</a> from stm32f10x_it.o(i.I2C1_ER_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2c]">I2C1_EV_IRQHandler</a> from stm32f10x_it.o(i.I2C1_EV_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2f]">I2C2_ER_IRQHandler</a> from stm32f10x_it.o(i.I2C2_ER_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2e]">I2C2_EV_IRQHandler</a> from stm32f10x_it.o(i.I2C2_EV_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[4f]">IR_Decode</a> from ir.o(i.IR_Decode) referenced from ir.o(i.IR_Init)
 <LI><a href="#[6]">MemManageException</a> from stm32f10x_it.o(i.MemManageException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[4]">NMIException</a> from stm32f10x_it.o(i.NMIException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[e]">PVD_IRQHandler</a> from stm32f10x_it.o(i.PVD_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[b]">PendSVC</a> from stm32f10x_it.o(i.PendSVC) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[12]">RCC_IRQHandler</a> from stm32f10x_it.o(i.RCC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[36]">RTCAlarm_IRQHandler</a> from stm32f10x_it.o(i.RTCAlarm_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[10]">RTC_IRQHandler</a> from stm32f10x_it.o(i.RTC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3]">Reset_Handler</a> from stm32f10x_vector.o(.text) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3e]">SDIO_IRQHandler</a> from stm32f10x_it.o(i.SDIO_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[30]">SPI1_IRQHandler</a> from stm32f10x_it.o(i.SPI1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[31]">SPI2_IRQHandler</a> from stm32f10x_it.o(i.SPI2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[40]">SPI3_IRQHandler</a> from stm32f10x_it.o(i.SPI3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[9]">SVCHandler</a> from stm32f10x_it.o(i.SVCHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[c]">SysTickHandler</a> from stm32f10x_it.o(i.SysTickHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[49]">SystemInit</a> from main.o(i.SystemInit) referenced from stm32f10x_vector.o(.text)
 <LI><a href="#[f]">TAMPER_IRQHandler</a> from stm32f10x_it.o(i.TAMPER_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[50]">TASK_Periodicity</a> from task.o(i.TASK_Periodicity) referenced from task.o(i.TASK_Init)
 <LI><a href="#[25]">TIM1_BRK_IRQHandler</a> from stm32f10x_it.o(i.TIM1_BRK_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[28]">TIM1_CC_IRQHandler</a> from stm32f10x_it.o(i.TIM1_CC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[27]">TIM1_TRG_COM_IRQHandler</a> from stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[26]">TIM1_UP_IRQHandler</a> from driver.o(i.TIM1_UP_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[29]">TIM2_IRQHandler</a> from stm32f10x_it.o(i.TIM2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2a]">TIM3_IRQHandler</a> from stm32f10x_it.o(i.TIM3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2b]">TIM4_IRQHandler</a> from stm32f10x_it.o(i.TIM4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3f]">TIM5_IRQHandler</a> from driver.o(i.TIM5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[43]">TIM6_IRQHandler</a> from stm32f10x_it.o(i.TIM6_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[44]">TIM7_IRQHandler</a> from stm32f10x_it.o(i.TIM7_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[38]">TIM8_BRK_IRQHandler</a> from stm32f10x_it.o(i.TIM8_BRK_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3b]">TIM8_CC_IRQHandler</a> from stm32f10x_it.o(i.TIM8_CC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3a]">TIM8_TRG_COM_IRQHandler</a> from stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[39]">TIM8_UP_IRQHandler</a> from stm32f10x_it.o(i.TIM8_UP_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[41]">UART4_IRQHandler</a> from stm32f10x_it.o(i.UART4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[42]">UART5_IRQHandler</a> from stm32f10x_it.o(i.UART5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[51]">UART_RxdIsr</a> from uart.o(i.UART_RxdIsr) referenced from uart.o(i.UART_Init)
 <LI><a href="#[52]">UART_TxdIsr</a> from uart.o(i.UART_TxdIsr) referenced from uart.o(i.UART_Init)
 <LI><a href="#[32]">USART1_IRQHandler</a> from stm32f10x_it.o(i.USART1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[33]">USART2_IRQHandler</a> from driver.o(i.USART2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[34]">USART3_IRQHandler</a> from stm32f10x_it.o(i.USART3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[37]">USBWakeUp_IRQHandler</a> from stm32f10x_it.o(i.USBWakeUp_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[20]">USB_HP_CAN_TX_IRQHandler</a> from stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[21]">USB_LP_CAN_RX0_IRQHandler</a> from stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[8]">UsageFaultException</a> from stm32f10x_it.o(i.UsageFaultException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[d]">WWDG_IRQHandler</a> from stm32f10x_it.o(i.WWDG_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[53]">XFLASH_UartRxdIsr</a> from xflash.o(i.XFLASH_UartRxdIsr) referenced from xflash.o(i.XFLASH_GetDataFromUart)
 <LI><a href="#[5b]">__main</a> from __main.o(!!!main) referenced from stm32f10x_vector.o(.text)
 <LI><a href="#[4c]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[4b]">_sputc</a> from _sputc.o(.text) referenced from noretval__2sprintf.o(.text)
 <LI><a href="#[59]">changeFun_DacSetValue0</a> from debug.o(i.changeFun_DacSetValue0) referenced from debug.o(.constdata)
 <LI><a href="#[5a]">changeFun_DacSetValue1</a> from debug.o(i.changeFun_DacSetValue1) referenced from debug.o(.constdata)
 <LI><a href="#[55]">changeFun_Duty</a> from debug.o(i.changeFun_Duty) referenced 3 times from debug.o(.constdata)
 <LI><a href="#[58]">changeFun_LcdBkLight</a> from debug.o(i.changeFun_LcdBkLight) referenced from debug.o(.constdata)
 <LI><a href="#[57]">changeFun_PwmDead</a> from debug.o(i.changeFun_PwmDead) referenced from debug.o(.constdata)
 <LI><a href="#[56]">changeFun_PwmFreq</a> from debug.o(i.changeFun_PwmFreq) referenced from debug.o(.constdata)
 <LI><a href="#[54]">changeFun_RunState</a> from debug.o(i.changeFun_RunState) referenced from debug.o(.constdata)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[5b]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[5c]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[5e]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[10d]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[10e]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[5f]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[10f]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[60]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[70]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[110]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[65]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[111]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[112]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[113]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[114]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[115]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[116]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[117]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[118]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[119]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[11a]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[11b]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[11c]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[11d]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[11e]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[11f]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[120]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[121]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[122]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[123]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[124]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[125]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[6a]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[126]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[127]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[128]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[129]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[12a]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[12b]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[12c]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[5d]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[12d]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[62]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[64]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[12e]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[66]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 132 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; TASK_Init &rArr; APK_Init &rArr; UART_Init &rArr; Uart2Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[12f]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[74]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[69]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[130]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[6b]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[131]"></a>__WFI</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[132]"></a>__WFE</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[133]"></a>__SEV</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[134]"></a>__ISB</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[135]"></a>__DSB</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[136]"></a>__DMB</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[137]"></a>__SVC</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[138]"></a>__MRS_CONTROL</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[139]"></a>__MSR_CONTROL</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[13a]"></a>__MRS_PSP</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[13b]"></a>__MSR_PSP</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[13c]"></a>__MRS_MSP</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[13d]"></a>__MSR_MSP</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[e9]"></a>__RESETPRIMASK</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_RESETPRIMASK
</UL>

<P><STRONG><a name="[13e]"></a>__SETPRIMASK</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[13f]"></a>__READ_PRIMASK</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[140]"></a>__SETFAULTMASK</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[141]"></a>__RESETFAULTMASK</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[142]"></a>__READ_FAULTMASK</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[143]"></a>__BASEPRICONFIG</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[144]"></a>__GetBASEPRI</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[145]"></a>__REV_HalfWord</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[146]"></a>__REV_Word</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, cortexm3_macro.o(.text), UNUSED)

<P><STRONG><a name="[3]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_vector.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, stm32f10x_vector.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[6d]"></a>__2sprintf</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, noretval__2sprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[6f]"></a>__printf</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, __printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[61]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[10c]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFONT_SeleFont
</UL>

<P><STRONG><a name="[147]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[148]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[149]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[14a]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[14b]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[14c]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[71]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[6e]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>

<P><STRONG><a name="[4b]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> noretval__2sprintf.o(.text)
</UL>
<P><STRONG><a name="[14d]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[14e]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[63]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[68]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[6c]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[14f]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[150]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[151]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[1f]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.ADC1_2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>ADC3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.ADC3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[8e]"></a>ADC_AutoInjectedConvCmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[8f]"></a>ADC_Cmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f10x_adc.o(i.ADC_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[93]"></a>ADC_GetCalibrationStatus</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f10x_adc.o(i.ADC_GetCalibrationStatus))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[91]"></a>ADC_GetResetCalibrationStatus</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[8b]"></a>ADC_Init</STRONG> (Thumb, 70 bytes, Stack size 12 bytes, stm32f10x_adc.o(i.ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[8d]"></a>ADC_InjectedChannelConfig</STRONG> (Thumb, 130 bytes, Stack size 20 bytes, stm32f10x_adc.o(i.ADC_InjectedChannelConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ADC_InjectedChannelConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[8c]"></a>ADC_InjectedSequencerLengthConfig</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_InjectedSequencerLengthConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[90]"></a>ADC_ResetCalibration</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_adc.o(i.ADC_ResetCalibration))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[94]"></a>ADC_SoftwareStartConvCmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[92]"></a>ADC_StartCalibration</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_adc.o(i.ADC_StartCalibration))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[75]"></a>APK_Common</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, apk.o(i.APK_Common))
<BR><BR>[Stack]<UL><LI>Max Depth = 1828 + Unknown Stack Size
<LI>Call Chain = APK_Common &rArr; CMD_MainTask &rArr;  CMD_MainTask (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[100]"></a>APK_Continuous</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, apk.o(i.APK_Continuous))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = APK_Continuous
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Run
</UL>

<P><STRONG><a name="[4e]"></a>APK_Ctrl</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, apk.o(i.APK_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = APK_Ctrl
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_VoltCurrCalc
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Periodicity
</UL>
<BR>[Address Reference Count : 1]<UL><LI> apk.o(i.APK_Init)
</UL>
<P><STRONG><a name="[78]"></a>APK_Init</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, apk.o(i.APK_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = APK_Init &rArr; UART_Init &rArr; Uart2Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFONT_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendStr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TP_Init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedInit
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBright
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IR_Init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DacInit
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_Init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Init
</UL>

<P><STRONG><a name="[87]"></a>APK_Periodicity</STRONG> (Thumb, 48 bytes, Stack size 4 bytes, apk.o(i.APK_Periodicity))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = APK_Periodicity &rArr; APK_Ctrl
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Ctrl
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Periodicity
</UL>

<P><STRONG><a name="[77]"></a>APK_VoltCurrCalc</STRONG> (Thumb, 172 bytes, Stack size 0 bytes, apk.o(i.APK_VoltCurrCalc))
<BR><BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Ctrl
</UL>

<P><STRONG><a name="[80]"></a>AdcInit</STRONG> (Thumb, 498 bytes, Stack size 32 bytes, driver.o(i.AdcInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = AdcInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_StartCalibration
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_SoftwareStartConvCmd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ResetCalibration
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_InjectedSequencerLengthConfig
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_InjectedChannelConfig
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetResetCalibrationStatus
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetCalibrationStatus
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Cmd
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AutoInjectedConvCmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ADCCLKConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[4d]"></a>Apk_Main</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, apk.o(i.Apk_Main))
<BR><BR>[Stack]<UL><LI>Max Depth = 1924 + Unknown Stack Size
<LI>Call Chain = Apk_Main &rArr; DEBUG_SetPara &rArr; CMD_MainTask &rArr;  CMD_MainTask (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFONT_SeleFont
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrCenter
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_SetPara
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_InitPara
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Common
</UL>
<BR>[Address Reference Count : 1]<UL><LI> apk.o(i.APK_Init)
</UL>
<P><STRONG><a name="[7]"></a>BusFaultException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.BusFaultException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = BusFaultException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN_RX1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.CAN_RX1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CAN_RX1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN_SCE_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.CAN_SCE_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CAN_SCE_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>CMD_Init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, cmd.o(i.CMD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMD_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_ClearRxdBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[76]"></a>CMD_MainTask</STRONG> (Thumb, 314 bytes, Stack size 16 bytes, cmd.o(i.CMD_MainTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 1820 + Unknown Stack Size
 + In Cycle
<LI>Call Chain = CMD_MainTask &rArr;  CMD_MainTask (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetRxdFifoLen
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetRxdData
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackCheck
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_SetPara
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Common
</UL>

<P><STRONG><a name="[9c]"></a>CMD_PackCheck</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, cmd.o(i.CMD_PackCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CMD_PackCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>

<P><STRONG><a name="[9d]"></a>CMD_PackRun</STRONG> (Thumb, 274 bytes, Stack size 16 bytes, cmd.o(i.CMD_PackRun))
<BR><BR>[Stack]<UL><LI>Max Depth = 1804 + Unknown Stack Size
<LI>Call Chain = CMD_PackRun &rArr; XFLASH_GetDataFromUart &rArr; LCD_PutStrLeftTop &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_GenerateSystemReset
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendStr
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrCenter
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>

<P><STRONG><a name="[a0]"></a>CloclkInit</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, driver.o(i.CloclkInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CloclkInit &rArr; RCC_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SYSCLKConfig
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLConfig
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLCmd
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK2Config
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK1Config
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSEConfig
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HCLKConfig
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetSYSCLKSource
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_DeInit
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayInit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_SetLatency
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_PrefetchBufferCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ae]"></a>CommonConfig</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, driver.o(i.CommonConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CommonConfig &rArr; GPIO_PinRemapConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinRemapConfig
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetCpuId
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[be]"></a>DAC_Cmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f10x_dac.o(i.DAC_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DacInit
</UL>

<P><STRONG><a name="[bd]"></a>DAC_Init</STRONG> (Thumb, 46 bytes, Stack size 12 bytes, stm32f10x_dac.o(i.DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DAC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DacInit
</UL>

<P><STRONG><a name="[95]"></a>DEBUG_InitPara</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, debug.o(i.DEBUG_InitPara))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DEBUG_InitPara &rArr; InitFun &rArr; changeFun_RunState &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LoadData
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitFun
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[97]"></a>DEBUG_SetPara</STRONG> (Thumb, 7280 bytes, Stack size 88 bytes, debug.o(i.DEBUG_SetPara))
<BR><BR>[Stack]<UL><LI>Max Depth = 1908 + Unknown Stack Size
<LI>Call Chain = DEBUG_SetPara &rArr; CMD_MainTask &rArr;  CMD_MainTask (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdatePeriod
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateOneTime
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveData
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuStrLen
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuDisplayNumber
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetKeyValue
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBar
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrLeftTop
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[18]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel6_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel7_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA2_Channel4_5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel4_5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>DacInit</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, driver.o(i.DacInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DacInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Cmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[a]"></a>DebugMonitor</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DebugMonitor))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DebugMonitor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[cb]"></a>DelayHalfUs</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, driver.o(i.DelayHalfUs))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetKeyValue
</UL>

<P><STRONG><a name="[ad]"></a>DelayInit</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, driver.o(i.DelayInit))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[ce]"></a>DelayMs</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, driver.o(i.DelayMs))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481Init
</UL>

<P><STRONG><a name="[f6]"></a>DelayUs</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, driver.o(i.DelayUs))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_Init
</UL>

<P><STRONG><a name="[13]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, driver.o(i.EXTI15_10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI15_10_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetITStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI9_5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI9_5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[bf]"></a>EXTI_GetITStatus</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f10x_exti.o(i.EXTI_GetITStatus))
<BR><BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[d1]"></a>EXTI_Init</STRONG> (Thumb, 174 bytes, Stack size 0 bytes, stm32f10x_exti.o(i.EXTI_Init))
<BR><BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IrInit
</UL>

<P><STRONG><a name="[c7]"></a>FLASH_ClearFlag</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashWriteStart
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashWriteEnd
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnChipFlashWritePageData
</UL>

<P><STRONG><a name="[c0]"></a>FLASH_ErasePage</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f10x_flash.o(i.FLASH_ErasePage))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = FLASH_ErasePage &rArr; FLASH_WaitForLastOperation &rArr; delay
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashWriteStart
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnChipFlashWritePageData
</UL>

<P><STRONG><a name="[c3]"></a>FLASH_GetStatus</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_GetStatus))
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>

<P><STRONG><a name="[11]"></a>FLASH_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.FLASH_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FLASH_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[c8]"></a>FLASH_Lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_Lock))
<BR><BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashWriteEnd
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnChipFlashWritePageData
</UL>

<P><STRONG><a name="[a5]"></a>FLASH_PrefetchBufferCmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[c2]"></a>FLASH_ProgramWord</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32f10x_flash.o(i.FLASH_ProgramWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = FLASH_ProgramWord &rArr; FLASH_WaitForLastOperation &rArr; delay
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashWriteData
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnChipFlashWritePageData
</UL>

<P><STRONG><a name="[a6]"></a>FLASH_SetLatency</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_SetLatency))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[ca]"></a>FLASH_Unlock</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_Unlock))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashWriteStart
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnChipFlashWritePageData
</UL>

<P><STRONG><a name="[c1]"></a>FLASH_WaitForLastOperation</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, stm32f10x_flash.o(i.FLASH_WaitForLastOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = FLASH_WaitForLastOperation &rArr; delay
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_GetStatus
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramWord
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ErasePage
</UL>

<P><STRONG><a name="[3d]"></a>FSMC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.FSMC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FSMC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[e7]"></a>FlashReadData</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, debug.o(i.FlashReadData))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LoadData
</UL>

<P><STRONG><a name="[c5]"></a>FlashWriteData</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, debug.o(i.FlashWriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = FlashWriteData &rArr; FLASH_ProgramWord &rArr; FLASH_WaitForLastOperation &rArr; delay
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramWord
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveData
</UL>

<P><STRONG><a name="[c6]"></a>FlashWriteEnd</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, debug.o(i.FlashWriteEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FlashWriteEnd
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ClearFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveData
</UL>

<P><STRONG><a name="[c9]"></a>FlashWriteStart</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, debug.o(i.FlashWriteStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = FlashWriteStart &rArr; FLASH_ErasePage &rArr; FLASH_WaitForLastOperation &rArr; delay
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ErasePage
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ClearFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveData
</UL>

<P><STRONG><a name="[d0]"></a>GPIO_EXTILineConfig</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, stm32f10x_gpio.o(i.GPIO_EXTILineConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = GPIO_EXTILineConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IrInit
</UL>

<P><STRONG><a name="[8a]"></a>GPIO_Init</STRONG> (Thumb, 276 bytes, Stack size 24 bytes, stm32f10x_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;changeFun_RunState
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_InitGpio
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IrInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedInit
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DacInit
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[b0]"></a>GPIO_PinRemapConfig</STRONG> (Thumb, 106 bytes, Stack size 20 bytes, stm32f10x_gpio.o(i.GPIO_PinRemapConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_PinRemapConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommonConfig
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_InitGpio
</UL>

<P><STRONG><a name="[b1]"></a>GetCpuId</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, driver.o(i.GetCpuId))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GetCpuId
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommonConfig
</UL>

<P><STRONG><a name="[f9]"></a>GetDelayTimeFlag</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, driver.o(i.GetDelayTimeFlag))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
</UL>

<P><STRONG><a name="[b8]"></a>GetKeyValue</STRONG> (Thumb, 360 bytes, Stack size 8 bytes, debug.o(i.GetKeyValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GetKeyValue
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayHalfUs
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_SetPara
</UL>

<P><STRONG><a name="[5]"></a>HardFaultException</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, driver.o(i.HardFaultException))
<BR><BR>[Stack]<UL><LI>Max Depth = 1412<LI>Call Chain = HardFaultException &rArr; LCD_PutStrCenter &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SelectFont
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBright
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrCenter
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C1_ER_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C1_ER_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C1_EV_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C1_EV_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C2_ER_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C2_ER_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C2_EV_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C2_EV_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[cd]"></a>ILI9481Init</STRONG> (Thumb, 1308 bytes, Stack size 4 bytes, driver.o(i.ILI9481Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = ILI9481Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayMs
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[db]"></a>ILI9481SetDisplayWindow</STRONG> (Thumb, 276 bytes, Stack size 12 bytes, driver.o(i.ILI9481SetDisplayWindow))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ILI9481SetDisplayWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBar
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>

<P><STRONG><a name="[dc]"></a>ILI9481WriteRamPrepare</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, driver.o(i.ILI9481WriteRamPrepare))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBar
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>

<P><STRONG><a name="[4f]"></a>IR_Decode</STRONG> (Thumb, 850 bytes, Stack size 0 bytes, ir.o(i.IR_Decode))
<BR>[Address Reference Count : 1]<UL><LI> ir.o(i.IR_Init)
</UL>
<P><STRONG><a name="[7b]"></a>IR_Init</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ir.o(i.IR_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = IR_Init &rArr; IrInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IrInit
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[b3]"></a>InitFun</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, debug.o(i.InitFun))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = InitFun &rArr; changeFun_RunState &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;changeFun_RunState
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;changeFun_PwmFreq
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;changeFun_LcdBkLight
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;changeFun_Duty
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;changeFun_DacSetValue1
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;changeFun_DacSetValue0
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_InitPara
</UL>

<P><STRONG><a name="[cf]"></a>IrInit</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, driver.o(i.IrInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = IrInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_EXTILineConfig
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IR_Init
</UL>

<P><STRONG><a name="[d6]"></a>LCD_BackLightInit</STRONG> (Thumb, 198 bytes, Stack size 40 bytes, driver.o(i.LCD_BackLightInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = LCD_BackLightInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4PreloadConfig
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4Init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBright
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[84]"></a>LCD_Clear</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, lcd.o(i.LCD_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LCD_Clear &rArr; LCD_WriteConst
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteConst
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481WriteRamPrepare
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481SetDisplayWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFaultException
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[de]"></a>LCD_DrawPoint</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, lcd.o(i.LCD_DrawPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = LCD_DrawPoint &rArr; ILI9481SetDisplayWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481WriteRamPrepare
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481SetDisplayWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutHanzi
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutChar
</UL>

<P><STRONG><a name="[df]"></a>LCD_DrawProgress</STRONG> (Thumb, 340 bytes, Stack size 40 bytes, lcd.o(i.LCD_DrawProgress))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = LCD_DrawProgress &rArr; LCD_SetBar &rArr; LCD_WriteConst
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBar
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[83]"></a>LCD_Init</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, lcd.o(i.LCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = LCD_Init &rArr; LCD_BackLightInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SelectFont
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_InitGpio
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481Init
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[e0]"></a>LCD_InitGpio</STRONG> (Thumb, 226 bytes, Stack size 8 bytes, driver.o(i.LCD_InitGpio))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LCD_InitGpio &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinRemapConfig
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[e1]"></a>LCD_PutChar</STRONG> (Thumb, 204 bytes, Stack size 1200 bytes, lcd.o(i.LCD_PutChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 1260<LI>Call Chain = LCD_PutChar &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SearchFont
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuDisplayNumber
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStr
</UL>

<P><STRONG><a name="[e3]"></a>LCD_PutHanzi</STRONG> (Thumb, 204 bytes, Stack size 1208 bytes, lcd.o(i.LCD_PutHanzi))
<BR><BR>[Stack]<UL><LI>Max Depth = 1268<LI>Call Chain = LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SearchFont
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStr
</UL>

<P><STRONG><a name="[e4]"></a>LCD_PutStr</STRONG> (Thumb, 304 bytes, Stack size 56 bytes, lcd.o(i.LCD_PutStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 1324<LI>Call Chain = LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutHanzi
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutChar
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrLeftTop
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrCenter
</UL>

<P><STRONG><a name="[98]"></a>LCD_PutStrCenter</STRONG> (Thumb, 100 bytes, Stack size 72 bytes, lcd.o(i.LCD_PutStrCenter))
<BR><BR>[Stack]<UL><LI>Max Depth = 1396<LI>Call Chain = LCD_PutStrCenter &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStr
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StrLen
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFaultException
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[b5]"></a>LCD_PutStrLeftTop</STRONG> (Thumb, 54 bytes, Stack size 80 bytes, lcd.o(i.LCD_PutStrLeftTop))
<BR><BR>[Stack]<UL><LI>Max Depth = 1404<LI>Call Chain = LCD_PutStrLeftTop &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStr
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_SetPara
</UL>

<P><STRONG><a name="[e2]"></a>LCD_SearchFont</STRONG> (Thumb, 442 bytes, Stack size 40 bytes, lcd.o(i.LCD_SearchFont))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = LCD_SearchFont &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadData
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutHanzi
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutChar
</UL>

<P><STRONG><a name="[cc]"></a>LCD_SelectFont</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, lcd.o(i.LCD_SelectFont))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFaultException
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[b4]"></a>LCD_SetBar</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, lcd.o(i.LCD_SetBar))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = LCD_SetBar &rArr; LCD_WriteConst
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteConst
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481WriteRamPrepare
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9481SetDisplayWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawProgress
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_SetPara
</UL>

<P><STRONG><a name="[85]"></a>LCD_SetBright</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, driver.o(i.LCD_SetBright))
<BR><BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;changeFun_LcdBkLight
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFaultException
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[dd]"></a>LCD_WriteConst</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, driver.o(i.LCD_WriteConst))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LCD_WriteConst
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBar
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>

<P><STRONG><a name="[7f]"></a>LedInit</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, driver.o(i.LedInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LedInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[b2]"></a>LoadData</STRONG> (Thumb, 322 bytes, Stack size 12 bytes, debug.o(i.LoadData))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LoadData
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashReadData
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveData
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_InitPara
</UL>

<P><STRONG><a name="[107]"></a>LrcCalc</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, common.o(i.LrcCalc))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LrcCalc
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[6]"></a>MemManageException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.MemManageException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MemManageException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[b7]"></a>MenuDisplayNumber</STRONG> (Thumb, 392 bytes, Stack size 80 bytes, debug.o(i.MenuDisplayNumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 1340<LI>Call Chain = MenuDisplayNumber &rArr; LCD_PutChar &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutChar
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_SetPara
</UL>

<P><STRONG><a name="[b6]"></a>MenuStrLen</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, debug.o(i.MenuStrLen))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_SetPara
</UL>

<P><STRONG><a name="[4]"></a>NMIException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.NMIException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NMIException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[9f]"></a>NVIC_GenerateSystemReset</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_nvic.o(i.NVIC_GenerateSystemReset))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
</UL>

<P><STRONG><a name="[d2]"></a>NVIC_Init</STRONG> (Thumb, 150 bytes, Stack size 20 bytes, stm32f10x_nvic.o(i.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IrInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[af]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommonConfig
</UL>

<P><STRONG><a name="[e8]"></a>NVIC_RESETPRIMASK</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f10x_nvic.o(i.NVIC_RESETPRIMASK))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = NVIC_RESETPRIMASK
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__RESETPRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Run
</UL>

<P><STRONG><a name="[10a]"></a>Num2Str</STRONG> (Thumb, 350 bytes, Stack size 24 bytes, common.o(i.Num2Str))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Num2Str
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[108]"></a>OnChipFlashReadData</STRONG> (Thumb, 24 bytes, Stack size 12 bytes, driver.o(i.OnChipFlashReadData))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = OnChipFlashReadData
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[ea]"></a>OnChipFlashWritePageData</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, driver.o(i.OnChipFlashWritePageData))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OnChipFlashWritePageData &rArr; FLASH_ProgramWord &rArr; FLASH_WaitForLastOperation &rArr; delay
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramWord
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ErasePage
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ClearFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[e]"></a>PVD_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.PVD_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PVD_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PendSVC</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.PendSVC))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PendSVC
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>PwmInit</STRONG> (Thumb, 546 bytes, Stack size 56 bytes, driver.o(i.PwmInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = PwmInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_BDTRConfig
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4PreloadConfig
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3PreloadConfig
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2PreloadConfig
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2Init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1PreloadConfig
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1Init
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[89]"></a>RCC_ADCCLKConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_ADCCLKConfig))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[bc]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IrInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DacInit
</UL>

<P><STRONG><a name="[88]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_InitGpio
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IrInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DacInit
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcInit
</UL>

<P><STRONG><a name="[a1]"></a>RCC_DeInit</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[104]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 192 bytes, Stack size 12 bytes, stm32f10x_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[a4]"></a>RCC_GetFlagStatus</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f10x_rcc.o(i.RCC_GetFlagStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[ac]"></a>RCC_GetSYSCLKSource</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_GetSYSCLKSource))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[a7]"></a>RCC_HCLKConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_HCLKConfig))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[a2]"></a>RCC_HSEConfig</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_HSEConfig))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[12]"></a>RCC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.RCC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[a9]"></a>RCC_PCLK1Config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PCLK1Config))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[a8]"></a>RCC_PCLK2Config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PCLK2Config))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[a3]"></a>RCC_PLLCmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PLLCmd))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[aa]"></a>RCC_PLLConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PLLConfig))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[ab]"></a>RCC_SYSCLKConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_SYSCLKConfig))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[36]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.RTCAlarm_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RTCAlarm_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RTC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.RTC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RTC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SDIO_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SDIO_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDIO_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SPI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SPI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>SPI3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SPI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[fc]"></a>SPI_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_spi.o(i.SPI_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
</UL>

<P><STRONG><a name="[f3]"></a>SPI_FLASH_EraseSector</STRONG> (Thumb, 198 bytes, Stack size 16 bytes, driver.o(i.SPI_FLASH_EraseSector))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SPI_FLASH_EraseSector &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_WriteData
</UL>

<P><STRONG><a name="[79]"></a>SPI_FLASH_Init</STRONG> (Thumb, 188 bytes, Stack size 8 bytes, driver.o(i.SPI_FLASH_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SPI_FLASH_Init &rArr; Spi3Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadId
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[e6]"></a>SPI_FLASH_ReadData</STRONG> (Thumb, 230 bytes, Stack size 16 bytes, driver.o(i.SPI_FLASH_ReadData))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFONT_GetFontInf
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_WriteData
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SearchFont
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[f7]"></a>SPI_FLASH_ReadId</STRONG> (Thumb, 336 bytes, Stack size 16 bytes, driver.o(i.SPI_FLASH_ReadId))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SPI_FLASH_ReadId &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_Init
</UL>

<P><STRONG><a name="[f4]"></a>SPI_FLASH_WaitBusy</STRONG> (Thumb, 104 bytes, Stack size 4 bytes, driver.o(i.SPI_FLASH_WaitBusy))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetDelayTimeUs
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetDelayTimeFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WriteSector
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadId
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadData
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_EraseSector
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_Init
</UL>

<P><STRONG><a name="[fa]"></a>SPI_FLASH_WriteSector</STRONG> (Thumb, 402 bytes, Stack size 24 bytes, driver.o(i.SPI_FLASH_WriteSector))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SPI_FLASH_WriteSector &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_WriteData
</UL>

<P><STRONG><a name="[fb]"></a>SPI_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stm32f10x_spi.o(i.SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
</UL>

<P><STRONG><a name="[9]"></a>SVCHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SVCHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SVCHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[b9]"></a>SaveData</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, debug.o(i.SaveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SaveData &rArr; FlashWriteData &rArr; FLASH_ProgramWord &rArr; FLASH_WaitForLastOperation &rArr; delay
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LoadData
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashWriteStart
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashWriteEnd
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashWriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_SetPara
</UL>

<P><STRONG><a name="[f8]"></a>SetDelayTimeUs</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, driver.o(i.SetDelayTimeUs))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WaitBusy
</UL>

<P><STRONG><a name="[f5]"></a>Spi3Init</STRONG> (Thumb, 232 bytes, Stack size 32 bytes, driver.o(i.Spi3Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Spi3Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinRemapConfig
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Cmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TP_Init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_Init
</UL>

<P><STRONG><a name="[e5]"></a>StrLen</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, common.o(i.StrLen))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrCenter
</UL>

<P><STRONG><a name="[c]"></a>SysTickHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SysTickHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTickHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>SystemInit</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, main.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SystemInit
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayMs
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(.text)
</UL>
<P><STRONG><a name="[f]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TAMPER_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TAMPER_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[fd]"></a>TASK_Init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, task.o(i.TASK_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = TASK_Init &rArr; APK_Init &rArr; UART_Init &rArr; Uart2Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[50]"></a>TASK_Periodicity</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, task.o(i.TASK_Periodicity))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TASK_Periodicity &rArr; APK_Periodicity &rArr; APK_Ctrl
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Periodicity
</UL>
<BR>[Address Reference Count : 1]<UL><LI> task.o(i.TASK_Init)
</UL>
<P><STRONG><a name="[ff]"></a>TASK_Run</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, task.o(i.TASK_Run))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = TASK_Run &rArr; NVIC_RESETPRIMASK
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_RESETPRIMASK
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Continuous
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fe]"></a>TASK_TimerInit</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, driver.o(i.TASK_TimerInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TASK_TimerInit &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Init
</UL>

<P><STRONG><a name="[25]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM1_BRK_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_BRK_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM1_CC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_CC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_TRG_COM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, driver.o(i.TIM1_UP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_UP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM5_IRQHandler</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, driver.o(i.TIM5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIM6_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM6_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM7_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM7_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_BRK_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_BRK_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_CC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_CC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_TRG_COM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_UP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_UP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[d4]"></a>TIM_ARRPreloadConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ARRPreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IrInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[f1]"></a>TIM_BDTRConfig</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_BDTRConfig))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[d9]"></a>TIM_ClearFlag</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[d5]"></a>TIM_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Run
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IrInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[da]"></a>TIM_CtrlPWMOutputs</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_CtrlPWMOutputs))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[f2]"></a>TIM_ITConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ITConfig))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[eb]"></a>TIM_OC1Init</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, stm32f10x_tim.o(i.TIM_OC1Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC1Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[ec]"></a>TIM_OC1PreloadConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_OC1PreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[ed]"></a>TIM_OC2Init</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, stm32f10x_tim.o(i.TIM_OC2Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC2Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[ee]"></a>TIM_OC2PreloadConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_OC2PreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[ef]"></a>TIM_OC3Init</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f10x_tim.o(i.TIM_OC3Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC3Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[f0]"></a>TIM_OC3PreloadConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_OC3PreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[d7]"></a>TIM_OC4Init</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, stm32f10x_tim.o(i.TIM_OC4Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC4Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[d8]"></a>TIM_OC4PreloadConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_OC4PreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[d3]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_TimeBaseInit))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_TimerInit
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_BackLightInit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IrInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PwmInit
</UL>

<P><STRONG><a name="[86]"></a>TP_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, driver.o(i.TP_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = TP_Init &rArr; Spi3Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi3Init
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[41]"></a>UART4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.UART4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>UART5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.UART5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[99]"></a>UART_ClearRxdBuffer</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, uart.o(i.UART_ClearRxdBuffer))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_Init
</UL>

<P><STRONG><a name="[9a]"></a>UART_GetRxdData</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, uart.o(i.UART_GetRxdData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_GetRxdData
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>

<P><STRONG><a name="[9b]"></a>UART_GetRxdFifoLen</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, uart.o(i.UART_GetRxdFifoLen))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxdIsr
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_MainTask
</UL>

<P><STRONG><a name="[7c]"></a>UART_Init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, uart.o(i.UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = UART_Init &rArr; Uart2Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[51]"></a>UART_RxdIsr</STRONG> (Thumb, 50 bytes, Stack size 4 bytes, uart.o(i.UART_RxdIsr))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = UART_RxdIsr
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_GetRxdFifoLen
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart.o(i.UART_Init)
</UL>
<P><STRONG><a name="[102]"></a>UART_SendData</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, uart.o(i.UART_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = UART_SendData &rArr; UART_TxdIsr
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_TxdIsr
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendStr
</UL>

<P><STRONG><a name="[82]"></a>UART_SendStr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, uart.o(i.UART_SendStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_SendStr &rArr; UART_SendData &rArr; UART_TxdIsr
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[52]"></a>UART_TxdIsr</STRONG> (Thumb, 58 bytes, Stack size 12 bytes, uart.o(i.UART_TxdIsr))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = UART_TxdIsr
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendData
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart.o(i.UART_Init)
</UL>
<P><STRONG><a name="[32]"></a>USART1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART2_IRQHandler</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, driver.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[105]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
</UL>

<P><STRONG><a name="[106]"></a>USART_ITConfig</STRONG> (Thumb, 64 bytes, Stack size 20 bytes, stm32f10x_usart.o(i.USART_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
</UL>

<P><STRONG><a name="[103]"></a>USART_Init</STRONG> (Thumb, 150 bytes, Stack size 56 bytes, stm32f10x_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2Init
</UL>

<P><STRONG><a name="[37]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USBWakeUp_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBWakeUp_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>USB_HP_CAN_TX_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_HP_CAN_TX_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>USB_LP_CAN_RX0_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_LP_CAN_RX0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[101]"></a>Uart2Init</STRONG> (Thumb, 174 bytes, Stack size 32 bytes, driver.o(i.Uart2Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = Uart2Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Init
</UL>

<P><STRONG><a name="[ba]"></a>UpdateOneTime</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, debug.o(i.UpdateOneTime))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_SetPara
</UL>

<P><STRONG><a name="[bb]"></a>UpdatePeriod</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, debug.o(i.UpdatePeriod))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG_SetPara
</UL>

<P><STRONG><a name="[8]"></a>UsageFaultException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.UsageFaultException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UsageFaultException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>WWDG_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.WWDG_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WWDG_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[9e]"></a>XFLASH_GetDataFromUart</STRONG> (Thumb, 1604 bytes, Stack size 384 bytes, xflash.o(i.XFLASH_GetDataFromUart))
<BR><BR>[Stack]<UL><LI>Max Depth = 1788 + Unknown Stack Size
<LI>Call Chain = XFLASH_GetDataFromUart &rArr; LCD_PutStrLeftTop &rArr; LCD_PutStr &rArr; LCD_PutHanzi &rArr; LCD_SearchFont &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayMs
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_WriteData
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SendData
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_PutStrLeftTop
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawProgress
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Num2Str
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LrcCalc
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SelectFont
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadData
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnChipFlashWritePageData
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnChipFlashReadData
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayUs
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_PackRun
</UL>

<P><STRONG><a name="[53]"></a>XFLASH_UartRxdIsr</STRONG> (Thumb, 228 bytes, Stack size 0 bytes, xflash.o(i.XFLASH_UartRxdIsr))
<BR>[Address Reference Count : 1]<UL><LI> xflash.o(i.XFLASH_GetDataFromUart)
</UL>
<P><STRONG><a name="[109]"></a>XFLASH_WriteData</STRONG> (Thumb, 342 bytes, Stack size 56 bytes, xflash.o(i.XFLASH_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = XFLASH_WriteData &rArr; SPI_FLASH_WriteSector &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_WriteSector
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadData
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_EraseSector
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFLASH_GetDataFromUart
</UL>

<P><STRONG><a name="[10b]"></a>XFONT_GetFontInf</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, xfont.o(i.XFONT_GetFontInf))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = XFONT_GetFontInf &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FLASH_ReadData
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFONT_Init
</UL>

<P><STRONG><a name="[7a]"></a>XFONT_Init</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, xfont.o(i.XFONT_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = XFONT_Init &rArr; XFONT_GetFontInf &rArr; SPI_FLASH_ReadData &rArr; SPI_FLASH_WaitBusy
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFONT_GetFontInf
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFONT_SeleFont
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APK_Init
</UL>

<P><STRONG><a name="[96]"></a>XFONT_SeleFont</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, xfont.o(i.XFONT_SeleFont))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = XFONT_SeleFont &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XFONT_Init
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apk_Main
</UL>

<P><STRONG><a name="[59]"></a>changeFun_DacSetValue0</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, debug.o(i.changeFun_DacSetValue0))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitFun
</UL>
<BR>[Address Reference Count : 1]<UL><LI> debug.o(.constdata)
</UL>
<P><STRONG><a name="[5a]"></a>changeFun_DacSetValue1</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, debug.o(i.changeFun_DacSetValue1))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitFun
</UL>
<BR>[Address Reference Count : 1]<UL><LI> debug.o(.constdata)
</UL>
<P><STRONG><a name="[55]"></a>changeFun_Duty</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, debug.o(i.changeFun_Duty))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitFun
</UL>
<BR>[Address Reference Count : 1]<UL><LI> debug.o(.constdata)
</UL>
<P><STRONG><a name="[58]"></a>changeFun_LcdBkLight</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, debug.o(i.changeFun_LcdBkLight))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = changeFun_LcdBkLight
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetBright
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;changeFun_PwmFreq
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitFun
</UL>
<BR>[Address Reference Count : 1]<UL><LI> debug.o(.constdata)
</UL>
<P><STRONG><a name="[57]"></a>changeFun_PwmDead</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, debug.o(i.changeFun_PwmDead))
<BR>[Address Reference Count : 1]<UL><LI> debug.o(.constdata)
</UL>
<P><STRONG><a name="[56]"></a>changeFun_PwmFreq</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, debug.o(i.changeFun_PwmFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = changeFun_PwmFreq &rArr; changeFun_LcdBkLight
</UL>
<BR>[Calls]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;changeFun_LcdBkLight
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitFun
</UL>
<BR>[Address Reference Count : 1]<UL><LI> debug.o(.constdata)
</UL>
<P><STRONG><a name="[54]"></a>changeFun_RunState</STRONG> (Thumb, 148 bytes, Stack size 8 bytes, debug.o(i.changeFun_RunState))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = changeFun_RunState &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitFun
</UL>
<BR>[Address Reference Count : 1]<UL><LI> debug.o(.constdata)
</UL>
<P><STRONG><a name="[67]"></a>main</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 132 + Unknown Stack Size
<LI>Call Chain = main &rArr; TASK_Init &rArr; APK_Init &rArr; UART_Init &rArr; Uart2Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Run
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TASK_Init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommonConfig
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[c4]"></a>delay</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32f10x_flash.o(i.delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>

<P><STRONG><a name="[4c]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
