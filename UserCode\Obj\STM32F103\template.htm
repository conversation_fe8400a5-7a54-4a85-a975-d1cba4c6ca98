<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Obj\STM32F103\template.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Obj\STM32F103\template.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5050169: Last Updated: Sun Oct 21 12:15:49 2018
<BR><P>
<H3>Maximum Stack Usage =         32 bytes + Unknown(Functions without stacksize, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; LedInit &rArr; GPIO_Init
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[5c]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1f]">ADC1_2_IRQHandler</a> from stm32f10x_it.o(i.ADC1_2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3c]">ADC3_IRQHandler</a> from stm32f10x_it.o(i.ADC3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[7]">BusFaultException</a> from stm32f10x_it.o(i.BusFaultException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[22]">CAN_RX1_IRQHandler</a> from stm32f10x_it.o(i.CAN_RX1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[23]">CAN_SCE_IRQHandler</a> from stm32f10x_it.o(i.CAN_SCE_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[18]">DMA1_Channel1_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[19]">DMA1_Channel2_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel3_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel4_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel5_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel6_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel6_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[1e]">DMA1_Channel7_IRQHandler</a> from stm32f10x_it.o(i.DMA1_Channel7_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[45]">DMA2_Channel1_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[46]">DMA2_Channel2_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[47]">DMA2_Channel3_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[48]">DMA2_Channel4_5_IRQHandler</a> from stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[a]">DebugMonitor</a> from stm32f10x_it.o(i.DebugMonitor) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[13]">EXTI0_IRQHandler</a> from stm32f10x_it.o(i.EXTI0_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[35]">EXTI15_10_IRQHandler</a> from driver.o(i.EXTI15_10_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[14]">EXTI1_IRQHandler</a> from stm32f10x_it.o(i.EXTI1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[15]">EXTI2_IRQHandler</a> from stm32f10x_it.o(i.EXTI2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[16]">EXTI3_IRQHandler</a> from stm32f10x_it.o(i.EXTI3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[17]">EXTI4_IRQHandler</a> from stm32f10x_it.o(i.EXTI4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[24]">EXTI9_5_IRQHandler</a> from stm32f10x_it.o(i.EXTI9_5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[11]">FLASH_IRQHandler</a> from stm32f10x_it.o(i.FLASH_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3d]">FSMC_IRQHandler</a> from stm32f10x_it.o(i.FSMC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[5]">HardFaultException</a> from stm32f10x_it.o(i.HardFaultException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2d]">I2C1_ER_IRQHandler</a> from stm32f10x_it.o(i.I2C1_ER_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2c]">I2C1_EV_IRQHandler</a> from stm32f10x_it.o(i.I2C1_EV_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2f]">I2C2_ER_IRQHandler</a> from stm32f10x_it.o(i.I2C2_ER_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2e]">I2C2_EV_IRQHandler</a> from stm32f10x_it.o(i.I2C2_EV_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[6]">MemManageException</a> from stm32f10x_it.o(i.MemManageException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[4]">NMIException</a> from stm32f10x_it.o(i.NMIException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[e]">PVD_IRQHandler</a> from stm32f10x_it.o(i.PVD_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[b]">PendSVC</a> from stm32f10x_it.o(i.PendSVC) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[12]">RCC_IRQHandler</a> from stm32f10x_it.o(i.RCC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[36]">RTCAlarm_IRQHandler</a> from stm32f10x_it.o(i.RTCAlarm_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[10]">RTC_IRQHandler</a> from stm32f10x_it.o(i.RTC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3]">Reset_Handler</a> from stm32f10x_vector.o(.text) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3e]">SDIO_IRQHandler</a> from stm32f10x_it.o(i.SDIO_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[30]">SPI1_IRQHandler</a> from stm32f10x_it.o(i.SPI1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[31]">SPI2_IRQHandler</a> from stm32f10x_it.o(i.SPI2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[40]">SPI3_IRQHandler</a> from stm32f10x_it.o(i.SPI3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[9]">SVCHandler</a> from stm32f10x_it.o(i.SVCHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[c]">SysTickHandler</a> from stm32f10x_it.o(i.SysTickHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[49]">SystemInit</a> from main.o(i.SystemInit) referenced from stm32f10x_vector.o(.text)
 <LI><a href="#[f]">TAMPER_IRQHandler</a> from stm32f10x_it.o(i.TAMPER_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[25]">TIM1_BRK_IRQHandler</a> from stm32f10x_it.o(i.TIM1_BRK_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[28]">TIM1_CC_IRQHandler</a> from stm32f10x_it.o(i.TIM1_CC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[27]">TIM1_TRG_COM_IRQHandler</a> from stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[26]">TIM1_UP_IRQHandler</a> from driver.o(i.TIM1_UP_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[29]">TIM2_IRQHandler</a> from stm32f10x_it.o(i.TIM2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2a]">TIM3_IRQHandler</a> from stm32f10x_it.o(i.TIM3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[2b]">TIM4_IRQHandler</a> from stm32f10x_it.o(i.TIM4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3f]">TIM5_IRQHandler</a> from driver.o(i.TIM5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[43]">TIM6_IRQHandler</a> from stm32f10x_it.o(i.TIM6_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[44]">TIM7_IRQHandler</a> from stm32f10x_it.o(i.TIM7_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[38]">TIM8_BRK_IRQHandler</a> from stm32f10x_it.o(i.TIM8_BRK_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3b]">TIM8_CC_IRQHandler</a> from stm32f10x_it.o(i.TIM8_CC_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[3a]">TIM8_TRG_COM_IRQHandler</a> from stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[39]">TIM8_UP_IRQHandler</a> from stm32f10x_it.o(i.TIM8_UP_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[41]">UART4_IRQHandler</a> from stm32f10x_it.o(i.UART4_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[42]">UART5_IRQHandler</a> from stm32f10x_it.o(i.UART5_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[32]">USART1_IRQHandler</a> from stm32f10x_it.o(i.USART1_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[33]">USART2_IRQHandler</a> from driver.o(i.USART2_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[34]">USART3_IRQHandler</a> from stm32f10x_it.o(i.USART3_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[37]">USBWakeUp_IRQHandler</a> from stm32f10x_it.o(i.USBWakeUp_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[20]">USB_HP_CAN_TX_IRQHandler</a> from stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[21]">USB_LP_CAN_RX0_IRQHandler</a> from stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[8]">UsageFaultException</a> from stm32f10x_it.o(i.UsageFaultException) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[d]">WWDG_IRQHandler</a> from stm32f10x_it.o(i.WWDG_IRQHandler) referenced from stm32f10x_vector.o(RESET)
 <LI><a href="#[4b]">__main</a> from __main.o(!!!main) referenced from stm32f10x_vector.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[4b]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[4c]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[4e]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[73]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[74]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[4f]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[75]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[53]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[76]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[77]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[78]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[79]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[7a]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[7b]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[7c]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[7d]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[7e]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[7f]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[80]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[81]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[82]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[83]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[84]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[85]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[86]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[87]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[88]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[89]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[8a]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[58]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[8b]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[8c]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[8d]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[8e]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[8f]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000003))

<P><STRONG><a name="[90]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B))

<P><STRONG><a name="[4d]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[91]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[50]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[52]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[92]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[54]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; LedInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[93]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[5d]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[57]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[94]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[59]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[3]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_vector.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, stm32f10x_vector.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[95]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[96]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[97]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[98]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[5b]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[99]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[51]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[56]"></a>exit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[5a]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[9a]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[9b]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[9c]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[1f]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.ADC1_2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>ADC3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.ADC3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>BusFaultException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.BusFaultException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = BusFaultException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN_RX1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.CAN_RX1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CAN_RX1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN_SCE_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.CAN_SCE_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CAN_SCE_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>CloclkInit</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, driver.o(i.CloclkInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CloclkInit &rArr; RCC_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SYSCLKConfig
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLConfig
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLCmd
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK2Config
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK1Config
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSEConfig
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HCLKConfig
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetSYSCLKSource
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_DeInit
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayInit
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_SetLatency
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_PrefetchBufferCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6d]"></a>CommonConfig</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, driver.o(i.CommonConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CommonConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[18]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel6_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA1_Channel7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Channel7_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA2_Channel4_5_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DMA2_Channel4_5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Channel4_5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>DebugMonitor</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.DebugMonitor))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DebugMonitor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>DelayInit</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, driver.o(i.DelayInit))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[72]"></a>DelayMs</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, driver.o(i.DelayMs))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, driver.o(i.EXTI15_10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI15_10_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetITStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.EXTI9_5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI9_5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>EXTI_GetITStatus</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f10x_exti.o(i.EXTI_GetITStatus))
<BR><BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[11]"></a>FLASH_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.FLASH_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FLASH_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>FLASH_PrefetchBufferCmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[64]"></a>FLASH_SetLatency</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_SetLatency))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[3d]"></a>FSMC_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.FSMC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FSMC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>GPIO_Init</STRONG> (Thumb, 276 bytes, Stack size 24 bytes, stm32f10x_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedInit
</UL>

<P><STRONG><a name="[5]"></a>HardFaultException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.HardFaultException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HardFaultException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C1_ER_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C1_ER_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C1_EV_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C1_EV_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C2_ER_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C2_ER_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.I2C2_EV_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C2_EV_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>LedInit</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, driver.o(i.LedInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LedInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6]"></a>MemManageException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.MemManageException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MemManageException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>NMIException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.NMIException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NMIException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_nvic.o(i.NVIC_PriorityGroupConfig))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommonConfig
</UL>

<P><STRONG><a name="[e]"></a>PVD_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.PVD_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PVD_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PendSVC</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.PendSVC))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PendSVC
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[5f]"></a>RCC_DeInit</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[62]"></a>RCC_GetFlagStatus</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f10x_rcc.o(i.RCC_GetFlagStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[6a]"></a>RCC_GetSYSCLKSource</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_GetSYSCLKSource))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[65]"></a>RCC_HCLKConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_HCLKConfig))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[60]"></a>RCC_HSEConfig</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_HSEConfig))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[12]"></a>RCC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.RCC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>RCC_PCLK1Config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PCLK1Config))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[66]"></a>RCC_PCLK2Config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PCLK2Config))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[61]"></a>RCC_PLLCmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PLLCmd))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[68]"></a>RCC_PLLConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_PLLConfig))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[69]"></a>RCC_SYSCLKConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_SYSCLKConfig))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>

<P><STRONG><a name="[36]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.RTCAlarm_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RTCAlarm_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RTC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.RTC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RTC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SDIO_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SDIO_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDIO_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SPI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SPI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>SPI3_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SPI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SVCHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SVCHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SVCHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>SysTickHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.SysTickHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTickHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>SystemInit</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, main.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SystemInit
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayMs
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(.text)
</UL>
<P><STRONG><a name="[f]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TAMPER_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TAMPER_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM1_BRK_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_BRK_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM1_CC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_CC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM1_TRG_COM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_TRG_COM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, driver.o(i.TIM1_UP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_UP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM5_IRQHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, driver.o(i.TIM5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIM6_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM6_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM7_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM7_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_BRK_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_BRK_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_CC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_CC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_TRG_COM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_TRG_COM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.TIM8_UP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM8_UP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART4_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.UART4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>UART5_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f10x_it.o(i.UART5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART2_IRQHandler</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, driver.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USBWakeUp_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBWakeUp_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>USB_HP_CAN_TX_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USB_HP_CAN_TX_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_HP_CAN_TX_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>USB_LP_CAN_RX0_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.USB_LP_CAN_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_LP_CAN_RX0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>UsageFaultException</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.UsageFaultException))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UsageFaultException
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>WWDG_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f10x_it.o(i.WWDG_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WWDG_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f10x_vector.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>main</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = main &rArr; LedInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedInit
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayMs
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommonConfig
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CloclkInit
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>
<P>
<H3>
Local Symbols
</H3><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
