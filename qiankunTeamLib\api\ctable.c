//------------------------------------------------------------------
//-------------------- (C) COPYRIGHT <EMAIL>  -----------------
//
//    File Name		ctable.c
//    Description	
//    Date			2018-10-28
//
//-------------------- (C) COPYRIGHT <EMAIL>  -----------------
//------------------------------------------------------------------
#include "fft.h"

short const theta0[8]={0,1,3,7,15,31,63,127,};
short const theta1[7]={2,5,11,23,47,95,191,};
float const ccos[240]={
0.707107,-0.707107,0.923880,0.707107,0.382683,-0.382683,-0.707107,-0.923880,
0.980785,0.923880,0.831470,0.707107,0.555570,0.382683,0.195090,-0.195090,
-0.382683,-0.555570,-0.707107,-0.831470,-0.923880,-0.980785,0.995185,0.980785,
0.956940,0.923880,0.881921,0.831470,0.773010,0.707107,0.634393,0.555570,
0.471397,0.382683,0.290285,0.195090,0.098017,-0.098017,-0.195090,-0.290285,
-0.382683,-0.471397,-0.555570,-0.634393,-0.707107,-0.773010,-0.831470,-0.881921,
-0.923880,-0.956940,-0.980785,-0.995185,0.998795,0.995185,0.989177,0.980785,
0.970031,0.956940,0.941544,0.923880,0.903989,0.881921,0.857729,0.831470,
0.803208,0.773010,0.740951,0.707107,0.671559,0.634393,0.595699,0.555570,
0.514103,0.471397,0.427555,0.382683,0.336890,0.290285,0.242980,0.195090,
0.146730,0.098017,0.049068,-0.049068,-0.098017,-0.146730,-0.195090,-0.242980,
-0.290285,-0.336890,-0.382683,-0.427555,-0.471397,-0.514103,-0.555570,-0.595699,
-0.634393,-0.671559,-0.707107,-0.740951,-0.773010,-0.803208,-0.831470,-0.857729,
-0.881921,-0.903989,-0.923880,-0.941544,-0.956940,-0.970031,-0.980785,-0.989177,
-0.995185,-0.998795,0.999699,0.998795,0.997290,0.995185,0.992480,0.989177,
0.985278,0.980785,0.975702,0.970031,0.963776,0.956940,0.949528,0.941544,
0.932993,0.923880,0.914210,0.903989,0.893224,0.881921,0.870087,0.857729,
0.844854,0.831470,0.817585,0.803208,0.788346,0.773010,0.757209,0.740951,
0.724247,0.707107,0.689541,0.671559,0.653173,0.634393,0.615232,0.595699,
0.575808,0.555570,0.534998,0.514103,0.492898,0.471397,0.449611,0.427555,
0.405241,0.382683,0.359895,0.336890,0.313682,0.290285,0.266713,0.242980,
0.219101,0.195090,0.170962,0.146730,0.122411,0.098017,0.073565,0.049068,
0.024541,-0.024541,-0.049068,-0.073565,-0.098017,-0.122411,-0.146730,-0.170962,
-0.195090,-0.219101,-0.242980,-0.266713,-0.290285,-0.313682,-0.336890,-0.359895,
-0.382683,-0.405241,-0.427555,-0.449611,-0.471397,-0.492898,-0.514103,-0.534998,
-0.555570,-0.575808,-0.595699,-0.615232,-0.634393,-0.653173,-0.671559,-0.689541,
-0.707107,-0.724247,-0.740951,-0.757209,-0.773010,-0.788346,-0.803208,-0.817585,
-0.831470,-0.844854,-0.857729,-0.870087,-0.881921,-0.893224,-0.903989,-0.914210,
-0.923880,-0.932993,-0.941544,-0.949528,-0.956940,-0.963776,-0.970031,-0.975702,
-0.980785,-0.985278,-0.989177,-0.992480,-0.995185,-0.997290,-0.998795,-0.999699,
};
float const csin[240]={
-0.707107,-0.707107,-0.382683,-0.707107,-0.923880,-0.923880,-0.707107,-0.382683,
-0.195090,-0.382683,-0.555570,-0.707107,-0.831470,-0.923880,-0.980785,-0.980785,
-0.923880,-0.831470,-0.707107,-0.555570,-0.382683,-0.195090,-0.098017,-0.195090,
-0.290285,-0.382683,-0.471397,-0.555570,-0.634393,-0.707107,-0.773010,-0.831470,
-0.881921,-0.923880,-0.956940,-0.980785,-0.995185,-0.995185,-0.980785,-0.956940,
-0.923880,-0.881921,-0.831470,-0.773010,-0.707107,-0.634393,-0.555570,-0.471397,
-0.382683,-0.290285,-0.195090,-0.098017,-0.049068,-0.098017,-0.146730,-0.195090,
-0.242980,-0.290285,-0.336890,-0.382683,-0.427555,-0.471397,-0.514103,-0.555570,
-0.595699,-0.634393,-0.671559,-0.707107,-0.740951,-0.773010,-0.803208,-0.831470,
-0.857729,-0.881921,-0.903989,-0.923880,-0.941544,-0.956940,-0.970031,-0.980785,
-0.989177,-0.995185,-0.998795,-0.998795,-0.995185,-0.989177,-0.980785,-0.970031,
-0.956940,-0.941544,-0.923880,-0.903989,-0.881921,-0.857729,-0.831470,-0.803208,
-0.773010,-0.740951,-0.707107,-0.671559,-0.634393,-0.595699,-0.555570,-0.514103,
-0.471397,-0.427555,-0.382683,-0.336890,-0.290285,-0.242980,-0.195090,-0.146730,
-0.098017,-0.049068,-0.024541,-0.049068,-0.073565,-0.098017,-0.122411,-0.146730,
-0.170962,-0.195090,-0.219101,-0.242980,-0.266713,-0.290285,-0.313682,-0.336890,
-0.359895,-0.382683,-0.405241,-0.427555,-0.449611,-0.471397,-0.492898,-0.514103,
-0.534998,-0.555570,-0.575808,-0.595699,-0.615232,-0.634393,-0.653173,-0.671559,
-0.689541,-0.707107,-0.724247,-0.740951,-0.757209,-0.773010,-0.788346,-0.803208,
-0.817585,-0.831470,-0.844854,-0.857729,-0.870087,-0.881921,-0.893224,-0.903989,
-0.914210,-0.923880,-0.932993,-0.941544,-0.949528,-0.956940,-0.963776,-0.970031,
-0.975702,-0.980785,-0.985278,-0.989177,-0.992480,-0.995185,-0.997290,-0.998795,
-0.999699,-0.999699,-0.998795,-0.997290,-0.995185,-0.992480,-0.989177,-0.985278,
-0.980785,-0.975702,-0.970031,-0.963776,-0.956940,-0.949528,-0.941544,-0.932993,
-0.923880,-0.914210,-0.903989,-0.893224,-0.881921,-0.870087,-0.857729,-0.844854,
-0.831470,-0.817585,-0.803208,-0.788346,-0.773010,-0.757209,-0.740951,-0.724247,
-0.707107,-0.689541,-0.671559,-0.653173,-0.634393,-0.615232,-0.595699,-0.575808,
-0.555570,-0.534998,-0.514103,-0.492898,-0.471397,-0.449611,-0.427555,-0.405241,
-0.382683,-0.359895,-0.336890,-0.313682,-0.290285,-0.266713,-0.242980,-0.219101,
-0.195090,-0.170962,-0.146730,-0.122411,-0.098017,-0.073565,-0.049068,-0.024541,
};
